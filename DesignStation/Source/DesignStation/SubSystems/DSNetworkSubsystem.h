// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "IHttpRequest.h"
#include "NetworkData/FileInfoRelativeData.h"
#include "NetworkData/ProjectInfoRelativeData.h"
#include "NetworkData/RoomStyleData.h"
#include "NetworkData/UserInfoRelativeData.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "SubSystems/NetworkData/ResourceStoreParams.h"
#include "UI/Widget/Common/CategoryTree/Structures/CategoryInfo.h"
#include "UI/Widget/Common/ResourceItem/Structures/QueryResourceListParams.h"
#include "UI/Widget/Common/ResourceItem/Structures/ResourceInfo.h"
#include "UI/Widget/MainDesignPage/MaterialPage/Structures/TagFilter.h"
#include "DSNetworkSubsystem.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(DSNetworkSubsystemLog, Log, All);

DECLARE_DYNAMIC_DELEGATE_OneParam(FOnGetRoomStyleCompleteDelegate, const TArray<FRoomStyleData>&, Data);

DECLARE_DYNAMIC_DELEGATE_OneParam(FOnQueryCategoryTreeCompleteDelegate, const TArray<FDSCategoryInfo>&, Categories);

DECLARE_DYNAMIC_DELEGATE_OneParam(FOnQueryTagFilterGroupsCompletedDelegate, const TArray<FDSTagFilterGroup>&, TagFilterGroups);

DECLARE_DYNAMIC_DELEGATE_TwoParams(FOnQueryResourceListCompletedDelegate, const FIntPoint&, InPageInfo, const TArray<FDSResourceInfo>&, ItemList);

DECLARE_DYNAMIC_DELEGATE_OneParam(FOnQueryResourceListByFolderIdsCompletedDelegate, const TArray<FDSResourceInfo>&, ItemList);

DECLARE_DYNAMIC_DELEGATE_OneParam(FOnQueryResourceListByIdsCompletedDelegate, const TArray<FDSResourceInfo>&, ItemList);

DECLARE_DYNAMIC_DELEGATE_OneParam(FOnAddCollectionCompletedDelegate, bool, bSucceed);

DECLARE_DYNAMIC_DELEGATE_OneParam(FOnCancleCollectionCompletedDelegate, bool, bSucceed);

DECLARE_DYNAMIC_DELEGATE_TwoParams(FOnQueryCollectionListCompletedDelegate, const FIntPoint&, InPageInfo, const TArray<FDSCollectionInfo>&, ItemList);

DECLARE_DYNAMIC_DELEGATE_OneParam(FOnQueryCollectionClassificationCompletedDelegate, const TArray<FDSCollectionClassificationInfo>&, ItemList);

DECLARE_DYNAMIC_DELEGATE_OneParam(FOnAddCollectionClassificationCompletedDelegate, bool, bSucceed);

enum class ESearchType : uint8
{
	OnlyFile, OnlyFolder, FileAndFolder
};

enum class EFolderSearchType : uint8
{
	ESearchAll = 0,
	EInteriorFinish = 1,
	//硬装
	ECustom = 2 //定制
};

/**
 *
 */
UCLASS(BlueprintType, Blueprintable)
class DESIGNSTATION_API UDSNetworkSubsystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()

	static UDSNetworkSubsystem* Instance;

	//Fix bug DES-1065用于断网时的超时检测，Key为请求的UUID，值为请求类型与请求时间
	TMap<FString, TPair<FString, float>> RequestList;

	struct FProjectDownloadInfo
	{
		//用于保存正在下载的项目信息
		FProjectInfoData ProjectData;

		FString CustomName;

		TArray<FFileInfoData> DependFiles;

		FProjectDownloadInfo()
			: ProjectData()
			  , CustomName(TEXT(""))
			  , DependFiles(TArray<FFileInfoData>()) {}
	};

	TArray<TPair<FString, TSharedPtr<struct FProjectDownloadInfo>>> ProjectDownloadList;

	UPROPERTY()
	FUserInfoData LoginUserInfo = FUserInfoData(); //登录用户的信息，用户登录后会一直保存当前登录用户的信息

	TPair<FString, FString> TempToken;

public:
	const FString& GetToken() const;

	void setToken(FString ken);

	FHttpRequestPtr CreatePostRequest(const FString& InUrl, const FString& InContent /*= ""*/) const;

	FHttpRequestPtr CreateGetRequest(const FString& InUrl) const;

	FOnQueryCategoryTreeCompleteDelegate OnQueryCategoryTreeCompletedDelegate;

	FOnQueryTagFilterGroupsCompletedDelegate OnQueryTagFilterGroupsCompletedDelegate;

	FOnQueryResourceListCompletedDelegate OnQueryResourceListCompletedDelegate;

	FOnQueryResourceListByFolderIdsCompletedDelegate OnQueryResourceListByFolderIdsCompleteDelegate;

	FOnQueryResourceListByIdsCompletedDelegate OnQueryResourceListByIdsCompletedDelegate;

	FOnGetRoomStyleCompleteDelegate OnGetRoomStyleCompleteDelegate;

	FOnAddCollectionCompletedDelegate OnAddCollectionCompletedDelegate;

	FOnCancleCollectionCompletedDelegate OnCancleCollectionCompletedDelegate;

	FOnQueryCollectionListCompletedDelegate OnQueryCollectionListCompletedDelegate;

	FOnAddCollectionClassificationCompletedDelegate OnAddCollectionClassificationCompletedDelegate;

	FOnQueryCollectionClassificationCompletedDelegate OnQueryCollectionClassificationCompletedDelegate;

	UDSNetworkSubsystem();

	static bool IsInitialized();

	static UDSNetworkSubsystem* GetInstance();

	virtual void Initialize(FSubsystemCollectionBase& Collection) override;

	virtual void Deinitialize() override;

	//左边栏相关接口
	void SendQueryCategoryTreeRequest(const FOnQueryCategoryTreeCompleteDelegate& Callback);

	void SendQueryCategoryTreeByRelativeCodes(const TArray<FString>& RelativeCodes, const FOnQueryCategoryTreeCompleteDelegate& Callback);

	void SendQueryRelativeCategoryTreeByBkId(const FString& QueryParams, const FOnQueryCategoryTreeCompleteDelegate& CallBack);

	void SendQueryTagFilterGroupsRequest(const FOnQueryTagFilterGroupsCompletedDelegate& CallBack);

	void SendQueryResourceListRequest(const FQueryResourceListParams& Params, const FOnQueryResourceListCompletedDelegate& CallBack);

	void SendQueryReplaceResourceListRequest(const FQueryResourceListParams& Params, const FOnQueryResourceListCompletedDelegate& CallBack);

	void SendQueryResourceListByFolderIdsRequest(const TMap<FString, EDSResourceType>& Params, const FOnQueryResourceListByFolderIdsCompletedDelegate& Callback);

	void SendQueryResourceListByIdsRequest(const TMap<FString, EDSResourceType>& Params, const FOnQueryResourceListByIdsCompletedDelegate& CallBack);

	void SendResourceAddStoredRequest(const FResourceAddStoreParams& Params);

	void SendResourceCancelStoredRequest(const FResourceCancelStoredParams& Params);

	void SendQeuryCollectionListRequst(const FQueryCollectionListParams& Params);

	void SendQueryCollectionClassificationListRequest(const FQueryCollectionClassificationParams& Params);

	void SendAddCollectionClassificationRequest(const FDSCollectionClassificationInfo& Params);

	void SendModifyCollectionClassificationRequest(const FDSCollectionClassificationInfo& Params);

	void SendDeleteCollectionClassificationRequest(const FDSCollectionClassificationInfo& Params);

	void SendGetRoomStyleRequest(const FString& GroupValue, const FOnGetRoomStyleCompleteDelegate& CallBack);

private:
	TMap<FString, FOnQueryCategoryTreeCompleteDelegate> OnRequestCategoryTreeCompletedHandlerMap;

	TMap<FString, FOnQueryCategoryTreeCompleteDelegate> OnRequestCategoryTreeByRelativeCodesCompleteHandlerMap;

	TMap<FString, FOnQueryCategoryTreeCompleteDelegate> OnRequestRelativeCategoryTreeCompleteHandlerMap;

	TMap<FString, FOnQueryTagFilterGroupsCompletedDelegate> OnRequestTagFilterGroupsCompletedHandlerMap;

	TMap<FString, FOnQueryResourceListCompletedDelegate> OnRequestResourceListCompletedHandlerMap;

	TMap<FString, FOnQueryResourceListByFolderIdsCompletedDelegate> OnRequestResourceListByFolderIdsCompletedHandlerMap;

	TMap<FString, FOnQueryResourceListByIdsCompletedDelegate> OnRequestResourceListByIdsCompletedHandlerMap;

	TMap<FString, FOnGetRoomStyleCompleteDelegate> OnRequestRoomStyleCompletedHandlerMap;

protected:
	//收藏相关
	void OnResourceAddStoredRequestCompletedHandle(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully) const;

	void OnRequestCancelStoredRequestCompletedHandle(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully) const;

	void OnQueryCollectionListRequestCompletedHandle(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully) const;

	void OnAddCollectionClassificationRequestCompletedHandle(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully) const;

	void OnQueryCollectionClassificationListRequestCompletedHandle(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully) const;

	void OnRequestCategoryTreeByRelativeCodesCompleteHandler(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully);

	void OnRequestCategoryTreeCompletedHandler(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully);

	void OnRequestRelativeCategoryTreeCompleteHandler(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully);

	void OnRequestTagFilterGroupsCompletedHandler(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully);

	void OnRequestResourceListCompletedHandler(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully);

	void OnRequestResourceListByFolderIdsCompletedHandler(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully);

	void OnRequestResourceListByIdsCompletedHandler(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully);

	void OnGetRoomStyleCompletedHandler(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully);

	UFUNCTION()
	void HandleReceivedRefreshTokenMessage(const FString& NewToken);
};
