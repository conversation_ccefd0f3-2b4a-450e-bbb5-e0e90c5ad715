// Fill out your copyright notice in the Description page of Project Settings.

#include "SubSystems/DSNetworkSubsystem.h"

#include "HttpModule.h"
#include "IHttpResponse.h"
#include "JsonReader.h"
#include "JsonSerializer.h"
#include "NetworkConfig.h"
#include "Engine/World.h"
#include "File/DSPipeSubsystem.h"
#include "NetworkData/JsonUtilitiesLibrary.h"
#include "NetworkData/ResourceStoreParams.h"
#include "SubSystems/Library/DesignStationFunctionLibrary.h"

DEFINE_LOG_CATEGORY(DSNetworkSubsystemLog);

UDSNetworkSubsystem* UDSNetworkSubsystem::Instance = nullptr;

UDSNetworkSubsystem::UDSNetworkSubsystem()
{
	Instance = this;
}

bool UDSNetworkSubsystem::IsInitialized()
{
	return Instance != nullptr;
}

UDSNetworkSubsystem* UDSNetworkSubsystem::GetInstance()
{
	return Instance;
}

void UDSNetworkSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
	UGameInstanceSubsystem::Initialize(Collection);

	Collection.InitializeDependency(UDSPipeSubsystem::StaticClass());

	UDSPipeSubsystem::GetInstance()->OnReceivedRefreshTokenMessageEvent().AddDynamic(this, &ThisClass::HandleReceivedRefreshTokenMessage);

	TempToken.Key = "Authorization";
	TempToken.Value = TEXT("");

	if (FParse::Value(FCommandLine::Get(), TEXT("Token="), TempToken.Value))
	{
		TempToken.Value = TEXT("Bearer ") + TempToken.Value;
	}
	else
	{
		FFileHelper::LoadFileToString(TempToken.Value, *FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("TestToken.txt")));
	}
}

FHttpRequestPtr UDSNetworkSubsystem::CreatePostRequest(const FString& InUrl, const FString& InContent /*= ""*/) const
{
	FHttpRequestPtr HttpRequestHandle = FHttpModule::Get().CreateRequest();
	HttpRequestHandle->SetURL(InUrl);
	HttpRequestHandle->SetVerb(TEXT("POST"));
	HttpRequestHandle->SetContentAsString(InContent);

	HttpRequestHandle->SetHeader(TEXT("Content-Type"), TEXT("application/json"));
	HttpRequestHandle->SetHeader(TEXT("Authorization"), TempToken.Value);

	return HttpRequestHandle;
}

FHttpRequestPtr UDSNetworkSubsystem::CreateGetRequest(const FString& InUrl) const
{
	FHttpRequestPtr HttpRequestHandle = FHttpModule::Get().CreateRequest();
	HttpRequestHandle->SetURL(InUrl);
	HttpRequestHandle->SetVerb(TEXT("GET"));

	HttpRequestHandle->SetHeader(TEXT("Content-Type"), TEXT("application/json"));
	HttpRequestHandle->SetHeader(TEXT("Authorization"), TempToken.Value);

	return HttpRequestHandle;
}

void UDSNetworkSubsystem::Deinitialize()
{
	UGameInstanceSubsystem::Deinitialize();

	Instance = nullptr;
}

const FString& UDSNetworkSubsystem::GetToken() const
{
	return TempToken.Value;
}

void UDSNetworkSubsystem::setToken(FString ken)
{
	TempToken.Value = ken;
}

void UDSNetworkSubsystem::SendQueryCategoryTreeRequest(const FOnQueryCategoryTreeCompleteDelegate& Callback)
{
	FString RequestID = FGuid::NewGuid().ToString();
	OnRequestCategoryTreeCompletedHandlerMap.Add(RequestID, Callback);

	FString URL = FString::Printf(TEXT("%s/bk-design/api/store/getCateList"), SERVER_URL);

	FHttpRequestPtr HttpRequestHandle = GetInstance()->CreatePostRequest(URL, TEXT(""));
	HttpRequestHandle->SetHeader(TEXT("X-Request-ID"), RequestID);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnRequestCategoryTreeCompletedHandler);
	HttpRequestHandle->ProcessRequest();
}

void UDSNetworkSubsystem::OnRequestCategoryTreeCompletedHandler(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully)
{
	FString RequestID = Request->GetHeader(TEXT("X-Request-ID"));

	FOnQueryCategoryTreeCompleteDelegate* Callback = OnRequestCategoryTreeCompletedHandlerMap.Find(RequestID);
	if (!Callback)
	{
		UE_LOG(DSNetworkSubsystemLog, Warning, TEXT("%s : No callback found for request ID %s"), __FUNCTIONW__, *RequestID);
		return;
	}

	ON_SCOPE_EXIT
	{
		OnRequestCategoryTreeCompletedHandlerMap.Remove(RequestID);
	};

	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		return;
	}

	TSharedPtr<FJsonObject> ResponseObj;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response->GetContentAsString());
	if (!FJsonSerializer::Deserialize(Reader, ResponseObj))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Can not parse the response data as json."), __FUNCTIONW__);
		return;
	}

	if (!ResponseObj->GetBoolField(TEXT("success")))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : The success field is false, msg is '%s'."), __FUNCTIONW__, *ResponseObj->GetStringField(TEXT("msg")));
		return;
	}

	TSharedPtr<FJsonObject> DataObj = ResponseObj->GetObjectField(TEXT("resp"));
	if (!DataObj)
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Not found the resp field from response json."), __FUNCTIONW__);
		return;
	}

	TArray<FDSCategoryInfo> CategoryTrees = {
		FDSCategoryInfo(EDSCategoryType::Model, -1, TEXT("Model")),
		FDSCategoryInfo(EDSCategoryType::Material, -1, TEXT("Surface")),
		FDSCategoryInfo(EDSCategoryType::Custom, -1, TEXT("Custom"))
	};

	const TArray<TSharedPtr<FJsonValue>>& ModelList = DataObj->GetArrayField(TEXT("modelList"));
	const TArray<TSharedPtr<FJsonValue>>& MaterialList = DataObj->GetArrayField(TEXT("materialList"));
	const TArray<TSharedPtr<FJsonValue>>& CustomList = DataObj->GetArrayField(TEXT("customizeList"));

	for (const TSharedPtr<FJsonValue>& ModelValue : ModelList)
	{
		const TSharedPtr<FJsonObject>& ModelObj = ModelValue->AsObject();
		if (ModelObj)
		{
			TSharedPtr<FDSCategoryInfo> NewChild = MakeShared<FDSCategoryInfo>();
			FDSCategoryInfo::DeserializeFromJson(NewChild, ModelObj);
			CategoryTrees[0].Children.Add(NewChild);
		}
	}

	for (const TSharedPtr<FJsonValue>& MaterialValue : MaterialList)
	{
		const TSharedPtr<FJsonObject>& MaterialObj = MaterialValue->AsObject();
		if (MaterialObj)
		{
			TSharedPtr<FDSCategoryInfo> NewChild = MakeShared<FDSCategoryInfo>();
			FDSCategoryInfo::DeserializeFromJson(NewChild, MaterialObj);
			CategoryTrees[1].Children.Add(NewChild);
		}
	}

	for (const TSharedPtr<FJsonValue>& MaterialValue : CustomList)
	{
		const TSharedPtr<FJsonObject>& CustomObj = MaterialValue->AsObject();
		if (CustomObj)
		{
			TSharedPtr<FDSCategoryInfo> NewChild = MakeShared<FDSCategoryInfo>();
			FDSCategoryInfo::DeserializeFromJson(NewChild, CustomObj);
			CategoryTrees[2].Children.Add(NewChild);
		}
	}

	if (Callback->IsBound())
	{
		Callback->Execute(CategoryTrees);
	}
}

void UDSNetworkSubsystem::SendQueryCategoryTreeByRelativeCodes(const TArray<FString>& RelativeCodes, const FOnQueryCategoryTreeCompleteDelegate& CallBack)
{
	FString RequestID = FGuid::NewGuid().ToString();
	OnRequestCategoryTreeByRelativeCodesCompleteHandlerMap.Add(RequestID, CallBack);

	FString QueryParamsStr;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&QueryParamsStr);

	Writer->WriteArrayStart();
	for (int32 Index = 0; Index < RelativeCodes.Num(); ++Index)
	{
		Writer->WriteValue(RelativeCodes[Index]);
	}
	Writer->WriteArrayEnd();

	Writer->Close();

	FString URL = FString::Printf(TEXT("%s/bk-design/api/store/getCateListByCode"), SERVER_URL);

	FHttpRequestPtr HttpRequestHandle = GetInstance()->CreatePostRequest(URL, QueryParamsStr);
	HttpRequestHandle->SetHeader(TEXT("X-Request-ID"), RequestID);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnRequestCategoryTreeByRelativeCodesCompleteHandler);
	HttpRequestHandle->ProcessRequest();
}

void UDSNetworkSubsystem::OnRequestCategoryTreeByRelativeCodesCompleteHandler(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully)
{
	FString RequestID = Request->GetHeader(TEXT("X-Request-ID"));

	FOnQueryCategoryTreeCompleteDelegate* Callback = OnRequestCategoryTreeByRelativeCodesCompleteHandlerMap.Find(RequestID);
	if (!Callback)
	{
		UE_LOG(DSNetworkSubsystemLog, Warning, TEXT("%s : No callback found for request ID %s"), __FUNCTIONW__, *RequestID);
		return;
	}

	ON_SCOPE_EXIT
	{
		OnRequestCategoryTreeByRelativeCodesCompleteHandlerMap.Remove(RequestID);
	};

	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		return;
	}

	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		return;
	}

	TSharedPtr<FJsonObject> ResponseObj;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response->GetContentAsString());
	if (!FJsonSerializer::Deserialize(Reader, ResponseObj))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Can not parse the response data as json."), __FUNCTIONW__);
		return;
	}

	if (!ResponseObj->GetBoolField(TEXT("success")))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : The success field is false, msg is '%s'."), __FUNCTIONW__, *ResponseObj->GetStringField(TEXT("msg")));
		return;
	}

	if (!ResponseObj->HasTypedField<EJson::Array>(TEXT("resp")))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Can not find 'resp' field."), __FUNCTIONW__);
		return;
	}

	TArray<TSharedPtr<FDSCategoryInfo>> CategoryTree;

	const TArray<TSharedPtr<FJsonValue>>& RespValues = ResponseObj->GetArrayField(TEXT("resp"));
	for (const TSharedPtr<FJsonValue>& RespValue : RespValues)
	{
		const TSharedPtr<FJsonObject>& RespObj = RespValue->AsObject();
		if (!RespObj)
		{
			continue;
		}

		TSharedPtr<FDSCategoryInfo>& NewCategory = CategoryTree.AddDefaulted_GetRef();
		NewCategory = MakeShared<FDSCategoryInfo>();
		FDSCategoryInfo::DeserializeFromJson(NewCategory, RespObj);
	}

	TArray<FDSCategoryInfo> CategoriesForDelegate;

	for (const TSharedPtr<FDSCategoryInfo>& CategoryPtr : CategoryTree)
	{
		if (CategoryPtr.IsValid())
		{
			CategoriesForDelegate.Add(*CategoryPtr);
		}
	}

	if (Callback->IsBound())
	{
		Callback->Execute(CategoriesForDelegate);
	}
}

void UDSNetworkSubsystem::SendQueryRelativeCategoryTreeByBkId(const FString& QueryParams, const FOnQueryCategoryTreeCompleteDelegate& CallBack)
{
	FString RequestID = FGuid::NewGuid().ToString();
	OnRequestRelativeCategoryTreeCompleteHandlerMap.Add(RequestID, CallBack);

	FString URL = FString::Printf(TEXT("%s/bk-design/api/store/getCateListByBkId"), SERVER_URL);

	FHttpRequestPtr HttpRequestHandle = GetInstance()->CreatePostRequest(URL, QueryParams);
	HttpRequestHandle->SetHeader(TEXT("X-Request-ID"), RequestID);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnRequestRelativeCategoryTreeCompleteHandler);
	HttpRequestHandle->ProcessRequest();
}

void UDSNetworkSubsystem::OnRequestRelativeCategoryTreeCompleteHandler(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully)
{
	FString RequestID = Request->GetHeader(TEXT("X-Request-ID"));

	const FOnQueryCategoryTreeCompleteDelegate* Callback = OnRequestRelativeCategoryTreeCompleteHandlerMap.Find(RequestID);
	if (!Callback)
	{
		UE_LOG(DSNetworkSubsystemLog, Warning, TEXT("%s : No callback found for request ID %s"), __FUNCTIONW__, *RequestID);
		return;
	}

	ON_SCOPE_EXIT
	{
		OnRequestRelativeCategoryTreeCompleteHandlerMap.Remove(RequestID);
	};

	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		return;
	}

	TSharedPtr<FJsonObject> ResponseObj;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response->GetContentAsString());
	if (!FJsonSerializer::Deserialize(Reader, ResponseObj))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Can not parse the response data as json."), __FUNCTIONW__);
		return;
	}

	if (!ResponseObj->GetBoolField(TEXT("success")))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : The success field is false, msg is '%s'."), __FUNCTIONW__, *ResponseObj->GetStringField(TEXT("msg")));
		return;
	}

	if (!ResponseObj->HasTypedField<EJson::Array>(TEXT("resp")))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Can not find 'resp' field."), __FUNCTIONW__);
		return;
	}

	TArray<TSharedPtr<FDSCategoryInfo>> CategoryTree;

	const TArray<TSharedPtr<FJsonValue>>& RespValues = ResponseObj->GetArrayField(TEXT("resp"));
	for (const TSharedPtr<FJsonValue>& RespValue : RespValues)
	{
		const TSharedPtr<FJsonObject>& RespObj = RespValue->AsObject();
		if (!RespObj)
		{
			continue;
		}

		TSharedPtr<FDSCategoryInfo>& NewCategory = CategoryTree.AddDefaulted_GetRef();
		NewCategory = MakeShared<FDSCategoryInfo>();
		FDSCategoryInfo::DeserializeFromJson(NewCategory, RespObj);
	}

	TArray<FDSCategoryInfo> CategoriesForDelegate;
	for (const TSharedPtr<FDSCategoryInfo>& CategoryPtr : CategoryTree)
	{
		if (CategoryPtr.IsValid())
		{
			CategoriesForDelegate.Add(*CategoryPtr);
		}
	}

	if (Callback->IsBound())
	{
		Callback->Execute(CategoriesForDelegate);
	}
}

void UDSNetworkSubsystem::SendQueryTagFilterGroupsRequest(const FOnQueryTagFilterGroupsCompletedDelegate& CallBack)
{
	FString requestId = FGuid::NewGuid().ToString();
	OnRequestTagFilterGroupsCompletedHandlerMap.Add(requestId, CallBack);

	FString URL = FString::Printf(TEXT("%s/bk-design/api/store/getLabelList"), SERVER_URL);

	FHttpRequestPtr HttpRequestHandle = GetInstance()->CreatePostRequest(URL, TEXT(""));
	HttpRequestHandle->SetHeader(TEXT("X-Request-ID"), requestId);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnRequestTagFilterGroupsCompletedHandler);
	HttpRequestHandle->ProcessRequest();
}

void UDSNetworkSubsystem::OnRequestTagFilterGroupsCompletedHandler(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully)
{
	FString RequestID = Request->GetHeader(TEXT("X-Request-ID"));

	FOnQueryTagFilterGroupsCompletedDelegate* Callback = OnRequestTagFilterGroupsCompletedHandlerMap.Find(RequestID);
	if (!Callback)
	{
		UE_LOG(DSNetworkSubsystemLog, Warning, TEXT("%s : No callback found for request ID %s"), __FUNCTIONW__, *RequestID);
		return;
	}

	ON_SCOPE_EXIT
	{
		OnRequestTagFilterGroupsCompletedHandlerMap.Remove(RequestID);
	};

	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		return;
	}

	TSharedPtr<FJsonObject> ResponseObj;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response->GetContentAsString());
	if (!FJsonSerializer::Deserialize(Reader, ResponseObj))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Can not parse the response data as json."), __FUNCTIONW__);
		return;
	}

	if (!ResponseObj->GetBoolField(TEXT("success")))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : The success field is false, msg is '%s'."), __FUNCTIONW__, *ResponseObj->GetStringField(TEXT("msg")));
		return;
	}

	const TArray<TSharedPtr<FJsonValue>>& GroupList = ResponseObj->GetArrayField(TEXT("resp"));
	if (GroupList.IsEmpty())
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Not found the resp field from response json."), __FUNCTIONW__);
		return;
	}

	TArray<FDSTagFilterGroup> TagFilterGroups;

	for (const TSharedPtr<FJsonValue>& GroupValue : GroupList)
	{
		const TSharedPtr<FJsonObject>& GroupObj = GroupValue->AsObject();
		if (!GroupObj)
		{
			continue;
		}

		FDSTagFilterGroup& NewGroup = TagFilterGroups.AddDefaulted_GetRef();
		NewGroup.GroupCode = GroupObj->GetStringField(TEXT("id"));
		NewGroup.GroupName = GroupObj->GetStringField(TEXT("groupLabelName"));

		const TArray<TSharedPtr<FJsonValue>>& TagList = GroupObj->GetArrayField(TEXT("labelList"));
		for (const TSharedPtr<FJsonValue>& TagValue : TagList)
		{
			const TSharedPtr<FJsonObject>& TagObj = TagValue->AsObject();
			if (!TagObj)
			{
				continue;
			}

			FDSTagFilterInfo& NewTag = NewGroup.Tags.AddDefaulted_GetRef();
			NewTag.Id = TagObj->GetIntegerField(TEXT("id"));
			NewTag.Name = TagObj->GetStringField(TEXT("name"));
		}
	}

	if (Callback->IsBound())
	{
		Callback->Execute(TagFilterGroups);
	}
}

void UDSNetworkSubsystem::SendQueryResourceListRequest(const FQueryResourceListParams& Params, const FOnQueryResourceListCompletedDelegate& CallBack)
{
	FString RequestID = FGuid::NewGuid().ToString();
	OnRequestResourceListCompletedHandlerMap.Add(RequestID, CallBack);

	TArray<TSharedPtr<FJsonValue>> TagIdValues;
	for (const int32& Tag : Params.TagFilters)
	{
		TagIdValues.Add(MakeShared<FJsonValueNumber>(Tag));
	}

	TSharedPtr<FJsonObject> QueryParams = MakeShared<FJsonObject>();
	QueryParams->SetNumberField(TEXT("cateId"), Params.CategoryId);
	QueryParams->SetArrayField(TEXT("labelId"), TagIdValues);
	QueryParams->SetNumberField(TEXT("pageNumber"), Params.PageNumber);
	QueryParams->SetNumberField(TEXT("pageSize"), Params.PageSize);
	QueryParams->SetStringField(TEXT("qryParam"), Params.SearchText);
	QueryParams->SetNumberField(TEXT("sort"), static_cast<int32>(Params.TimeFilter));
	QueryParams->SetStringField(TEXT("styleName"), Params.StyleName);

	int32 QueryType = static_cast<int32>(Params.Type);
	QueryParams->SetNumberField(TEXT("type"), QueryType);

	FString QueryParamsStr;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&QueryParamsStr);
	if (!FJsonSerializer::Serialize(QueryParams.ToSharedRef(), Writer))
	{
		UE_LOG(DSNetworkSubsystemLog, Warning, TEXT("%s - Serialize json into string failed."), __FUNCTIONW__);
		return;
	}

	FString URL = FString::Printf(TEXT("%s/bk-design/api/store/getListByScreen"), SERVER_URL);

	FHttpRequestPtr HttpRequestHandle = CreatePostRequest(URL, QueryParamsStr);
	HttpRequestHandle->SetHeader(TEXT("X-Request-ID"), RequestID);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnRequestResourceListCompletedHandler);
	HttpRequestHandle->ProcessRequest();
}

void UDSNetworkSubsystem::OnRequestResourceListCompletedHandler(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully)
{
	FString RequestID = Request->GetHeader(TEXT("X-Request-ID"));

	FOnQueryResourceListCompletedDelegate* Callback = OnRequestResourceListCompletedHandlerMap.Find(RequestID);
	if (!Callback)
	{
		UE_LOG(DSNetworkSubsystemLog, Warning, TEXT("%s : No callback found for request ID %s"), __FUNCTIONW__, *RequestID);
		return;
	}

	ON_SCOPE_EXIT
	{
		OnRequestResourceListCompletedHandlerMap.Remove(RequestID);
	};

	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		return;
	}

	TSharedPtr<FJsonObject> ResponseObj;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response->GetContentAsString());
	if (!FJsonSerializer::Deserialize(Reader, ResponseObj))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Can not parse the response data as json."), __FUNCTIONW__);
		return;
	}

	if (!ResponseObj->GetBoolField(TEXT("success")))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : The success field is false, msg is '%s'."), __FUNCTIONW__, *ResponseObj->GetStringField(TEXT("msg")));
		return;
	}

	const TSharedPtr<FJsonObject>& RespObj = ResponseObj->GetObjectField(TEXT("resp"));
	if (!RespObj || !RespObj->HasTypedField<EJson::Array>(TEXT("list")))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Not found 'resp' or 'list' field."), __FUNCTIONW__);
		return;
	}

	TArray<FDSResourceInfo> ItemList;
	UDesignStationFunctionLibrary::ParseJsonToResourceList(RespObj->GetArrayField(TEXT("list")), ItemList);

	if (Callback->IsBound())
	{
		FIntPoint PageInfo;
		PageInfo.X = RespObj->GetIntegerField(TEXT("pageNum"));
		PageInfo.Y = RespObj->GetIntegerField(TEXT("pages"));

		Callback->Execute(PageInfo, ItemList);
	}
}

void UDSNetworkSubsystem::SendQueryReplaceResourceListRequest(const FQueryResourceListParams& Params, const FOnQueryResourceListCompletedDelegate& CallBack)
{
	FString RequestID = FGuid::NewGuid().ToString();
	OnRequestResourceListCompletedHandlerMap.Add(RequestID, CallBack);

	TArray<TSharedPtr<FJsonValue>> TagIdValues;
	for (const int32& Tag : Params.TagFilters)
	{
		TagIdValues.Add(MakeShared<FJsonValueNumber>(Tag));
	}

	TSharedPtr<FJsonObject> QueryParams = MakeShared<FJsonObject>();
	QueryParams->SetNumberField(TEXT("cateId"), Params.CategoryId);
	QueryParams->SetArrayField(TEXT("labelId"), TagIdValues);
	QueryParams->SetNumberField(TEXT("pageNumber"), Params.PageNumber);
	QueryParams->SetNumberField(TEXT("pageSize"), Params.PageSize);
	QueryParams->SetStringField(TEXT("qryParam"), Params.SearchText);
	QueryParams->SetNumberField(TEXT("sort"), static_cast<int32>(Params.TimeFilter));
	QueryParams->SetStringField(TEXT("styleName"), Params.StyleName);
	QueryParams->SetStringField(TEXT("selectId"), Params.SelectId);

	int32 QueryType = static_cast<int32>(Params.Type);
	QueryParams->SetNumberField(TEXT("type"), QueryType);

	FString QueryParamsStr;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&QueryParamsStr);
	if (!FJsonSerializer::Serialize(QueryParams.ToSharedRef(), Writer))
	{
		UE_LOG(DSNetworkSubsystemLog, Warning, TEXT("%s - Serialize json into string failed."), __FUNCTIONW__);
		return;
	}

	FString URL = FString::Printf(TEXT("%s/bk-design/api/store/replaceGoods"), SERVER_URL);

	FHttpRequestPtr HttpRequestHandle = GetInstance()->CreatePostRequest(URL, QueryParamsStr);
	HttpRequestHandle->SetHeader(TEXT("X-Request-ID"), RequestID);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnRequestResourceListCompletedHandler);
	HttpRequestHandle->ProcessRequest();
}

void UDSNetworkSubsystem::SendQueryResourceListByFolderIdsRequest(const TMap<FString, EDSResourceType>& Params, const FOnQueryResourceListByFolderIdsCompletedDelegate& Callback)
{
	FString RequestID = FGuid::NewGuid().ToString();
	OnRequestResourceListByFolderIdsCompletedHandlerMap.Add(RequestID, Callback);

	TMap<EDSResourceType, TArray<TSharedPtr<FJsonValue>>> IdsMap;

	for (const TPair<FString, EDSResourceType>& Pair : Params)
	{
		if (!IdsMap.Contains(Pair.Value))
		{
			IdsMap.Add(Pair.Value, {});
		}

		IdsMap[Pair.Value].Add(MakeShared<FJsonValueString>(Pair.Key));
	}

	TMap<EDSResourceType, TSharedPtr<FJsonValue>> ParamObjMap;
	for (const TPair<EDSResourceType, TArray<TSharedPtr<FJsonValue>>>& Pair : IdsMap)
	{
		if (!ParamObjMap.Contains(Pair.Key))
		{
			ParamObjMap.Add(Pair.Key, MakeShared<FJsonValueObject>(MakeShared<FJsonObject>()));
		}

		const TSharedPtr<FJsonObject>& CurrentObj = ParamObjMap[Pair.Key]->AsObject();

		CurrentObj->SetNumberField(TEXT("type"), static_cast<int32>(Pair.Key));
		CurrentObj->SetArrayField(TEXT("folderIds"), Pair.Value);
	}

	TArray<TSharedPtr<FJsonValue>> ParamObjArray;
	ParamObjMap.GenerateValueArray(ParamObjArray);

	FString QueryParamStr;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&QueryParamStr);
	if (!FJsonSerializer::Serialize(ParamObjArray, Writer))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s - Convert params to json failure."), __FUNCTIONW__);
		return;
	}

	FString URL = FString::Printf(TEXT("%s/bk-design/api/store/getListByFolderId"), SERVER_URL);

	FHttpRequestPtr HttpRequestHandle = CreatePostRequest(URL, QueryParamStr);
	HttpRequestHandle->SetHeader(TEXT("X-Request-ID"), RequestID);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnRequestResourceListByFolderIdsCompletedHandler);
	HttpRequestHandle->ProcessRequest();
}

void UDSNetworkSubsystem::OnRequestResourceListByFolderIdsCompletedHandler(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully)
{
	FString RequestID = Request->GetHeader(TEXT("X-Request-ID"));

	FOnQueryResourceListByFolderIdsCompletedDelegate* Callback = OnRequestResourceListByFolderIdsCompletedHandlerMap.Find(RequestID);
	if (!Callback)
	{
		UE_LOG(DSNetworkSubsystemLog, Warning, TEXT("%s : No callback found for request ID %s"), __FUNCTIONW__, *RequestID);
		return;
	}

	ON_SCOPE_EXIT
	{
		OnRequestResourceListByFolderIdsCompletedHandlerMap.Remove(RequestID);
	};

	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		return;
	}

	TSharedPtr<FJsonObject> ResponseObj;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response->GetContentAsString());
	if (!FJsonSerializer::Deserialize(Reader, ResponseObj))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Can not parse the response data as json."), __FUNCTIONW__);
		return;
	}

	if (!ResponseObj->GetBoolField(TEXT("success")))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : The success field is false, msg is '%s'."), __FUNCTIONW__, *ResponseObj->GetStringField(TEXT("msg")));
		return;
	}

	if (!ResponseObj->HasTypedField<EJson::Array>(TEXT("resp")))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Not found 'resp' field."), __FUNCTIONW__);
		return;
	}

	TArray<FDSResourceInfo> ItemList;
	UDesignStationFunctionLibrary::ParseJsonToResourceList(ResponseObj->GetArrayField(TEXT("resp")), ItemList);

	if (Callback->IsBound())
	{
		Callback->Execute(ItemList);
	}
}

void UDSNetworkSubsystem::SendQueryResourceListByIdsRequest(const TMap<FString, EDSResourceType>& Params, const FOnQueryResourceListByIdsCompletedDelegate& CallBack)
{
	FString RequestID = FGuid::NewGuid().ToString();
	OnRequestResourceListByIdsCompletedHandlerMap.Add(RequestID, CallBack);

	TMap<EDSResourceType, TArray<TSharedPtr<FJsonValue>>> IdsMap;

	for (const TPair<FString, EDSResourceType>& Pair : Params)
	{
		if (!IdsMap.Contains(Pair.Value))
		{
			IdsMap.Add(Pair.Value, {});
		}

		IdsMap[Pair.Value].Add(MakeShared<FJsonValueString>(Pair.Key));
	}

	TMap<EDSResourceType, TSharedPtr<FJsonValue>> ParamObjMap;
	for (const TPair<EDSResourceType, TArray<TSharedPtr<FJsonValue>>>& Pair : IdsMap)
	{
		if (!ParamObjMap.Contains(Pair.Key))
		{
			ParamObjMap.Add(Pair.Key, MakeShared<FJsonValueObject>(MakeShared<FJsonObject>()));
		}

		const TSharedPtr<FJsonObject>& CurrentObj = ParamObjMap[Pair.Key]->AsObject();

		CurrentObj->SetNumberField(TEXT("type"), static_cast<int32>(Pair.Key));
		CurrentObj->SetArrayField(TEXT("ids"), Pair.Value);
	}

	TArray<TSharedPtr<FJsonValue>> ParamObjArray;
	ParamObjMap.GenerateValueArray(ParamObjArray);

	FString QueryParamStr;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&QueryParamStr);
	if (!FJsonSerializer::Serialize(ParamObjArray, Writer))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s - Convert params to json failure."), __FUNCTIONW__);
		return;
	}

	FString URL = FString::Printf(TEXT("%s/bk-design/api/store/getListById"), SERVER_URL);

	FHttpRequestPtr HttpRequestHandle = GetInstance()->CreatePostRequest(URL, QueryParamStr);
	HttpRequestHandle->SetHeader(TEXT("X-Request-ID"), RequestID);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnRequestResourceListByIdsCompletedHandler);
	HttpRequestHandle->ProcessRequest();
}

void UDSNetworkSubsystem::OnRequestResourceListByIdsCompletedHandler(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully)
{
	FString RequestID = Request->GetHeader(TEXT("X-Request-ID"));

	FOnQueryResourceListByIdsCompletedDelegate* Callback = OnRequestResourceListByIdsCompletedHandlerMap.Find(RequestID);
	if (!Callback)
	{
		UE_LOG(DSNetworkSubsystemLog, Warning, TEXT("%s : No callback found for request ID %s"), __FUNCTIONW__, *RequestID);
		return;
	}

	ON_SCOPE_EXIT
	{
		OnRequestResourceListByIdsCompletedHandlerMap.Remove(RequestID);
	};

	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		return;
	}

	TSharedPtr<FJsonObject> ResponseObj;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response->GetContentAsString());
	if (!FJsonSerializer::Deserialize(Reader, ResponseObj))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Can not parse the response data as json."), __FUNCTIONW__);
		return;
	}

	if (!ResponseObj->GetBoolField(TEXT("success")))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : The success field is false, msg is '%s'."), __FUNCTIONW__, *ResponseObj->GetStringField(TEXT("msg")));
		return;
	}

	if (!ResponseObj->HasTypedField<EJson::Array>(TEXT("resp")))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Not found 'resp' field."), __FUNCTIONW__);
		return;
	}

	TArray<FDSResourceInfo> ItemList;
	UDesignStationFunctionLibrary::ParseJsonToResourceList(ResponseObj->GetArrayField(TEXT("resp")), ItemList);

	if (Callback->IsBound())
	{
		Callback->Execute(ItemList);
	}
}

void UDSNetworkSubsystem::SendResourceAddStoredRequest(const FResourceAddStoreParams& Params)
{
	TSharedPtr<FJsonObject> RequestParams = MakeShared<FJsonObject>();
	RequestParams->SetNumberField(TEXT("type"), static_cast<int32>(Params.Type));
	RequestParams->SetStringField(TEXT("depth"), Params.Depth);
	RequestParams->SetStringField(TEXT("height"), Params.Height);
	RequestParams->SetStringField(TEXT("width"), Params.Width);
	RequestParams->SetStringField(TEXT("resourceName"), Params.Name);
	RequestParams->SetStringField(TEXT("resourceId"), Params.CollectionID);
	RequestParams->SetNumberField(TEXT("resourceType"), static_cast<int32>(Params.CollectionType));
	RequestParams->SetNumberField(TEXT("groupId"), Params.ClassificationId);
	RequestParams->SetStringField(TEXT("filePath"), Params.ResourcePath);
	RequestParams->SetStringField(TEXT("productImg"), Params.ProductImg);

	if (!Params.ResourceList.IsEmpty())
	{
		TArray<TSharedPtr<FJsonValue>> resourceListJsonValues;
		for (auto& Iter : Params.ResourceList)
		{
			TSharedPtr<FJsonObject> resourceJsonObj = MakeShared<FJsonObject>();
			resourceJsonObj->SetStringField(TEXT("resourceId"), Iter.Key);
			resourceJsonObj->SetNumberField(TEXT("type"), static_cast<int32>(Iter.Value));
			resourceListJsonValues.Add(MakeShared<FJsonValueObject>(resourceJsonObj));
		}
		RequestParams->SetArrayField(TEXT("resourceList"), resourceListJsonValues);
	}

	TArray<TSharedPtr<FJsonValue>> ParamObjArray;
	ParamObjArray.Add(MakeShared<FJsonValueObject>(RequestParams));

	FString QueryParamsStr;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&QueryParamsStr);
	if (!FJsonSerializer::Serialize(ParamObjArray, Writer))
	{
		UE_LOG(DSNetworkSubsystemLog, Warning, TEXT("%s - Serialize json into string failed."), __FUNCTIONW__);
		return;
	}
	FString URL = FString::Printf(TEXT("%s/bk-design/api/bkGroupCollect/collectData"), SERVER_URL);

	FHttpRequestPtr HttpRequestHandle = GetInstance()->CreatePostRequest(URL, QueryParamsStr);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnResourceAddStoredRequestCompletedHandle);
	HttpRequestHandle->ProcessRequest();
}

void UDSNetworkSubsystem::OnResourceAddStoredRequestCompletedHandle(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully) const
{
	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		return;
	}

	TSharedPtr<FJsonObject> ResponseObj;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response->GetContentAsString());

	if (!FJsonSerializer::Deserialize(Reader, ResponseObj))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Can not parse the response data as json."), __FUNCTIONW__);
		return;
	}
	bool bSucceed = ResponseObj->GetBoolField(TEXT("success"));

	if (OnAddCollectionCompletedDelegate.IsBound())
	{
		OnAddCollectionCompletedDelegate.Execute(bSucceed);
	}
}

void UDSNetworkSubsystem::SendResourceCancelStoredRequest(const FResourceCancelStoredParams& Params)
{
	TSharedPtr<FJsonObject> RequestParams = MakeShared<FJsonObject>();
	RequestParams->SetNumberField(TEXT("type"), static_cast<int32>(Params.Type));
	//RequestParams->SetNumberField(TEXT("brandId"), Params.Brand);
	if (Params.CollectionID.IsEmpty())
	{
		RequestParams->SetNumberField(TEXT("id"), Params.Id);
	}
	else
	{
		RequestParams->SetStringField(TEXT("resourceId"), Params.CollectionID);
	}
	FString QueryParamsStr;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&QueryParamsStr);
	if (!FJsonSerializer::Serialize(RequestParams.ToSharedRef(), Writer))
	{
		UE_LOG(DSNetworkSubsystemLog, Warning, TEXT("%s - Serialize json into string failed."), __FUNCTIONW__);
		return;
	}
	FString URL = FString::Printf(TEXT("%s/bk-design/api/bkGroupCollect/cancelCollectData"), SERVER_URL);

	FHttpRequestPtr HttpRequestHandle = GetInstance()->CreatePostRequest(URL, QueryParamsStr);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnRequestCancelStoredRequestCompletedHandle);
	HttpRequestHandle->ProcessRequest();
}

void UDSNetworkSubsystem::OnRequestCancelStoredRequestCompletedHandle(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully) const
{
	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		return;
	}

	TSharedPtr<FJsonObject> ResponseObj;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response->GetContentAsString());
	if (!FJsonSerializer::Deserialize(Reader, ResponseObj))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Can not parse the response data as json."), __FUNCTIONW__);
		return;
	}
	bool bSucceed = ResponseObj->GetBoolField(TEXT("success"));

	if (OnCancleCollectionCompletedDelegate.IsBound())
	{
		OnCancleCollectionCompletedDelegate.Execute(bSucceed);
	}
}

void UDSNetworkSubsystem::SendQeuryCollectionListRequst(const FQueryCollectionListParams& Params)
{
	TSharedPtr<FJsonObject> QueryParams = MakeShared<FJsonObject>();
	QueryParams->SetNumberField(TEXT("pageNumber"), Params.PageNumber);
	QueryParams->SetNumberField(TEXT("pageSize"), Params.PageSize);
	QueryParams->SetNumberField(TEXT("groupId"), Params.ClassificationID);
	QueryParams->SetStringField(TEXT("qryParam"), Params.QueryParam);
	int32 QueryType = static_cast<int32>(Params.Type);
	QueryParams->SetNumberField(TEXT("type"), QueryType);

	FString QueryParamsStr;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&QueryParamsStr);
	if (!FJsonSerializer::Serialize(QueryParams.ToSharedRef(), Writer))
	{
		UE_LOG(DSNetworkSubsystemLog, Warning, TEXT("%s - Serialize json into string failed."), __FUNCTIONW__);
		return;
	}
	FString URL = FString::Printf(TEXT("%s/bk-design/api/bkGroupCollect/getGroupCollectList"), SERVER_URL);

	FHttpRequestPtr HttpRequestHandle = GetInstance()->CreatePostRequest(URL, QueryParamsStr);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnQueryCollectionListRequestCompletedHandle);
	HttpRequestHandle->ProcessRequest();
}

void UDSNetworkSubsystem::OnQueryCollectionListRequestCompletedHandle(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully) const
{
	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		return;
	}

	TSharedPtr<FJsonObject> ResponseObj;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response->GetContentAsString());
	if (!FJsonSerializer::Deserialize(Reader, ResponseObj))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Can not parse the response data as json."), __FUNCTIONW__);
		return;
	}

	if (!ResponseObj->GetBoolField(TEXT("success")))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : The success field is false, msg is '%s'."), __FUNCTIONW__, *ResponseObj->GetStringField(TEXT("msg")));
		return;
	}

	const TSharedPtr<FJsonObject>& RespObj = ResponseObj->GetObjectField(TEXT("resp"));
	if (!RespObj || !RespObj->HasTypedField<EJson::Array>(TEXT("list")))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Not found 'resp' or 'list' field."), __FUNCTIONW__);
		return;
	}

	TArray<FDSCollectionInfo> ItemList;
	UDesignStationFunctionLibrary::ParseJsonToCollectionList(RespObj->GetArrayField(TEXT("list")), ItemList);

	if (OnQueryCollectionListCompletedDelegate.IsBound())
	{
		FIntPoint PageInfo;
		PageInfo.X = RespObj->GetIntegerField(TEXT("pageNum"));
		PageInfo.Y = RespObj->GetIntegerField(TEXT("pages"));

		OnQueryCollectionListCompletedDelegate.Execute(PageInfo, ItemList);
	}
}

void UDSNetworkSubsystem::SendQueryCollectionClassificationListRequest(const FQueryCollectionClassificationParams& Params)
{
	TSharedPtr<FJsonObject> QueryParams = MakeShared<FJsonObject>();
	QueryParams->SetNumberField(TEXT("pageNumber"), Params.PageNumber);
	QueryParams->SetNumberField(TEXT("pageSize"), Params.PageSize);

	int32 QueryType = static_cast<int32>(Params.Type);
	QueryParams->SetNumberField(TEXT("type"), QueryType);

	FString QueryParamsStr;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&QueryParamsStr);
	if (!FJsonSerializer::Serialize(QueryParams.ToSharedRef(), Writer))
	{
		UE_LOG(DSNetworkSubsystemLog, Warning, TEXT("%s - Serialize json into string failed."), __FUNCTIONW__);
		return;
	}
	FString URL = FString::Printf(TEXT("%s/bk-design/api/bkGroup/getGroupList"), SERVER_URL);

	FHttpRequestPtr HttpRequestHandle = GetInstance()->CreatePostRequest(URL, QueryParamsStr);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnQueryCollectionClassificationListRequestCompletedHandle);
	HttpRequestHandle->ProcessRequest();
}

void UDSNetworkSubsystem::OnQueryCollectionClassificationListRequestCompletedHandle(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully) const
{
	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		return;
	}

	TSharedPtr<FJsonObject> ResponseObj;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response->GetContentAsString());
	if (!FJsonSerializer::Deserialize(Reader, ResponseObj))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Can not parse the response data as json."), __FUNCTIONW__);
		return;
	}

	if (!ResponseObj->GetBoolField(TEXT("success")))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : The success field is false, msg is '%s'."), __FUNCTIONW__, *ResponseObj->GetStringField(TEXT("msg")));
		return;
	}

	TSharedPtr<FJsonObject> DataObj = ResponseObj->GetObjectField(TEXT("resp"));
	if (!DataObj)
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Not found the resp field from response json."), __FUNCTIONW__);
		return;
	}
	if (!DataObj->HasTypedField<EJson::Array>(TEXT("list")))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : 'resp'Not found 'list' field."), __FUNCTIONW__);
		return;
	}

	TArray<FDSCollectionClassificationInfo> ItemList;
	UDesignStationFunctionLibrary::ParseJsonToCollectionClassificaitonList(DataObj->GetArrayField(TEXT("list")), ItemList);

	if (OnQueryCollectionClassificationCompletedDelegate.IsBound())
	{
		OnQueryCollectionClassificationCompletedDelegate.Execute(ItemList);
	}
}

void UDSNetworkSubsystem::SendAddCollectionClassificationRequest(const FDSCollectionClassificationInfo& Params)
{
	TSharedPtr<FJsonObject> QueryParams = MakeShared<FJsonObject>();
	QueryParams->SetStringField(TEXT("name"), Params.Name);
	QueryParams->SetNumberField(TEXT("type"), static_cast<int>(Params.Type));
	FString QueryParamsStr;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&QueryParamsStr);
	if (!FJsonSerializer::Serialize(QueryParams.ToSharedRef(), Writer))
	{
		UE_LOG(DSNetworkSubsystemLog, Warning, TEXT("%s - Serialize json into string failed."), __FUNCTIONW__);
		return;
	}
	FString URL = FString::Printf(TEXT("%s/bk-design/api/bkGroup/createdGroup"), SERVER_URL);

	FHttpRequestPtr HttpRequestHandle = GetInstance()->CreatePostRequest(URL, QueryParamsStr);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnAddCollectionClassificationRequestCompletedHandle);
	HttpRequestHandle->ProcessRequest();
}

void UDSNetworkSubsystem::SendModifyCollectionClassificationRequest(const FDSCollectionClassificationInfo& Params)
{
	TSharedPtr<FJsonObject> QueryParams = MakeShared<FJsonObject>();
	QueryParams->SetNumberField(TEXT("id"), Params.ID);
	QueryParams->SetStringField(TEXT("name"), Params.Name);
	QueryParams->SetNumberField(TEXT("type"), static_cast<int>(Params.Type));
	FString QueryParamsStr;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&QueryParamsStr);
	if (!FJsonSerializer::Serialize(QueryParams.ToSharedRef(), Writer))
	{
		UE_LOG(DSNetworkSubsystemLog, Warning, TEXT("%s - Serialize json into string failed."), __FUNCTIONW__);
		return;
	}
	FString URL = FString::Printf(TEXT("%s/bk-design/api/bkGroup/updateGroup"), SERVER_URL);

	FHttpRequestPtr HttpRequestHandle = GetInstance()->CreatePostRequest(URL, QueryParamsStr);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnAddCollectionClassificationRequestCompletedHandle);
	HttpRequestHandle->ProcessRequest();
}

void UDSNetworkSubsystem::OnAddCollectionClassificationRequestCompletedHandle(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully) const
{
	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		return;
	}

	TSharedPtr<FJsonObject> ResponseObj;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response->GetContentAsString());
	if (!FJsonSerializer::Deserialize(Reader, ResponseObj))
	{
		UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : Can not parse the response data as json."), __FUNCTIONW__);
		return;
	}

	bool bSucceed = ResponseObj->GetBoolField(TEXT("success"));

	UE_LOG(DSNetworkSubsystemLog, Error, TEXT("%s : The success field is false, msg is '%s'."), __FUNCTIONW__, *ResponseObj->GetStringField(TEXT("msg")));

	if (OnAddCollectionClassificationCompletedDelegate.IsBound())
	{
		OnAddCollectionClassificationCompletedDelegate.Execute(bSucceed);
	}
}

void UDSNetworkSubsystem::SendDeleteCollectionClassificationRequest(const FDSCollectionClassificationInfo& Params)
{
	FString UUID = FGuid::NewGuid().ToString();

	TSharedPtr<FJsonObject> QueryParams = MakeShared<FJsonObject>();
	QueryParams->SetNumberField(TEXT("id"), Params.ID);
	FString QueryParamsStr;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&QueryParamsStr);
	if (!FJsonSerializer::Serialize(QueryParams.ToSharedRef(), Writer))
	{
		return;
	}
	FString URL = FString::Printf(TEXT("%s/bk-design/api/bkGroup/deleteGroup"), SERVER_URL);
	FHttpRequestPtr HttpRequestHandle = GetInstance()->CreatePostRequest(URL, QueryParamsStr);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnGetRoomStyleCompletedHandler);
	HttpRequestHandle->ProcessRequest();
}

void UDSNetworkSubsystem::SendGetRoomStyleRequest(const FString& GroupValue, const FOnGetRoomStyleCompleteDelegate& CallBack)
{
	FString RequestID = FGuid::NewGuid().ToString();
	OnRequestRoomStyleCompletedHandlerMap.Add(RequestID, CallBack);

	TMap<FString, FRequestValue> KeyValue;
	KeyValue.Add(TEXT("dictGroupValue"), FRequestValue(GroupValue, true));
	FString URL = FString::Printf(TEXT("%s/bk-design/api/designOther/dictDtlList"), ONECAR_URL);

	FString JsonStr;
	UJsonUtilitiesLibrary::ConvertKeyValuePairsToJsonString(KeyValue, JsonStr);

	FHttpRequestPtr HttpRequestHandle = GetInstance()->CreatePostRequest(URL, JsonStr);
	HttpRequestHandle->SetHeader(TEXT("X-Request-ID"), RequestID);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnGetRoomStyleCompletedHandler);
	HttpRequestHandle->ProcessRequest();
}

void UDSNetworkSubsystem::OnGetRoomStyleCompletedHandler(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully)
{
	FString RequestID = Request->GetHeader(TEXT("X-Request-ID"));

	FOnGetRoomStyleCompleteDelegate* CallBack = OnRequestRoomStyleCompletedHandlerMap.Find(RequestID);
	if (!CallBack)
	{
		UE_LOG(DSNetworkSubsystemLog, Warning, TEXT("%s : No callback found for request ID %s"), __FUNCTIONW__, *RequestID);
		return;
	}

	ON_SCOPE_EXIT
	{
		OnRequestRoomStyleCompletedHandlerMap.Remove(RequestID);
	};

	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		return;
	}

	FRoomStyleDataMsg RoomStyleDataMsg;
	UJsonUtilitiesLibrary::ConvertJsonStringToStruct<FRoomStyleDataMsg>(Response->GetContentAsString(), RoomStyleDataMsg);

	if (CallBack->IsBound())
	{
		CallBack->Execute(RoomStyleDataMsg.resp);
	}
}

void UDSNetworkSubsystem::HandleReceivedRefreshTokenMessage(const FString& NewToken)
{
	setToken(NewToken);
}
