// Fill out your copyright notice in the Description page of Project Settings.

#include "DSRevokeLibrary.h"
#include "BasicClasses/DesignStationController.h"
#include "SubSystems/ModelDependencySubsystem/DSModelDependencySubsystem.h"
#include "SubSystems/MVC/DSMVCSubsystem.h"
#include "SubSystems/MVC/Core/Macro.h"
#include "SubSystems/MVC/Core/Property/CupboardProperty.h"
#include "SubSystems/MVC/Core/Property/DoorAndWindowProperty.h"
#include "SubSystems/MVC/Core/Property/HouseAreaProperty.h"
#include "SubSystems/MVC/Core/Property/HousePathProperty.h"
#include "SubSystems/MVC/Core/Property/PillarProperty.h"
#include "SubSystems/MVC/Library/DSToolLibrary.h"
#include "SubSystems/MVC/Model/CounterTop/CounterTopModel.h"
#include "SubSystems/MVC/Model/DoorAndWindow/DSDoorAndWindowBaseModel.h"
#include "Subsystems/MVC/Model/DoorAndWindow/DSDoorModel.h"
#include "SubSystems/MVC/Model/DoorAndWindow/DSWindowModel.h"
#include "SubSystems/MVC/Model/Group/DSMultiModel.h"
#include "SubSystems/MVC/Model/Group/DSGroupModel.h"
#include "SubSystems/MVC/Model/House/Wall/DSHouseWallModel.h"
#include "SubSystems/MVC/Model/House/Area/DSHouseAreaModel.h"
#include "SubSystems/MVC/Model/Item/DSBeamModel.h"
#include "SubSystems/MVC/Model/Item/DSPillarModel.h"
#include "SubSystems/MVC/Model/Item/DSPlatformModel.h"
#include "SubSystems/Undo/Data/DSCustomRevokeData.h"
#include "SubSystems/MVC/Model/Custom/Library/DSCupboardLibrary.h"
#include "SubSystems/Undo/DSRevokeSubsystem.h"
#include "SubSystems/MVC/Library/CounterTopLibrary.h"
#include "SubSystems/MVC/Library/DSPathLibrary.h"
#include "SubSystems/MVC/Model/CounterTop/CounterTopLineModel.h"
#include "SubSystems/MVC/Model/CounterTop/CounterTopPointModel.h"
#include "SubSystems/MVC/Model/CounterTop/SideCounterTopModel.h"
#include "SubSystems/MVC/Model/Furniture/MoldingFurniture/DSMoldingCeilingModel.h"
#include "SubSystems/MVC/Model/Kitchen/RangeHood/DSRangeHoodModel.h"
#include "SubSystems/MVC/Model/Kitchen/Sink/DSSinkModel.h"
#include "SubSystems/MVC/Model/Kitchen/Stove/DSStoveModel.h"
#include "SubSystems/MVC/Model/Line/CrownMoulding/DSCrownMouldingModel.h"
#include "Subsystems/MVC/Model/Line/LightCord/DSLightCordModel.h"
#include "Subsystems/MVC/Model/Line/SkirtingBoard/DSSkirtingBoardModel.h"
#include "SubSystems/Undo/Data/DSGeneratedLineRevokeData.h"
#include "SubSystems/Undo/Data/DSModelCeilingRevokeData.h"
#include "SubSystems/Undo/Data/DSSoftFurnitureRevokeData.h"

DEFINE_LOG_CATEGORY(DSRevokeLibraryLog);

extern const TArray<EDSModelType> ConsiderAsHouseType;
extern const TArray<EDSModelType> RevokeConsiderAsHouseType;

//UDSCommandCore* UDSRevokeLibrary::CreateCommandCore(UDSBaseModel* InEditModel, const EActionCommandType& InCommandType)
//{
//	UDSCommandCore* Res = nullptr;
//	if (DS_MODEL_VALID_FOR_USE(InEditModel))
//	{
//		if (InEditModel->GetModelType() == EDSModelType::E_House_Wall
//			|| InEditModel->GetModelType() == EDSModelType::E_House_Area)
//		{
//			Res = NewObject<UHouseCommand>();
//		}
//		else
//		{
//			Res = NewObject<USingleCommand>();
//		}
//	}
//	else
//	{
//		/*
//		 *  @@ no edit model, spawn command seem to be a house command
//		 */
//		if (InCommandType == EActionCommandType::E_Spawn)
//		{
//			Res = NewObject<UHouseCommand>();
//		}
//	}
//	return Res;
//}

UDSCommandCore* UDSRevokeLibrary::CreateCommandCore(const EDSPushDataType& InPushType, const FString& InCommandUUID, bool bCreateSnapshotCommand)
{
	EDSRevokeType CurType = EDSRevokeType::E_None;
	switch (InPushType)
	{
	case EDSPushDataType::E_Home:
		{
			CurType = EDSRevokeType::E_House;
			break;
		}
	case EDSPushDataType::E_Single:
		{
			CurType = EDSRevokeType::E_Single;
			break;
		}
	case EDSPushDataType::E_Custom:
		{
			CurType = EDSRevokeType::E_Custom;
			break;
		}
	case EDSPushDataType::E_CounterTop:
		{
			CurType = EDSRevokeType::E_CounterTop;
			break;
		}
	case EDSPushDataType::E_Sink:
		{
			CurType = EDSRevokeType::E_Sink;
			break;
		}
	case EDSPushDataType::E_StoveRangeHood:
		{
			CurType = EDSRevokeType::E_StoveRangeHood;
			break;
		}
	case EDSPushDataType::E_GeneratedLine:
		{
			CurType = EDSRevokeType::E_GeneratedLine;
			break;
		}
	case EDSPushDataType::E_LineEntity:
		{
			CurType = EDSRevokeType::E_LineEntity;
			break;
		}
	case EDSPushDataType::E_Multi_Group:
		{
			CurType = EDSRevokeType::E_Multi_Group;
			break;
		}
	case EDSPushDataType::E_SoftFurniture:
		{
			CurType = EDSRevokeType::E_SoftFurniture;
			break;
		}
	case EDSPushDataType::E_ModelCeiling:
		{
			CurType = EDSRevokeType::E_ModelCeiling;
			break;
		}
	case EDSPushDataType::E_None:
	default:
		return nullptr;
	}
	
	return CreateCommandCore(CurType, InCommandUUID, bCreateSnapshotCommand);
}

UDSCommandCore* UDSRevokeLibrary::CreateCommandCore(const EDSRevokeType& InRevokeType, const FString& InCommandUUID, bool bCreateSnapshotCommand)
{
	UDSCommandCore* Res = nullptr;
	if (InRevokeType == EDSRevokeType::E_House)
	{
		Res = NewObject<UHouseCommand>();
	}
	else if (InRevokeType == EDSRevokeType::E_Single)
	{
		Res = NewObject<USingleCommand>();
	}
	else if (InRevokeType == EDSRevokeType::E_Multi_Group)
	{
		Res = NewObject<UMultiGroupCommand>();
	}
	else if (InRevokeType == EDSRevokeType::E_Custom)
	{
		Res = NewObject<UCustomCommand>();
	}
	else if (InRevokeType == EDSRevokeType::E_CounterTop)
	{
		Res = NewObject<UCounterTopCommand>();
	}
	else if (InRevokeType == EDSRevokeType::E_Sink)
	{
		Res = NewObject<USinkCommand>();
	}
	else if (InRevokeType == EDSRevokeType::E_StoveRangeHood)
	{
		Res = NewObject<USRHCommand>();
	}
	else if (InRevokeType == EDSRevokeType::E_GeneratedLine)
	{
		Res = NewObject<UGeneratedLineCommand>();
	}
	else if (InRevokeType == EDSRevokeType::E_LineEntity)
	{
		Res = NewObject<ULineEntityCommand>();
	}
	else if (InRevokeType == EDSRevokeType::E_SoftFurniture)
	{
		Res = NewObject<USoftFurnitureCommand >();
	}
	else if(InRevokeType == EDSRevokeType::E_ModelCeiling)
	{
		Res = NewObject<UModelCeilingCommand>();
	}

	if (InCommandUUID.IsEmpty())
	{
		Res->SetCommandUUID(FGuid::NewGuid().ToString());
	}
	else
	{
		Res->SetCommandUUID(InCommandUUID);
	}

	Res->SetIsSnapshotCommand(bCreateSnapshotCommand);

	return Res;
}

// void UDSRevokeLibrary::ConstructRevokeData(UDSBaseModel* InEditModel, const EActionCommandType& InCommandType,
//                                            UDSRevokeData& OutRevokeData)
// {
// 	OutRevokeData.CommandType = InCommandType;
// 	TArray<UDSBaseModel*> RevokeModel;
// 	if (DS_MODEL_VALID_FOR_USE(InEditModel))
// 	{
// 		if (InEditModel->GetModelType() == EDSModelType::E_House_Wall
// 			|| InEditModel->GetModelType() == EDSModelType::E_House_Area)
// 		{
// 			RevokeModel = UDSMVCSubsystem::GetInstance()->GetModels(
// 				{EDSModelType::E_House_Wall, EDSModelType::E_House_Area}
// 			);
// 			if (InCommandType == EActionCommandType::E_Spawn)
// 			{
// 				RevokeModel.Remove(InEditModel);
// 			}
// 		}
// 		else if (InEditModel->GetModelType() == EDSModelType::E_House_Pillar
// 			|| InEditModel->GetModelType() == EDSModelType::E_House_Door
// 			|| InEditModel->GetModelType() == EDSModelType::E_House_Window
// 			|| UDSToolLibrary::IsCustomCabinetType(InEditModel->GetModelType()) //先把定制，成品加上，不敢直接用else,全部
// 			|| InEditModel->GetModelType() == EDSModelType::E_Furniture_HouseFurniture)
// 		{
// 			RevokeModel = {InEditModel};
// 		}
// 	}
// 	else
// 	{
// 		if (InCommandType == EActionCommandType::E_Spawn)
// 		{
// 			RevokeModel = UDSMVCSubsystem::GetInstance()->GetModels(
// 				{EDSModelType::E_House_Wall, EDSModelType::E_House_Area}
// 			);
// 		}
// 	}
//
// 	for (auto AM : RevokeModel)
// 	{
// 		if (DS_MODEL_VALID_FOR_USE(AM))
// 		{
// 			FDSRevokeDataInner Temp = UDSRevokeLibrary::ConstructRevokeInnerData(AM);
// 			OutRevokeData.AddData(Temp);
// 		}
// 	}
// }

// void UDSRevokeLibrary::ConstructRevokeData(UDSBaseModel* InEditModel, const EActionCommandType& InCommandType, const UDSCommandCore* InCommandCore)
// {
// 	checkf(InCommandCore != nullptr, TEXT("command core is nullptr"));
//
// 	InCommandCore->GetRevokeDataPtr()->CommandType = InCommandType;
// 	TArray<UDSBaseModel*> RevokeModel;
// 	if (DS_MODEL_VALID_FOR_USE(InEditModel))
// 	{
// 		if (InEditModel->GetModelType() == EDSModelType::E_House_Wall
// 			|| InEditModel->GetModelType() == EDSModelType::E_House_Area)
// 		{
// 			RevokeModel = UDSMVCSubsystem::GetInstance()->GetModels(
// 				{ EDSModelType::E_House_Wall, EDSModelType::E_House_Area }
// 			);
// 			if (InCommandType == EActionCommandType::E_Spawn)
// 			{
// 				RevokeModel.Remove(InEditModel);
// 			}
// 		}
// 		else if (InEditModel->GetModelType() == EDSModelType::E_House_Pillar
// 			|| InEditModel->GetModelType() == EDSModelType::E_House_Door
// 			|| InEditModel->GetModelType() == EDSModelType::E_House_Window
// 			|| IsCabinetType(InEditModel->GetModelType()) //先把定制，成品加上，不敢直接用else,全部
// 			|| InEditModel->GetModelType() == EDSModelType::E_Furniture_HouseFurniture)
// 		{
// 			RevokeModel = { InEditModel };
// 		}
// 	}
// 	else
// 	{
// 		if (InCommandType == EActionCommandType::E_Spawn)
// 		{
// 			RevokeModel = UDSMVCSubsystem::GetInstance()->GetModels(
// 				{ EDSModelType::E_House_Wall, EDSModelType::E_House_Area }
// 			);
// 		}
// 	}
//
// 	for (auto AM : RevokeModel)
// 	{
// 		if (DS_MODEL_VALID_FOR_USE(AM))
// 		{
// 			FDSRevokeDataInner Temp = ConstructRevokeInnerData(AM);
// 			InCommandCore->GetRevokeDataPtr()->AddData(Temp);
// 		}
// 	}
// }

void UDSRevokeLibrary::ConstructRevokeData(UDSBaseModel* InEditModel, const FDSModelExecuteType& InExecuteType, const UDSCommandCore* InCommandCore)
{
	checkf(InCommandCore != nullptr, TEXT("command core is nullptr"));

	InCommandCore->GetRevokeDataPtr()->ModelExecuteType = InExecuteType;
	TArray<UDSBaseModel*> RevokeModel;
	if (DS_MODEL_VALID_FOR_USE(InEditModel))
	{
		if (InEditModel->GetModelType() == EDSModelType::E_House_Wall || InEditModel->GetModelType() == EDSModelType::E_House_Area)
		{
			RevokeModel = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_House_Wall, EDSModelType::E_House_Area }
			);
			if (InExecuteType.IsSpawnExecute())
			{
				RevokeModel.Remove(InEditModel);
			}
		}
		else if (InEditModel->GetModelType() == EDSModelType::E_House_Pillar
			|| InEditModel->GetModelType() == EDSModelType::E_House_Door
			|| InEditModel->GetModelType() == EDSModelType::E_House_Window
			|| UDSToolLibrary::IsCustomCabinetType(InEditModel->GetModelType()) //先把定制，成品加上，不敢直接用else,全部
			|| InEditModel->GetModelType() == EDSModelType::E_Furniture_HouseFurniture)
		{
			RevokeModel = { InEditModel };
		}
	}
	else
	{
		if (InExecuteType.IsSpawnExecute())
		{
			RevokeModel = UDSMVCSubsystem::GetInstance()->GetModels(
				{ EDSModelType::E_House_Wall, EDSModelType::E_House_Area }
			);
		}
	}

	for (auto AM : RevokeModel)
	{
		if (DS_MODEL_VALID_FOR_USE(AM))
		{
			FDSRevokeDataInner Temp = ConstructRevokeInnerData(AM);
			InCommandCore->GetRevokeDataPtr()->AddData(Temp);
		}
	}
}


// void UDSRevokeLibrary::ConstructRevokeData_Single(UDSBaseModel* InEditModel, const EActionCommandType& InCommandType,
//                                                   UDSRevokeData& OutRevokeData)
// {
// 	OutRevokeData.CommandType = InCommandType;
// 	if (DS_MODEL_VALID_FOR_USE(InEditModel))
// 	{
// 		FDSRevokeDataInner Temp = UDSRevokeLibrary::ConstructRevokeInnerData(InEditModel);
// 		OutRevokeData.AddData(Temp);
// 	}
// }

void UDSRevokeLibrary::ConstructRevokeData_Single(UDSBaseModel* InEditModel, const FDSModelExecuteType& InExecuteType, const UDSCommandCore* InCommandCore)
{
	checkf(InCommandCore != nullptr, TEXT("command core is nullptr"));

	InCommandCore->GetRevokeDataPtr()->ModelExecuteType = InExecuteType;
	if (DS_MODEL_VALID_FOR_USE(InEditModel))
	{
		FDSRevokeDataInner Temp = ConstructRevokeInnerData(InEditModel);
		InCommandCore->GetRevokeDataPtr()->AddData(Temp);
	}
}

// void UDSRevokeLibrary::ConstructRevokeData_Multi(UDSBaseModel* InEditModel, const EActionCommandType& InCommandType,
//                                                  UDSRevokeData& OutRevokeData)
// {
// 	UDSRevokeLibrary::ConstructRevokeData_Single(InEditModel, InCommandType, OutRevokeData);
// }

void UDSRevokeLibrary::ConstructRevokeData_Multi(UDSBaseModel* InEditModel, const FDSModelExecuteType& InExecuteType, const UDSCommandCore* InCommandCore)
{
	ConstructRevokeData_Single(InEditModel, InExecuteType, InCommandCore);
}

// void UDSRevokeLibrary::ConstructRevokeData_House(const TArray<UDSBaseModel*>& InEditModel, UDSRevokeData& OutRevokeData)
// {
// 	OutRevokeData.CommandType = EActionCommandType::E_Spawn;
// 	TArray<UDSBaseModel*> RevokeModel = UDSRevokeLibrary::GetCurrentHouseModel();
// 	for (auto& IM : InEditModel)
// 	{
// 		if (DS_MODEL_VALID_FOR_USE(IM))
// 		{
// 			RevokeModel.Remove(IM);
// 		}
// 	}
//
// 	for (auto AM : RevokeModel)
// 	{
// 		if (DS_MODEL_VALID_FOR_USE(AM))
// 		{
// 			FDSRevokeDataInner Temp = UDSRevokeLibrary::ConstructRevokeInnerData(AM);
// 			OutRevokeData.AddData(Temp);
// 		}
// 	}
// }

void UDSRevokeLibrary::ConstructRevokeData_House(const TArray<UDSBaseModel*>& InEditModel, const UDSCommandCore* InCommandCore)
{
	checkf(InCommandCore != nullptr, TEXT("command core is nullptr"));
	
	InCommandCore->GetRevokeDataPtr()->ModelExecuteType = FDSModelExecuteType::ExecuteSpawn;
	TArray<UDSBaseModel*> RevokeModel = GetCurrentHouseModel();
	for (auto& IM : InEditModel)
	{
		if (DS_MODEL_VALID_FOR_USE(IM))
		{
			RevokeModel.Remove(IM);
		}
	}

	for (auto AM : RevokeModel)
	{
		if (DS_MODEL_VALID_FOR_USE(AM))
		{
			FDSRevokeDataInner Temp = ConstructRevokeInnerData(AM);
			InCommandCore->GetRevokeDataPtr()->AddData(Temp);
		}
	}
}

void UDSRevokeLibrary::ConstructRevokeData_House_WinDoor(const TArray<UDSBaseModel*>& InIgnoreModels, const UDSCommandCore* InCommandCore)
{//门窗的撤销数据
	checkf(InCommandCore != nullptr, TEXT("command core is nullptr"));
	
	InCommandCore->GetRevokeDataPtr_WinDoor()->ModelExecuteType = FDSModelExecuteType::ExecuteSpawn;
	TArray<UDSBaseModel*> RevokeModel = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_House_Window, EDSModelType::E_House_Door });
	for (auto& IM : InIgnoreModels)
	{
		if (IM != nullptr && RevokeModel.Contains(IM))
		{
			RevokeModel.Remove(IM);
		}
	}

	for (auto AM : RevokeModel)
	{
		if (DS_MODEL_VALID_FOR_USE(AM))
		{
			FDSRevokeDataInner Temp = ConstructRevokeInnerData(AM);
			InCommandCore->GetRevokeDataPtr_WinDoor()->AddData(Temp);
		}
	}
}

void UDSRevokeLibrary::ConstructRevokeData_House_Area(const TArray<UDSBaseModel*>& InIgnoreModels, const UDSCommandCore* InCommandCore)
{//区域的撤销数据
	InCommandCore->GetRevokeDataPtr_Area()->ModelExecuteType = FDSModelExecuteType::ExecuteSpawn;
	TArray<UDSBaseModel*> RevokeModel = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_House_Area });
	for (auto& IM : InIgnoreModels)
	{
		if (IM != nullptr && RevokeModel.Contains(IM))
		{
			RevokeModel.Remove(IM);
		}
	}

	for (auto AM : RevokeModel)
	{
		if (DS_MODEL_VALID_FOR_USE(AM))
		{
			FDSRevokeDataInner Temp = ConstructRevokeInnerData(AM);
			InCommandCore->GetRevokeDataPtr_Area()->AddData(Temp);
		}
	}
}

void UDSRevokeLibrary::ConstructRevokeData_Custom(UDSBaseModel* InEditModel, const FDSModelExecuteType& InExecuteType, UDSCommandCore* InCommandCore)
{
	checkf(InCommandCore != nullptr, TEXT("command core is nullptr"));

	UDSRevokeData* NewData = NewObject<UDSCustomRevokeData>();
	NewData->ModelExecuteType = InExecuteType;
	if (DS_MODEL_VALID_FOR_USE(InEditModel))
	{
		ConstructRevokeModelData_Custom(InEditModel, NewData);
	}
	InCommandCore->AddRevokeData(NewData);
}

void UDSRevokeLibrary::ConstructRevokeData_Custom(UDSBaseModel* InEditModel, const FDSModelExecuteType& InExecuteType,
	const TSharedPtr<FMultiComponentDataItem>& InComponentData, const EDSRevokeComponentType& InComponentCommandType,
	UDSCommandCore* InCommandCore)
{
	checkf(InCommandCore != nullptr, TEXT("command core is nullptr"));

	UDSRevokeData* NewData = NewObject<UDSCustomRevokeData>();
	NewData->ModelExecuteType = InExecuteType;
	if (DS_MODEL_VALID_FOR_USE(InEditModel))
	{
		UDSRevokeLibrary::ConstructRevokeModelData_Custom(InEditModel, InComponentData, InComponentCommandType, NewData);
	}
	InCommandCore->AddRevokeData(NewData);
}

void UDSRevokeLibrary::ConstructRevokeData_Custom(UDSBaseModel* InEditModel, const FDSModelExecuteType& InExecuteType, const TSharedPtr<FDSCustomComponentRevokeData>& InComponentData, UDSCommandCore* InCommandCore)
{
	checkf(InCommandCore != nullptr, TEXT("command core is nullptr"));

	UDSRevokeData* NewData = NewObject<UDSCustomRevokeData>();
	NewData->ModelExecuteType = InExecuteType;
	if (DS_MODEL_VALID_FOR_USE(InEditModel))
	{
		UDSRevokeLibrary::ConstructRevokeModelData_Custom(InEditModel, InComponentData, NewData);
	}
	InCommandCore->AddRevokeData(NewData);
}

void UDSRevokeLibrary::ConstructRevokeData_CT(UDSBaseModel* InEditModel, const FDSModelExecuteType& InExecuteType, const EDSRevokePoolType& InPoolType, const FDSCounterTopPushData& InPushData,
	UDSCommandCore* InCommandCore)
{
	checkf(InCommandCore != nullptr, TEXT("command core is nullptr"));

	UDSCounterTopRevokeData* NewData = NewObject<UDSCounterTopRevokeData>();
	NewData->ModelExecuteType = InExecuteType;
	if (IsValid(InEditModel))
	{//state
		NewData->SetVisualState(InEditModel->GetVisual());
	}

	if (IsValid(InEditModel) &&
		(Cast<UDSCounterTopModel>(InEditModel) || Cast<UDSSideCounterTopModel>(InEditModel)
			|| Cast<UDSCounterTopLineModel>(InEditModel) || Cast<UDSCounterTopPointModel>(InEditModel))
		)
	{
		bool IsSide = Cast<UDSSideCounterTopModel>(InEditModel) != nullptr;
		NewData->SetModelInfo(InEditModel->GetUUID(), InEditModel->GetModelType(), IsSide);

		//relation
		if (Cast<UDSCounterTopModel>(InEditModel))
		{//CT
			TArray<FString> RelationModel = UDSModelDependencySubsystem::GetInstance()->FindCounterTopRelations(InEditModel->GetUUID());
			UDSRevokeLibrary::ConstructRevokeCTRelation(RelationModel, NewData->GetCTDependencyInfoRef());
		}
		else if (Cast<UDSSideCounterTopModel>(InEditModel))
		{//side CT
			FSideCounterTopDependencyInfo* RelationInfo = UDSModelDependencySubsystem::GetInstance()->FindSideCounterTopDependency(InEditModel->GetUUID());
			UDSRevokeLibrary::ConstructRevokeSideCTRelation(RelationInfo, NewData->GetSideCTDependencyInfoRef());
		}
		else if (Cast<UDSCounterTopLineModel>(InEditModel) || Cast<UDSCounterTopPointModel>(InEditModel))
		{// CT line / point
			TSet<TWeakObjectPtr<UDSBaseModel>> LinkModel = InEditModel->GetLinkModels();
			for (auto& LM : LinkModel)
			{
				if (IsValid(LM.Get()))
				{
					NewData->AddLinkUUID(LM->GetUUID());
				}
			}
		}
	}

	NewData->ConstructDataFromPushData(InPushData);

	InCommandCore->AddRevokeData(NewData);

}

void UDSRevokeLibrary::ConstructRevokeData_CounterTop(UDSBaseModel* InEditModel, const FDSModelExecuteType& InExecuteType, const FDSCounterTopPushData& InPushData, UDSCommandCore* InCommandCore)
{
	checkf(InCommandCore != nullptr, TEXT("command core is nullptr"));

	UDSCounterTopRevokeData* NewData = NewObject<UDSCounterTopRevokeData>();
	NewData->ModelExecuteType = InExecuteType;
	if (IsValid(InEditModel))
	{//state
		NewData->SetVisualState(InEditModel->GetVisual());
	}

	if (IsValid(InEditModel) &&
		(Cast<UDSCounterTopModel>(InEditModel) || Cast<UDSSideCounterTopModel>(InEditModel)
			|| Cast<UDSCounterTopLineModel>(InEditModel) || Cast<UDSCounterTopPointModel>(InEditModel))
		)
	{
		bool IsSide = Cast<UDSSideCounterTopModel>(InEditModel) != nullptr;
		NewData->SetModelInfo(InEditModel->GetUUID(), InEditModel->GetModelType(), IsSide);

		//relation
		if (Cast<UDSCounterTopModel>(InEditModel))
		{//CT
			TArray<FString> RelationModel = UDSModelDependencySubsystem::GetInstance()->FindCounterTopRelations(InEditModel->GetUUID());
			UDSRevokeLibrary::ConstructRevokeCTRelation(RelationModel, NewData->GetCTDependencyInfoRef());
		}
		else if (Cast<UDSSideCounterTopModel>(InEditModel))
		{//side CT
			FSideCounterTopDependencyInfo* RelationInfo = UDSModelDependencySubsystem::GetInstance()->FindSideCounterTopDependency(InEditModel->GetUUID());
			UDSRevokeLibrary::ConstructRevokeSideCTRelation(RelationInfo, NewData->GetSideCTDependencyInfoRef());
		}
		else if (Cast<UDSCounterTopLineModel>(InEditModel) || Cast<UDSCounterTopPointModel>(InEditModel))
		{// CT line / point
			TSet<TWeakObjectPtr<UDSBaseModel>> LinkModel = InEditModel->GetLinkModels();
			for (auto& LM : LinkModel)
			{
				if (IsValid(LM.Get()))
				{
					NewData->AddLinkUUID(LM->GetUUID());
				}
			}
		}
	}

	NewData->ConstructDataFromPushData(InPushData);

	InCommandCore->AddRevokeData(NewData);
}

UDSCounterTopRevokeData* UDSRevokeLibrary::ConstructCTRevokeData(UDSBaseModel* InEditModel, const FDSModelExecuteType& InExecuteType, const FDSCounterTopPushData& InPushData)
{
	UDSCounterTopRevokeData* NewData = NewObject<UDSCounterTopRevokeData>();
	NewData->ModelExecuteType = InExecuteType;
	if (IsValid(InEditModel))
	{//state
		NewData->SetVisualState(InEditModel->GetVisual());
	}

	if (IsValid(InEditModel) &&
		(Cast<UDSCounterTopModel>(InEditModel) || Cast<UDSSideCounterTopModel>(InEditModel)
			|| Cast<UDSCounterTopLineModel>(InEditModel) || Cast<UDSCounterTopPointModel>(InEditModel))
		)
	{
		bool IsSide = Cast<UDSSideCounterTopModel>(InEditModel) != nullptr;
		NewData->SetModelInfo(InEditModel->GetUUID(), InEditModel->GetModelType(), IsSide);

		//relation
		if (Cast<UDSCounterTopModel>(InEditModel))
		{//CT
			TArray<FString> RelationModel = UDSModelDependencySubsystem::GetInstance()->FindCounterTopRelations(InEditModel->GetUUID());
			UDSRevokeLibrary::ConstructRevokeCTRelation(RelationModel, NewData->GetCTDependencyInfoRef());
		}
		else if (Cast<UDSSideCounterTopModel>(InEditModel))
		{//side CT
			FSideCounterTopDependencyInfo* RelationInfo = UDSModelDependencySubsystem::GetInstance()->FindSideCounterTopDependency(InEditModel->GetUUID());
			UDSRevokeLibrary::ConstructRevokeSideCTRelation(RelationInfo, NewData->GetSideCTDependencyInfoRef());
		}
		else if (Cast<UDSCounterTopLineModel>(InEditModel) || Cast<UDSCounterTopPointModel>(InEditModel))
		{// CT line / point
			TSet<TWeakObjectPtr<UDSBaseModel>> LinkModel = InEditModel->GetLinkModels();
			for (auto& LM : LinkModel)
			{
				if (IsValid(LM.Get()))
				{
					NewData->AddLinkUUID(LM->GetUUID());
				}
			}
		}
	}
	NewData->ConstructDataFromPushData(InPushData);
	return NewData;
}

void UDSRevokeLibrary::ConstructRevokeDataUnion(const UDSBaseModel* InEditModel, const FDSModelExecuteType& InExecuteType, const EDSRevokePoolType& InPoolType, const FDSRevokePushData& InPushData, UDSCommandCore* InCommandCore)
{
	checkf(InCommandCore != nullptr, TEXT("command core is nullptr"));

	if (InCommandCore->IsSnapshotCommand())
	{//snapshot
		if(InPushData.DataType == EDSPushDataType::E_Home)
		{//户型部分全部快照
			UDSRevokeLibrary::ConstructRevokeData_House(InPushData.RoomData.IgnoreModels, InCommandCore);
			UDSRevokeLibrary::ConstructRevokeData_House_WinDoor(InPushData.RoomData.IgnoreModels, InCommandCore);
			UDSRevokeLibrary::ConstructRevokeData_House_Area(InPushData.RoomData.IgnoreModels, InCommandCore);
		}
		else if (InPushData.DataType == EDSPushDataType::E_GeneratedLine)
		{
			TArray<UDSBaseModel*> AllLineModels = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_Generated_CrownMoulding, EDSModelType::E_Generated_LightCord, EDSModelType::E_Generated_SkirtingBoard });
			for (UDSBaseModel* LineModel : AllLineModels)
			{
				UDSGeneratedLineRevokeData* NewLineData = NewObject<UDSGeneratedLineRevokeData>();
				NewLineData->ModelInfo.UUID = LineModel->GetUUID();
				NewLineData->ModelInfo.ModelType = LineModel->GetModelType();
				NewLineData->ModelInfo.SetProperty(LineModel->GetTypedProperty<FDSGeneratedLineBaseProperty>(), false);
				NewLineData->RelatedModels = UDSModelDependencySubsystem::GetInstance()->FindGeneratedLineRelations(NewLineData->ModelInfo.UUID);

				InCommandCore->AddRevokeData(NewLineData);
			}
		}
		else if (InPushData.DataType == EDSPushDataType::E_StoveRangeHood)
		{// 用于灶台烟机生成状态，快照
			TArray<UDSBaseModel*> AllStoveRangHood = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_Custom_Stove, EDSModelType::E_Custom_RangeHood });
			for (const auto& ASR : AllStoveRangHood)
			{
				if (!IsValid(ASR)) continue;

				UDSSRHRevokeData* ASRRevokeData = NewObject<UDSSRHRevokeData>();
				ASRRevokeData->SetModelInfo(ASR->GetUUID(), ASR->GetModelType());
				ASRRevokeData->ExecuteType = InExecuteType;
				if (ASR->GetModelType() == EDSModelType::E_Custom_Stove)
				{
					ASRRevokeData->SetProperty(StaticCastSharedPtr<FDSStoveProperty>(ASR->GetPropertySharedPtr()));
					ASRRevokeData->SetLinkUUID(UDSModelDependencySubsystem::GetInstance()->FindCupboardByStove(ASR->GetUUID()));
				}
				else if (ASR->GetModelType() == EDSModelType::E_Custom_RangeHood)
				{
					ASRRevokeData->SetProperty(StaticCastSharedPtr<FDSRangeHoodProperty>(ASR->GetPropertySharedPtr()));
					ASRRevokeData->SetLinkUUID(UDSModelDependencySubsystem::GetInstance()->FindStoveByRangeHood(ASR->GetUUID()));
				}
				else
				{
					checkNoEntry();
				}

				InCommandCore->AddRevokeData(ASRRevokeData);
			}
		}
		else if (InPushData.DataType == EDSPushDataType::E_Sink)
		{// waterTap in sink 
			TArray<UDSBaseModel*> AllSinkWT = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_Custom_Sink });
			for (const auto& ASWT : AllSinkWT)
			{
				if (!IsValid(ASWT)) continue;

				UDSSinkRevokeData* ASWTRevokeData = NewObject<UDSSinkRevokeData>();
				ASWTRevokeData->SetModelInfo(ASWT->GetUUID(), ASWT->GetModelType());
				ASWTRevokeData->SetExecuteType(InExecuteType);
				ASWTRevokeData->SetProperty(StaticCastSharedPtr<FDSSinkProperty>(ASWT->GetPropertySharedPtr()));
				ASWTRevokeData->SetLinkUUID(UDSModelDependencySubsystem::GetInstance()->FindCupboardBySink(ASWT->GetUUID()));

				InCommandCore->AddRevokeData(ASWTRevokeData);
			}
		}
		else if (InPushData.DataType == EDSPushDataType::E_CounterTop)
		{
			TArray<UDSBaseModel*> AllModels = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_Generated_CounterTop, EDSModelType::E_Generated_SideCounterTop });
			for (UDSBaseModel* Model : AllModels)
			{
				FDSCounterTopPushData PushData;
				if (Model->GetModelType() == EDSModelType::E_Generated_CounterTop)
				{
					PushData.SetCTPropertyData(Model->GetTypedProperty<FDSCounterTopProperty>(), false);
				}
				else if (Model->GetModelType() == EDSModelType::E_Generated_SideCounterTop)
				{
					PushData.SetCTPropertyData(Model->GetTypedProperty<FDSSideCounterTopProperty>(), false);
				}
				
				ConstructRevokeData_CounterTop(Model, InExecuteType, PushData, InCommandCore);
			}
		}
	}
	else
	{
		//single
		UDSRevokeData* NewData = nullptr;
		if (IsValid(InEditModel))
		{//single union
			if(InPushData.DataType == EDSPushDataType::E_Single)
			{
				UDSRevokeLibrary::ConstructRevokeData(const_cast<UDSBaseModel*>(InEditModel), InExecuteType, InCommandCore);
				return;
			}
			else if(InPushData.DataType == EDSPushDataType::E_Custom)
			{//定制分柜子和柜子上挂载的门、抽屉...
				UDSCustomRevokeData* CustomData = NewObject<UDSCustomRevokeData>();
				if (UDSRevokeLibrary::IsAddComponentModel(const_cast<UDSBaseModel*>(InEditModel)) && !UDSMVCSubsystem::GetInstance()->GetRevokeMark().GetPushDirect())
				{
					UDSBaseModel* OwnerModel = const_cast<UDSBaseModel*>(InEditModel)->GetOwnerModel();
					if (!InPushData.CustomData.ComponentRevokeDataPtr.IsValid() && OwnerModel != nullptr)
					{
						TSharedPtr<FDSCustomComponentRevokeData> CompRevokeData = UDSRevokeLibrary::ConstructComponentRevokeData_Custom(const_cast<UDSBaseModel*>(InEditModel), InExecuteType);
						CustomData->ModelExecuteType = InExecuteType;
						UDSRevokeLibrary::ConstructCustomRevokeLogic(OwnerModel, CompRevokeData, CustomData);
					}
					else
					{// safe logic -- no execute
						//checkf(false, TEXT("EDSPushDataType::E_Custom --- Safe Logic --- This Brunch NoEntry"));
					}
				}
				else
				{
					CustomData->ModelExecuteType = InExecuteType;
					CustomData->GroupMarkData = InEditModel->GetMultiGroupData();
					UDSRevokeLibrary::ConstructCustomRevokeLogic(const_cast<UDSBaseModel*>(InEditModel), InPushData.CustomData.ComponentRevokeDataPtr, CustomData);
				}
				NewData = CustomData;
			}
			else if (InPushData.DataType == EDSPushDataType::E_CounterTop)
			{
				EDSModelType ModelType = InEditModel->GetModelType();
				if (ModelType == EDSModelType::E_Generated_CounterTop || ModelType == EDSModelType::E_Generated_SideCounterTop)
				{
					UDSRevokeLibrary::ConstructRevokeData_CounterTop(const_cast<UDSBaseModel*>(InEditModel), InExecuteType, InPushData.CounterTopData, InCommandCore);
				}
				else if (ModelType == EDSModelType::E_Generated_CounterTop_Line || ModelType == EDSModelType::E_Generated_CounterTop_Point)
				{
					TSet<TWeakObjectPtr<UDSBaseModel>> LinkModels = const_cast<UDSBaseModel*>(InEditModel)->GetLinkModels();

					if (InPushData.CounterTopData.GetRevokeType() != EDSCTRevokeType::E_Delete)
					{
						//line point push data
						UDSRevokeLibrary::ConstructRevokeData_CounterTop(const_cast<UDSBaseModel*>(InEditModel), InExecuteType, InPushData.CounterTopData, InCommandCore);

						//push link model data
						FDSCounterTopPushData PushData;
						PushData.CT_RevokeType = EDSCTRevokeType::E_Update;
						for (auto& LM : LinkModels)
						{
							if (IsValid(LM.Get()))
							{
								UDSRevokeLibrary::ConstructRevokeData_CounterTop(LM.Get(), InExecuteType, PushData, InCommandCore);
							}
						}
					}
					else
					{//if delete, model refresh data and regenerate point line

						for (auto& LM : LinkModels)
						{
							if (IsValid(LM.Get()))
							{
								FDSCounterTopPushData PushData(EDSCTRevokeType::E_ReGenerateLinePoint);
								UDSRevokeLibrary::UpdatePushDataProperty(LM.Get(), PushData);
								UDSRevokeLibrary::ConstructRevokeData_CounterTop(LM.Get(), InExecuteType, PushData, InCommandCore);
							}
						}
					}
				}

				return;
			}
			else if (InPushData.DataType == EDSPushDataType::E_Sink)
			{//sink
				UDSSinkRevokeData* SinkData = NewObject<UDSSinkRevokeData>();
				SinkData->SetExecuteType(InExecuteType);
				SinkData->SetRevokeType(InPushData.SinkData.RevokeType);
				SinkData->SetModelInfo(InEditModel->GetUUID(), InEditModel->GetModelType());
				SinkData->SetProperty(InPushData.SinkData.Property);
				SinkData->SetLinkUUID(InPushData.SinkData.LinkUUID);

				NewData = SinkData;
			}
			else if (InPushData.DataType == EDSPushDataType::E_StoveRangeHood)
			{//stove \ RangeHood
				UDSSRHRevokeData* Data = NewObject<UDSSRHRevokeData>();
				Data->SetExecuteType(InExecuteType);
				Data->SetRevokeType(InPushData.StoveRangeHoodData.RevokeType);
				Data->SetModelInfo(InEditModel->GetUUID(), InEditModel->GetModelType());
				if (InPushData.StoveRangeHoodData.IsStovePropertyValid())
				{
					Data->SetProperty(InPushData.StoveRangeHoodData.GetStoveProperty());
				}
				else if (InPushData.StoveRangeHoodData.IsHoodPropertyValid())
				{
					Data->SetProperty(InPushData.StoveRangeHoodData.GetHoodProperty());
				}
				else
				{
					checkNoEntry();
				}

				Data->SetLinkUUID(InPushData.StoveRangeHoodData.LinkUUID);

				NewData = Data;
			}
			else if (InPushData.DataType == EDSPushDataType::E_GeneratedLine)
			{
				UDSGeneratedLineRevokeData* NewLineData = NewObject<UDSGeneratedLineRevokeData>();
				NewLineData->ModelInfo.UUID = InEditModel->GetUUID();
				NewLineData->ModelInfo.ModelType = InEditModel->GetModelType();
				NewLineData->ModelInfo.SetProperty(InPushData.GeneratedLineData.Property);
				NewLineData->ExecuteType = InExecuteType;
				NewLineData->RevokeType = InPushData.GeneratedLineData.RevokeType;
				NewLineData->RelatedModels = InPushData.GeneratedLineData.Relations;

				if (InCommandCore->IsA(UMultiGroupCommand::StaticClass()))
				{//多选
					Cast<UMultiGroupCommand>(InCommandCore)->AddInnerRevokeData(NewLineData);
					return;
				}

				NewData = NewLineData;
			}
			else if (InPushData.DataType == EDSPushDataType::E_LineEntity)
			{
				UDSLineEntityRevokeData* NewLineData = NewObject<UDSLineEntityRevokeData>();
				NewLineData->ModelInfo.UUID = InEditModel->GetUUID();
				NewLineData->ModelInfo.ModelType = InEditModel->GetModelType();
				NewLineData->LinkModelInfo.ModelType = InPushData.LineEntityRevokeData.LinkModelType;
				NewLineData->LinkModelInfo.UUID = InPushData.LineEntityRevokeData.LinkUUID;
				NewLineData->ModelInfo.SetProperty(InPushData.LineEntityRevokeData.Property);
				NewLineData->ExecuteType = InExecuteType;
				NewLineData->RevokeType = InPushData.LineEntityRevokeData.RevokeType;

				NewData = NewLineData;
			}
			else if (InPushData.DataType == EDSPushDataType::E_SoftFurniture)
			{
				UDSSoftFurnitureRevokeData* SoftFurnitureData = NewObject<UDSSoftFurnitureRevokeData>();
				SoftFurnitureData->SetExecuteType(InExecuteType);
				SoftFurnitureData->SetRevokeType(InPushData.SoftFurnitureRevokeData.RevokeType);
				SoftFurnitureData->SetModelInfo(InEditModel->GetUUID(), InEditModel->GetModelType());
				SoftFurnitureData->SetProperty(InPushData.SoftFurnitureRevokeData.Property);
				SoftFurnitureData->SetLinkUUID(InPushData.SoftFurnitureRevokeData.LinkUUID);
				SoftFurnitureData->GroupMarkData = InEditModel->GetMultiGroupData();
				NewData = SoftFurnitureData;
			}
			else if(InPushData.DataType == EDSPushDataType::E_ModelCeiling)
			{
				UDSModelCeilingRevokeData* RevokeData = NewObject<UDSModelCeilingRevokeData>();
				RevokeData->ExecuteType = InExecuteType;
				RevokeData->SetRevokeType(InPushData.ModelCeilingData.RevokeType);
				RevokeData->SetModelInfo(InEditModel->GetUUID(), InEditModel->GetModelType());
				RevokeData->SetProperty(InPushData.ModelCeilingData.Property);
				NewData = RevokeData;
			}
			else if (InPushData.DataType == EDSPushDataType::E_Multi_Group)
			{//multi group
				if (InCommandCore->GetRevokeDataPtr() == nullptr)
				{
					UDSMultiGroupRevokeData* MultiGroupRevokeData = NewObject<UDSMultiGroupRevokeData>();
					InCommandCore->AddRevokeData(MultiGroupRevokeData);
				}
				UDSMultiGroupRevokeData* MultiGroupData = Cast<UDSMultiGroupRevokeData>(InCommandCore->GetRevokeDataPtr());
				MultiGroupData->ExecuteType = InExecuteType;
				MultiGroupData->RevokeType = InPushData.MultiGroupPushData.RevokeType;
				if (const UDSMultiModel* MultiModel = Cast<UDSMultiModel>(InEditModel))
				{
					MultiGroupData->SetCurModelInfo(MultiModel->GetUUID(), MultiModel->GetModelType(), MultiModel->GetPropertySharedPtr());
					if (MultiModel->HasTypes({ EDSModelType::E_Generated_LineEntity }))
					{//有线条时赋值全局Core UUID
						UDSRevokeSubsystem::GetInstance()->SetGlobalCommandUUID(InCommandCore->GetCommandUUID());
					}
					for (const auto& IM : MultiModel->GetIncludeModel())
					{
						if (IM != nullptr)
						{
							UDSMultiGroupItemData* InnerData = UDSRevokeLibrary::ConstructInnerIncludeRevokeData(IM, InExecuteType);
							if (InnerData != nullptr)
							{
								MultiGroupData->AddData(InnerData);
							}
						}
					}
				}
			}
		}
		else
		{
			if (InPushData.DataType == EDSPushDataType::E_CounterTop)
			{
				ConstructRevokeData_CounterTop(nullptr, InExecuteType, InPushData.CounterTopData, InCommandCore);
				return;
			}
		}

		if (NewData != nullptr)
		{
			InCommandCore->AddRevokeData(NewData);
		}
	}
}

// UDSRevokeData UDSRevokeLibrary::ConstructRevokeData_CurrentHouse()
// {
// 	UDSRevokeData OutRevokeData;
// 	OutRevokeData.CommandType = EActionCommandType::E_All;
// 	TArray<UDSBaseModel*> RevokeModel = UDSRevokeLibrary::GetCurrentHouseModel();
// 	for (auto AM : RevokeModel)
// 	{
// 		if (DS_MODEL_VALID_FOR_USE(AM))
// 		{
// 			FDSRevokeDataInner Temp = UDSRevokeLibrary::ConstructRevokeInnerData(AM);
// 			OutRevokeData.AddData(Temp);
// 		}
// 	}
// 	return OutRevokeData;
// }

// void UDSRevokeLibrary::ConstructRevokeData_CurrentHouse(const UDSCommandCore* InCommandCore)
// {
// 	checkf(InCommandCore != nullptr, TEXT("command core is nullptr"));
// 	
// 	InCommandCore->GetRevokeDataPtr()->CommandType = EActionCommandType::E_All;
// 	TArray<UDSBaseModel*> RevokeModel = UDSRevokeLibrary::GetCurrentHouseModel();
// 	for (auto AM : RevokeModel)
// 	{
// 		if (DS_MODEL_VALID_FOR_USE(AM))
// 		{
// 			FDSRevokeDataInner Temp = UDSRevokeLibrary::ConstructRevokeInnerData(AM);
// 			InCommandCore->GetRevokeDataPtr()->AddData(Temp);
// 		}
// 	}
// }

void UDSRevokeLibrary::ConstructRevokeData_CurrentHouse(UDSRevokeData* InRevokeDataPtr)
{
	checkf(InRevokeDataPtr != nullptr, TEXT("command core revoke data is nullptr"));

	InRevokeDataPtr->ModelExecuteType = FDSModelExecuteType::ExecuteAll;
	
	TArray<UDSBaseModel*> RevokeModel = GetCurrentHouseModel();
	for (auto AM : RevokeModel)
	{
		if (DS_MODEL_VALID_FOR_USE(AM))
		{
			FDSRevokeDataInner Temp = ConstructRevokeInnerData(AM);
			InRevokeDataPtr->AddData(Temp);
		}
	}
}

void UDSRevokeLibrary::ConstructRevokeData_CurrentHouse_WinDoor(UDSRevokeData* InRevokeDataPtr)
{
	checkf(InRevokeDataPtr != nullptr, TEXT("command core revoke data is nullptr"));

	InRevokeDataPtr->ModelExecuteType = FDSModelExecuteType::ExecuteAll;

	TArray<UDSBaseModel*> RevokeModel = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_House_Window, EDSModelType::E_House_Door });
	for (auto AM : RevokeModel)
	{
		if (DS_MODEL_VALID_FOR_USE(AM))
		{
			FDSRevokeDataInner Temp = ConstructRevokeInnerData(AM);
			InRevokeDataPtr->AddData(Temp);
		}
	}
}

void UDSRevokeLibrary::ConstructRevokeData_CurrentHouse_Area(UDSRevokeData* InRevokeDataPtr)
{
	checkf(InRevokeDataPtr != nullptr, TEXT("command core revoke data is nullptr"));

	InRevokeDataPtr->ModelExecuteType = FDSModelExecuteType::ExecuteAll;

	TArray<UDSBaseModel*> RevokeModel = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_House_Area });
	for (auto AM : RevokeModel)
	{
		if (DS_MODEL_VALID_FOR_USE(AM))
		{
			FDSRevokeDataInner Temp = ConstructRevokeInnerData(AM);
			InRevokeDataPtr->AddData(Temp);
		}
	}
}

FDSRevokeDataInner UDSRevokeLibrary::ConstructRevokeInnerData(UDSBaseModel* InEditModel)
{
	FDSRevokeDataInner Res;
	Res.SetUUID(InEditModel->GetUUID());
	Res.SetModelType(InEditModel->GetModelType());
	if (!Res.GetPropertyRef().IsValid())
	{
		if (Res.GetModelType() == EDSModelType::E_House_Wall)
		{
			Res.GetPropertyRef() = MakeShared<FDSHousePathProperty>();
			Res.GetPropertyRef()->CopyData(InEditModel->GetProperty());
		}
		else if (Res.GetModelType() == EDSModelType::E_House_Area)
		{
			Res.GetPropertyRef() = MakeShared<FDSHouseAreaProperty>();
			Res.GetPropertyRef()->CopyData(InEditModel->GetProperty());
		}
		else if (Res.GetModelType() == EDSModelType::E_House_Pillar)
		{
			Res.GetPropertyRef() = MakeShared<FDSPillarProperty>();
			Res.GetPropertyRef()->CopyData(InEditModel->GetProperty());
		}
		else if (Res.GetModelType() == EDSModelType::E_House_Beam)
		{
			Res.GetPropertyRef() = MakeShared<FDSHousePathProperty>();
			Res.GetPropertyRef()->CopyData(InEditModel->GetProperty());
		}
		else if (Res.GetModelType() == EDSModelType::E_House_Platform)
		{
			Res.GetPropertyRef() = MakeShared<FDSHousePathProperty>();
			Res.GetPropertyRef()->CopyData(InEditModel->GetProperty());
		}
		else if (Res.GetModelType() == EDSModelType::E_House_Door
			|| Res.GetModelType() == EDSModelType::E_House_Window)
		{
			Res.GetPropertyRef() = MakeShared<FDSDoorAndWindowProperty>();
			Res.GetPropertyRef()->CopyData(InEditModel->GetProperty());
			UDSBaseModel* LinkModel = Cast<UDSDoorAndWindowBaseModel>(InEditModel)->GetLinkWall();
			if (DS_MODEL_VALID_FOR_USE(LinkModel))
			{
				Res.SetLinkUUID(LinkModel->GetUUID());
				Res.SetLinkModelType(LinkModel->GetModelType());
			}
		}
		else if (Res.GetModelType() == EDSModelType::E_MultiSelect)
		{
			Res.GetPropertyRef() = MakeShared<FDSBaseProperty>();
			Res.GetPropertyRef()->CopyData(InEditModel->GetProperty());
			if (UDSMultiModel* CurModel = Cast<UDSMultiModel>(InEditModel))
			{
				for (auto& IM : CurModel->GetIncludeModel())
				{
					if (!DS_MODEL_VALID_FOR_USE(IM))
					{
						continue;
					}

					FDSRevokeModelInner& NewData = Res.ModelData.IncludeModelDatas.AddDefaulted_GetRef();
					ConstructRevokeModelInnerData(IM, NewData);
				}
			}
		}
		else if (Res.GetModelType() == EDSModelType::E_Generated_CounterTop)
		{
			Res.GetPropertyRef() = MakeShared<FDSCounterTopProperty>();
			Res.GetPropertyRef()->CopyData(InEditModel->GetProperty());
		}
	}
	return Res;
}

void UDSRevokeLibrary::ConstructRevokeInnerData(UDSBaseModel* InEditModel, FDSRevokeDataInner& OutRevokeData)
{
	if (DS_MODEL_VALID_FOR_USE(InEditModel))
	{
	}
}

void UDSRevokeLibrary::ConstructRevokeModelData(UDSBaseModel* InEditModel, FDSRevokeModelData& OutModelData)
{
	if (DS_MODEL_VALID_FOR_USE(InEditModel))
	{
	}
}

void UDSRevokeLibrary::ConstructRevokeModelInnerData(UDSBaseModel* InEditModel, FDSRevokeModelInner& OutModelData)
{
	if (DS_MODEL_VALID_FOR_USE(InEditModel))
	{
		OutModelData.SetUUID(InEditModel->GetUUID());
		OutModelData.SetModelType(InEditModel->GetModelType());
		if (!OutModelData.GetPropertyRef().IsValid())
		{
			if (UDSToolLibrary::IsCustomCabinetType(OutModelData.GetModelType()))
			{
				//cupboard
				OutModelData.GetPropertyRef() = MakeShared<FCupboardProperty>();
				OutModelData.GetPropertyRef()->CopyData(InEditModel->GetProperty());
			}
			else if (OutModelData.GetModelType() == EDSModelType::E_Furniture_HouseFurniture)
			{
				//soft furniture
				OutModelData.GetPropertyRef() = MakeShared<FDSSoftFurnitureProperty>();
				OutModelData.GetPropertyRef()->CopyData(InEditModel->GetProperty());
			}
		}
	}
}

void UDSRevokeLibrary::ConstructRevokeModelData_Custom(UDSBaseModel* InEditModel, UDSRevokeData*& RevokeDataPtr)
{
	if (DS_MODEL_VALID_FOR_USE(InEditModel) && Cast<UDSCupboardModel>(InEditModel) && Cast<UDSCustomRevokeData>(RevokeDataPtr))
	{
		const UDSBaseModel* OwnerModel = UDSToolLibrary::GetOwnerModelRecursion(InEditModel);
		if (OwnerModel != nullptr && OwnerModel != InEditModel)
		{
			Cast<UDSCustomRevokeData>(RevokeDataPtr)->UpdateBelongModelBaseData(OwnerModel->GetUUID(), OwnerModel->GetModelType());
		}
		Cast<UDSCustomRevokeData>(RevokeDataPtr)->UpdateModelBaseData(InEditModel->GetUUID(), InEditModel->GetModelType(), InEditModel->GetPropertySharedPtr());
		Cast<UDSCustomRevokeData>(RevokeDataPtr)->SetCupboardModelInfo(Cast<UDSCupboardModel>(InEditModel)->GetModelInfo());

		Cast<UDSCustomRevokeData>(RevokeDataPtr)->NodeRecords = Cast<UDSCupboardModel>(InEditModel)->GetNodeRecords();
		Cast<UDSCustomRevokeData>(RevokeDataPtr)->NodesFromAssociation = Cast<UDSCupboardModel>(InEditModel)->GetFromAssociationNodes();
	}
}

void UDSRevokeLibrary::ConstructRevokeModelData_Custom(UDSBaseModel* InEditModel, UDSCustomRevokeData* PreRevokeDataPtr, UDSRevokeData*& RevokeDataPtr)
{
	if (DS_MODEL_VALID_FOR_USE(InEditModel) && Cast<UDSCupboardModel>(InEditModel) && Cast<UDSCustomRevokeData>(RevokeDataPtr) && PreRevokeDataPtr != nullptr)
	{
		const UDSBaseModel* OwnerModel = UDSToolLibrary::GetOwnerModelRecursion(InEditModel);
		if (OwnerModel != nullptr && OwnerModel != InEditModel)
		{
			Cast<UDSCustomRevokeData>(RevokeDataPtr)->UpdateBelongModelBaseData(OwnerModel->GetUUID(), OwnerModel->GetModelType());
		}
		Cast<UDSCustomRevokeData>(RevokeDataPtr)->UpdateModelBaseData(InEditModel->GetUUID(), InEditModel->GetModelType(), InEditModel->GetPropertySharedPtr());
		Cast<UDSCustomRevokeData>(RevokeDataPtr)->SetCupboardModelInfo(Cast<UDSCupboardModel>(InEditModel)->GetModelInfo());
		if (PreRevokeDataPtr->IsComponentDataValid())
		{
			TSharedPtr<FDSCustomComponentRevokeData> ComponentRevokeData = MakeShared<FDSCustomComponentRevokeData>();
			TSharedPtr<FMultiComponentDataItem> PreData = PreRevokeDataPtr->GetComponentDataItemRef();
			EDSRevokeComponentType OppositeType = UDSRevokeLibrary::GetOppositeComponentCommandType(PreRevokeDataPtr->GetComponentData()->ComponentCommandType);
			TSharedPtr<FDSCustomComponentRevokeData> PreComponentRevokeData = PreRevokeDataPtr->GetComponentData();
			if (PreComponentRevokeData->NodePath.IsEmpty())
			{//this model all is a component
				ComponentRevokeData->SetComponentData(Cast<UDSCupboardModel>(InEditModel)->GetModelInfoRef().ComponentTreeData, true);
				ComponentRevokeData->SetComponentCommandType(OppositeType);
				ComponentRevokeData->SetNodePath(TEXT(""));
			}
			else
			{
				TSharedPtr<FMultiComponentDataItem> CurComponentData;
				Cast<UDSCupboardModel>(InEditModel)->GetDataByNodePathRet(Cast<UDSCupboardModel>(InEditModel)->GetModelInfo().ComponentTreeData, PreComponentRevokeData->NodePath, CurComponentData);
				if (CurComponentData.IsValid())
				{
					ComponentRevokeData->SetComponentData(CurComponentData, true);
				}
				else
				{
					ComponentRevokeData->SetComponentData(PreData, true);
				}
				ComponentRevokeData->SetComponentCommandType(OppositeType);
				ComponentRevokeData->SetNodePath(PreComponentRevokeData->NodePath);
			}
			Cast<UDSCustomRevokeData>(RevokeDataPtr)->SetComponentData(ComponentRevokeData);


			/*TSharedPtr<FMultiComponentDataItem> PreData = PreRevokeDataPtr->GetComponentDataItemRef();
			EDSRevokeComponentType OppositeType = UDSRevokeLibrary::GetOppositeComponentCommandType(PreRevokeDataPtr->GetComponentData()->ComponentCommandType);
			const int32 NodeIndex = Cast<UDSCupboardModel>(InEditModel)->GetModelInfo().ComponentTreeData->ChildComponent.IndexOfByPredicate(
				[&PreData](const TSharedPtr<FMultiComponentDataItem>& CCP)->bool
				{
					return PreData == CCP;
				}
			);
			if (NodeIndex != INDEX_NONE)
			{
				Cast<UDSCustomRevokeData>(RevokeDataPtr)->SetComponentData(
					Cast<UDSCupboardModel>(InEditModel)->GetModelInfo().ComponentTreeData->ChildComponent[NodeIndex],
					OppositeType
				);
			}
			else
			{
				Cast<UDSCustomRevokeData>(RevokeDataPtr)->SetComponentData(PreData, OppositeType);
			}*/
		}
	}
}

void UDSRevokeLibrary::ConstructRevokeModelData_Custom(UDSBaseModel* InEditModel,
	const TSharedPtr<FMultiComponentDataItem>& InComponentData, const EDSRevokeComponentType& InComponentCommandType,
	UDSRevokeData*& RevokeDataPtr)
{
	if (DS_MODEL_VALID_FOR_USE(InEditModel) && Cast<UDSCupboardModel>(InEditModel) && Cast<UDSCustomRevokeData>(RevokeDataPtr))
	{
		const UDSBaseModel* OwnerModel = UDSToolLibrary::GetOwnerModelRecursion(InEditModel);
		if (OwnerModel != nullptr && OwnerModel != InEditModel)
		{
			Cast<UDSCustomRevokeData>(RevokeDataPtr)->UpdateBelongModelBaseData(OwnerModel->GetUUID(), OwnerModel->GetModelType());
		}
		Cast<UDSCustomRevokeData>(RevokeDataPtr)->UpdateModelBaseData(InEditModel->GetUUID(), InEditModel->GetModelType(), InEditModel->GetPropertySharedPtr());
		Cast<UDSCustomRevokeData>(RevokeDataPtr)->SetCupboardModelInfo(Cast<UDSCupboardModel>(InEditModel)->GetModelInfo());
		Cast<UDSCustomRevokeData>(RevokeDataPtr)->SetComponentData(InComponentData, InComponentCommandType);
	}
}

void UDSRevokeLibrary::ConstructRevokeModelData_Custom(UDSBaseModel* InEditModel, const TSharedPtr<FDSCustomComponentRevokeData>& InComponentData, UDSRevokeData*& RevokeDataPtr)
{
	if (DS_MODEL_VALID_FOR_USE(InEditModel) && Cast<UDSCupboardModel>(InEditModel) && Cast<UDSCustomRevokeData>(RevokeDataPtr))
	{
		const UDSBaseModel* OwnerModel = UDSToolLibrary::GetOwnerModelRecursion(InEditModel);
		if (OwnerModel != nullptr && OwnerModel != InEditModel)
		{
			Cast<UDSCustomRevokeData>(RevokeDataPtr)->UpdateBelongModelBaseData(OwnerModel->GetUUID(), OwnerModel->GetModelType());
		}
		Cast<UDSCustomRevokeData>(RevokeDataPtr)->UpdateModelBaseData(InEditModel->GetUUID(), InEditModel->GetModelType(), InEditModel->GetPropertySharedPtr());
		Cast<UDSCustomRevokeData>(RevokeDataPtr)->SetCupboardModelInfo(Cast<UDSCupboardModel>(InEditModel)->GetModelInfo());
		Cast<UDSCustomRevokeData>(RevokeDataPtr)->SetComponentData(InComponentData);
		Cast<UDSCustomRevokeData>(RevokeDataPtr)->SetDependencyInfo(UDSModelDependencySubsystem::GetInstance()->GetCupboardDependencyInfo(InEditModel->GetUUID()));
	}

}

void UDSRevokeLibrary::ConstructCustomRevokeLogic(UDSBaseModel* InEditModel, const TSharedPtr<FDSCustomComponentRevokeData>& InComponentData, UDSCustomRevokeData*& RevokeDataPtr)
{
	if (DS_MODEL_VALID_FOR_USE(InEditModel) && Cast<UDSCupboardModel>(InEditModel))
	{
		const UDSBaseModel* OwnerModel = UDSToolLibrary::GetOwnerModelRecursion(InEditModel);
		if (OwnerModel != nullptr && OwnerModel != InEditModel)
		{
			RevokeDataPtr->UpdateBelongModelBaseData(OwnerModel->GetUUID(), OwnerModel->GetModelType());
		}
		RevokeDataPtr->UpdateModelBaseData(InEditModel->GetUUID(), InEditModel->GetModelType(), InEditModel->GetPropertySharedPtr());
		RevokeDataPtr->SetCupboardModelInfo(Cast<UDSCupboardModel>(InEditModel)->GetModelInfo());
		RevokeDataPtr->SetComponentData(InComponentData);
		RevokeDataPtr->SetDependencyInfo(UDSModelDependencySubsystem::GetInstance()->GetCupboardDependencyInfo(InEditModel->GetUUID()));
		RevokeDataPtr->NodeRecords = Cast<UDSCupboardModel>(InEditModel)->GetNodeRecords();
		RevokeDataPtr->NodesFromAssociation = Cast<UDSCupboardModel>(InEditModel)->GetFromAssociationNodes();

		if (RevokeDataPtr->ModelExecuteType.IsDeleteExecute())
		{
	
			//linked sink
			FString RelativeSink = UDSModelDependencySubsystem::GetInstance()->FindSinkByCupboard(InEditModel->GetUUID());
			if (!RelativeSink.IsEmpty())
			{
				if (UDSBaseModel* SinkModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Custom_Sink, RelativeSink))
				{
					UDSSinkRevokeData* SinkRevokeData = NewObject<UDSSinkRevokeData>();
					UDSRevokeLibrary::ConstructRevokeData_Sink(SinkModel, SinkRevokeData);

					RevokeDataPtr->LinkedData.Add(SinkRevokeData);
				}
			}

			//linked stove

			FString RelativeStove = UDSModelDependencySubsystem::GetInstance()->FindStoveByCupboard(InEditModel->GetUUID());
			if (!RelativeStove.IsEmpty())
			{
				if (UDSBaseModel* StoveModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Custom_Stove, RelativeStove))
				{
					UDSSRHRevokeData* SRHRevokeData = NewObject<UDSSRHRevokeData>();
					UDSRevokeLibrary::ConstructRevokeData_StoveRangeHood(StoveModel, SRHRevokeData);

					RevokeDataPtr->LinkedData.Add(SRHRevokeData);
				}
			}
		}

	}
}

TSharedPtr<FDSCustomComponentRevokeData> UDSRevokeLibrary::ConstructComponentRevokeData_Custom(UDSBaseModel* InEditModel, const FDSModelExecuteType& InExecuteType)
{
	TSharedPtr<FDSCustomComponentRevokeData> Ret = nullptr;
	if (DS_MODEL_VALID_FOR_USE(InEditModel) && Cast<UDSCupboardModel>(InEditModel) && InEditModel->GetOwnerModel() != nullptr && Cast<UDSCupboardModel>(InEditModel->GetOwnerModel()))
	{
		UDSCupboardModel* OwnerModel = Cast<UDSCupboardModel>(InEditModel->GetOwnerModel());
		Ret = MakeShared<FDSCustomComponentRevokeData>();
		Ret->SetComponentCommandType(UDSRevokeLibrary::ConvertComponentExecuteTypeFromAction(InExecuteType));
		EDSModelType EditType = InEditModel->GetModelType();
		TSharedPtr<FMultiComponentDataItem> ConsiderComponentData = Cast<UDSCupboardModel>(InEditModel)->GetModelInfo().ComponentTreeData;
		FString NodePath = OwnerModel->GetComponentTreePath(ConsiderComponentData);

		if (EditType == EDSModelType::E_Custom_SwingDoor || EditType == EDSModelType::E_Custom_SlidingDoor
			|| EditType == EDSModelType::E_Custom_FlipUpDoor || EditType == EDSModelType::E_Custom_FlipDownDoor)
		{// 定制门需要取门容器层级
			TArray<TSharedPtr<FMultiComponentDataItem>> PathComponent;
			if (OwnerModel->CollectComponentPath_Public(OwnerModel->GetComponentTreeDataRef(), ConsiderComponentData, PathComponent))
			{
				ConsiderComponentData = PathComponent.Last(1);
				NodePath = OwnerModel->GetComponentTreePath(ConsiderComponentData);
			}

			FString ModelInfoUUID = Cast<UDSCupboardModel>(InEditModel)->GetModelInfo().GetModelUUID();
			if (!ModelInfoUUID.IsEmpty() && UDSModelDependencySubsystem::GetInstance()->IsGeneratedDoor(ModelInfoUUID))
			{
				FDSDoorDependencyInfo DoorDependencyInfo = UDSModelDependencySubsystem::GetInstance()->FindDoorDependencyInfo(ModelInfoUUID);
				if (DoorDependencyInfo.IsValid())
				{
					Ret->SetDoorDependencyInfo(DoorDependencyInfo);
				}
			}
		}
		Ret->SetComponentData(ConsiderComponentData, true);
		Ret->SetNodePath(NodePath);
	}
	return Ret;
}

// TSharedPtr<FDSCustomComponentRevokeData> UDSRevokeLibrary::ConstructComponentRevokeData_Custom(UDSBaseModel* InEditModel, const EActionCommandType& InCommandType)
// {
// 	TSharedPtr<FDSCustomComponentRevokeData> Ret = nullptr;
// 	if (DS_MODEL_VALID_FOR_USE(InEditModel) && Cast<UDSCupboardModel>(InEditModel) && InEditModel->GetOwnerModel() != nullptr && Cast<UDSCupboardModel>(InEditModel->GetOwnerModel()))
// 	{
// 		UDSCupboardModel* OwnerModel = Cast<UDSCupboardModel>(InEditModel->GetOwnerModel());
// 		Ret = MakeShared<FDSCustomComponentRevokeData>();
// 		Ret->SetComponentCommandType(UDSRevokeLibrary::ConvertComponentCommandTypeFromAction(InCommandType));
// 		EDSModelType EditType = InEditModel->GetModelType();
// 		TSharedPtr<FMultiComponentDataItem> ConsiderComponentData = Cast<UDSCupboardModel>(InEditModel)->GetModelInfo().ComponentTreeData;
// 		FString NodePath = OwnerModel->GetComponentTreePath(ConsiderComponentData);
//
// 		if (EditType == EDSModelType::E_Custom_SwingDoor || EditType == EDSModelType::E_Custom_SlidingDoor
// 			|| EditType == EDSModelType::E_Custom_FlipUpDoor || EditType == EDSModelType::E_Custom_FlipDownDoor)
// 		{// 定制门需要取门容器层级
// 			TArray<TSharedPtr<FMultiComponentDataItem>> PathComponent;
// 			if (OwnerModel->CollectComponentPath_Public(OwnerModel->GetComponentTreeDataRef(), ConsiderComponentData, PathComponent))
// 			{
// 				ConsiderComponentData = PathComponent.Last(1);
// 				NodePath = OwnerModel->GetComponentTreePath(ConsiderComponentData);
// 			}
//
// 			FString ModelInfoUUID = Cast<UDSCupboardModel>(InEditModel)->GetModelInfo().GetModelUUID();
// 			if (!ModelInfoUUID.IsEmpty() && UDSModelDependencySubsystem::GetInstance()->IsGeneratedDoor(ModelInfoUUID))
// 			{
// 				FDSDoorDependencyInfo DoorDependencyInfo = UDSModelDependencySubsystem::GetInstance()->FindDoorDependencyInfo(ModelInfoUUID);
// 				if (DoorDependencyInfo.IsValid())
// 				{
// 					Ret->SetDoorDependencyInfo(DoorDependencyInfo);
// 				}
// 			}
// 		}
// 		Ret->SetComponentData(ConsiderComponentData, true);
// 		Ret->SetNodePath(NodePath);
// 	}
// 	return Ret;
// }

// void UDSRevokeLibrary::CompareHouseData(const UDSRevokeData& InRevokeData)
// {
// 	TArray<UDSBaseModel*> CurrentHouseModel = UDSRevokeLibrary::GetCurrentHouseModel();
// 	TArray<UDSBaseModel*> CurrentMoreModel;
// 	TArray<FDSRevokeDataInner> CurrentLessData = InRevokeData.RevokeData;
// 	TArray<int32> DeleteIndex;
// 	for (int32 i = 0; i < InRevokeData.RevokeData.Num(); ++i)
// 	{
// 		const int32 Index = CurrentHouseModel.IndexOfByPredicate(
// 			[&](UDSBaseModel* InModel)
// 			{
// 				if (!DS_MODEL_VALID_FOR_USE(InModel))
// 				{
// 					return false;
// 				}
// 				return InModel->GetUUID().Equals(InRevokeData.RevokeData[i].GetUUID());
// 			}
// 		);
// 		if (Index != INDEX_NONE)
// 		{
// 			UDSRevokeLibrary::RefreshModel(CurrentHouseModel[Index], InRevokeData.RevokeData[i]);
//
// 			CurrentHouseModel.RemoveAt(Index);
// 			DeleteIndex.Insert(i, 0);
// 		}
// 	}
// 	for (auto DI : DeleteIndex)
// 	{
// 		CurrentLessData.RemoveAt(DI);
// 	}
//
// 	/*
// 	 *  @@ delete current scene more model
// 	 */
// 	UDSRevokeLibrary::DeleteMoreHouseData_Inner(CurrentHouseModel);
//
// 	/*
// 	 *  @@ spawn current scene less model by revoke data
// 	 */
// 	UDSRevokeLibrary::SpawnLessHouseData_Inner(CurrentLessData);
// }

void UDSRevokeLibrary::CompareHouseData(const UDSRevokeData* InRevokeData)
{
	if (InRevokeData == nullptr)
	{
		return;
	}
	TArray<UDSBaseModel*> CurrentHouseModel = GetCurrentHouseModel();
	TArray<UDSBaseModel*> CurrentMoreModel;
	TArray<FDSRevokeDataInner> CurrentLessData = InRevokeData->RevokeData;
	TArray<int32> DeleteIndex;
	for (int32 i = 0; i < InRevokeData->RevokeData.Num(); ++i)
	{
		const int32 Index = CurrentHouseModel.IndexOfByPredicate(
			[&](UDSBaseModel* InModel)
			{
				if (!DS_MODEL_VALID_FOR_USE(InModel))
				{
					return false;
				}
				return InModel->GetUUID().Equals(InRevokeData->RevokeData[i].GetUUID());
			}
		);
		if (Index != INDEX_NONE)
		{
			RefreshModel(CurrentHouseModel[Index], InRevokeData->RevokeData[i], true);

			CurrentHouseModel.RemoveAt(Index);
			DeleteIndex.Insert(i, 0);
		}
	}
	for (auto DI : DeleteIndex)
	{
		CurrentLessData.RemoveAt(DI);
	}

	/*
	 *  @@ delete current scene more model
	 */
	DeleteMoreHouseData_Inner(CurrentHouseModel);

	/*
	 *  @@ spawn current scene less model by revoke data
	 */
	SpawnLessHouseData_Inner(CurrentLessData);
}

void UDSRevokeLibrary::CompareHouseData_WinDoor(const UDSRevokeData* InRevokeData)
{
	if (InRevokeData == nullptr)
	{
		return;
	}
	TArray<UDSBaseModel*> CurrentHouseModel = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_House_Window, EDSModelType::E_House_Door });
	TArray<UDSBaseModel*> CurrentMoreModel;
	TArray<FDSRevokeDataInner> CurrentLessData = InRevokeData->RevokeData;
	TArray<int32> DeleteIndex;
	for (int32 i = 0; i < InRevokeData->RevokeData.Num(); ++i)
	{
		const int32 Index = CurrentHouseModel.IndexOfByPredicate(
			[&](UDSBaseModel* InModel)
			{
				if (!DS_MODEL_VALID_FOR_USE(InModel))
				{
					return false;
				}
				return InModel->GetUUID().Equals(InRevokeData->RevokeData[i].GetUUID());
			}
		);
		if (Index != INDEX_NONE)
		{
			RefreshModel(CurrentHouseModel[Index], InRevokeData->RevokeData[i], true);

			CurrentHouseModel.RemoveAt(Index);
			DeleteIndex.Insert(i, 0);
		}
	}
	for (auto DI : DeleteIndex)
	{
		CurrentLessData.RemoveAt(DI);
	}

	/*
	 *  @@ delete current scene more model
	 */
	DeleteMoreHouseData_Inner(CurrentHouseModel);

	/*
	 *  @@ spawn current scene less model by revoke data
	 */
	SpawnLessHouseData_Inner(CurrentLessData);
}

void UDSRevokeLibrary::CompareHouseData_Area(const UDSRevokeData* InRevokeData)
{
	if (InRevokeData == nullptr)
	{
		return;
	}
	TArray<UDSBaseModel*> CurrentHouseModel = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_House_Area });
	TArray<UDSBaseModel*> CurrentMoreModel;
	TArray<FDSRevokeDataInner> CurrentLessData = InRevokeData->RevokeData;
	TArray<int32> DeleteIndex;
	for (int32 i = 0; i < InRevokeData->RevokeData.Num(); ++i)
	{
		const int32 Index = CurrentHouseModel.IndexOfByPredicate(
			[&](UDSBaseModel* InModel)
			{
				if (!DS_MODEL_VALID_FOR_USE(InModel))
				{
					return false;
				}
				return InModel->GetUUID().Equals(InRevokeData->RevokeData[i].GetUUID());
			}
		);
		if (Index != INDEX_NONE)
		{
			RefreshModel(CurrentHouseModel[Index], InRevokeData->RevokeData[i], true);

			CurrentHouseModel.RemoveAt(Index);
			DeleteIndex.Insert(i, 0);
		}
	}
	for (auto DI : DeleteIndex)
	{
		CurrentLessData.RemoveAt(DI);
	}

	/*
	 *  @@ delete current scene more model
	 */
	DeleteMoreHouseData_Inner(CurrentHouseModel);

	/*
	 *  @@ spawn current scene less model by revoke data
	 */
	SpawnLessHouseData_Inner(CurrentLessData);
}

TArray<UDSBaseModel*> UDSRevokeLibrary::GetCurrentHouseModel()
{
	TArray<UDSBaseModel*> HouseModel = UDSMVCSubsystem::GetInstance()->GetModels(RevokeConsiderAsHouseType);
	return HouseModel;
}

void UDSRevokeLibrary::RefreshModel(UDSBaseModel* InEditModel, const FDSRevokeDataInner& InRevokeData, bool OnlyData /*= false*/)
{
	if (DS_MODEL_VALID_FOR_USE(InEditModel)
		&& InEditModel->GetUUID().Equals(InRevokeData.GetUUID())
		&& InEditModel->GetModelType() == InRevokeData.GetModelType())
	{
		UDSToolLibrary::ParsePropertyCopy(InEditModel, InRevokeData.GetProperty());

		FDSModelExecuteType HiddenExecute = InEditModel->GetPropertySharedPtr()->GetIsHidden() ? FDSModelExecuteType::ExecuteHidden : FDSModelExecuteType::ExecuteUnHidden;
		InEditModel->GetModelHiddenFlags().SetActiveHiddenFlag(InEditModel->GetPropertySharedPtr()->GetIsHidden());
		InEditModel->OnExecuteAction(HiddenExecute, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);

		if (!OnlyData)
		{
			InEditModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
		}
	}
}

void UDSRevokeLibrary::RefreshModel_Custom(UDSBaseModel* InEditModel, const UDSCustomRevokeData* InRevokeData)
{
	if (DS_MODEL_VALID_FOR_USE(InEditModel) && Cast<UDSCupboardModel>(InEditModel) && InRevokeData != nullptr
		&& InEditModel->GetUUID().Equals(InRevokeData->GetModelBaseData().GetUUID())
		&& InEditModel->GetModelType() == InRevokeData->GetModelBaseData().GetModelType())
	{
		//Cast<UDSCupboardModel>(InEditModel)->SetModelInfo(InRevokeData->GetCupboardModelInfo());
		Cast<UDSCupboardModel>(InEditModel)->GetModelInfoRef().ComponentTreeData->ComponentParameters = InRevokeData->GetCupboardModelInfo().ComponentTreeData->ComponentParameters;

		//belong model
		if (InRevokeData->GetBelongModelBaseData().IsValid())
		{
			UDSBaseModel* BelongModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(
				InRevokeData->GetBelongModelBaseData().GetModelType(), InRevokeData->GetBelongModelBaseData().GetUUID());
			if (DS_MODEL_VALID_FOR_USE(BelongModel))
			{
				InEditModel->SetOwnerModel(BelongModel);
			}
		}

		//component
		if (InRevokeData->IsComponentDataValid())
		{
			TSharedPtr<FDSCustomComponentRevokeData> CompData = InRevokeData->GetComponentData();
			if (CompData->ComponentCommandType == EDSRevokeComponentType::E_Component_Add)
			{
				auto& CompTreeData = Cast<UDSCupboardModel>(InEditModel)->GetModelInfoRef().ComponentTreeData;
				if (CompTreeData->ChildComponent.Contains(CompData->ComponentData))
				{
					CompTreeData->ChildComponent.Remove(CompData->ComponentData);
				}
				else
				{
					if (!CompData->NodePath.IsEmpty() && CompTreeData->ChildComponent.IsValidIndex(FCString::Atoi(*CompData->NodePath)))
					{
						CompTreeData->ChildComponent.RemoveAt(FCString::Atoi(*CompData->NodePath));
					}
				}
			}
			else if (CompData->ComponentCommandType == EDSRevokeComponentType::E_Component_Delete)
			{
				Cast<UDSCupboardModel>(InEditModel)->GetModelInfoRef().ComponentTreeData->ChildComponent.Add(CompData->ComponentData);
			}
			else if (CompData->ComponentCommandType == EDSRevokeComponentType::E_Component_Update)
			{
				if (CompData->NodePath.IsEmpty())
				{
					Cast<UDSCupboardModel>(InEditModel)->GetModelInfoRef().ComponentTreeData = CompData->ComponentData;
				}
				else
				{
					Cast<UDSCupboardModel>(InEditModel)->ParseDataByNodePath(CompData->ComponentData, CompData->NodePath);
				}
			}
			else if (CompData->ComponentCommandType == EDSRevokeComponentType::E_Component_CustomReplace)
			{
				Cast<UDSCupboardModel>(InEditModel)->SetModelInfo(InRevokeData->GetCupboardModelInfo());
			}
			else if (CompData->ComponentCommandType == EDSRevokeComponentType::E_Component_MatReplace)
			{
				//参数上边逻辑已覆盖
			}
			else if (CompData->ComponentCommandType == EDSRevokeComponentType::E_Component_ModelReplace)
			{
			}
		}

		//property
		UDSToolLibrary::ParsePropertyCopy(InEditModel, InRevokeData->GetModelBaseData().GetPropertyRef());


		/*
		 *  @@ dependency info
		 *  @@ redirect dependency info
		 */
		FCupboardDependencyInfo RevokeDependencyInfo = InRevokeData->GetDependencyInfo();
		for (auto& RDI : RevokeDependencyInfo.GetNodesRef())
		{
			//TODO : add model type 
			if (!RDI.UUID.IsEmpty() && !RDI.CupboardModelPtr.IsValid())
			{
				UE_LOG(DSRevokeLibraryLog, Log, TEXT("[%s] -- uuid [%s] pointer to model is not valid"), *FString(__FUNCTION__), *RDI.UUID);
				//UDSBaseModel* RedirectModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID()
			}
		}
		UDSModelDependencySubsystem::GetInstance()->UpdateCupboardDependencyInfo(InEditModel->GetUUID(), RevokeDependencyInfo);
	}
}

void UDSRevokeLibrary::DeleteMoreHouseData_Inner(TArray<UDSBaseModel*> MoreModel)
{
	for (auto MM : MoreModel)
	{
		if (DS_MODEL_VALID_FOR_USE(MM))
		{
			if (MM->GetModelType() == EDSModelType::E_House_Door
				|| MM->GetModelType() == EDSModelType::E_House_Window)
			{
				if (Cast<UDSDoorAndWindowBaseModel>(MM))
				{
					UDSBaseModel* LinkModel = Cast<UDSDoorAndWindowBaseModel>(MM)->GetLinkWall();
					if (DS_MODEL_VALID_FOR_USE(LinkModel) && Cast<UDSHouseWallModel>(LinkModel))
					{
						Cast<UDSHouseWallModel>(LinkModel)->DeleteDoorAndWindow(MM);
					}
					MM->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
					if (DS_MODEL_VALID_FOR_USE(LinkModel))
					{
						LinkModel->OnExecuteAction(FDSModelExecuteType::ExecuteAll, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
					}
				}
			}
			else
			{
				//if (RevokeConsiderAsHouseType.Contains(MM->GetModelType()))
				{
					MM->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
				}
			}
		}
	}
}

void UDSRevokeLibrary::SpawnLessHouseData_Inner(const TArray<FDSRevokeDataInner>& LessRevokeData)
{
	for (const auto& LRD : LessRevokeData)
	{
		UDSBaseModel* NewModel = nullptr;
		if (LRD.GetModelType() == EDSModelType::E_House_Wall)
		{
			NewModel = NewObject<UDSHouseWallModel>();
		}
		else if (LRD.GetModelType() == EDSModelType::E_House_Area)
		{
			NewModel = NewObject<UDSHouseAreaModel>();
		}
		else if (LRD.GetModelType() == EDSModelType::E_House_Beam)
		{
			NewModel = NewObject<UDSBeamModel>();
		}
		else if (LRD.GetModelType() == EDSModelType::E_House_Platform)
		{
			NewModel = NewObject<UDSPlatformModel>();
		}
		else if (LRD.GetModelType() == EDSModelType::E_House_Pillar)
		{
			NewModel = NewObject<UDSPillarModel>();
			NewModel->SetNoNewGenerate();
		}
		else if (LRD.GetModelType() == EDSModelType::E_House_Door)
		{
			NewModel = NewObject<UDSDoorModel>();
			NewModel->SetNoNewGenerate();
		}
		else if (LRD.GetModelType() == EDSModelType::E_House_Window)
		{
			NewModel = NewObject<UDSWindowModel>();
			NewModel->SetNoNewGenerate();
		}
		else if (LRD.GetModelType() == EDSModelType::E_Generated_CounterTop)
		{
			NewModel = NewObject<UDSCounterTopModel>();
			NewModel->SetNoNewGenerate();
		}

		if (DS_MODEL_VALID_FOR_USE(NewModel))
		{
			NewModel->SetUUID(LRD.GetUUID());
			UDSToolLibrary::ParsePropertyCopy(NewModel, LRD.GetProperty());
			UDSMVCSubsystem::GetInstance()->SpawnViewUnion(NewModel);
			if (LRD.HasLinkData())
			{
				UDSBaseModel* LinkModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(
					LRD.GetLinkModelType(), LRD.GetLinkUUID());
				if (DS_MODEL_VALID_FOR_USE(LinkModel))
				{
					if (LinkModel->GetModelType() == EDSModelType::E_House_Wall &&
						(NewModel->GetModelType() == EDSModelType::E_House_Window || NewModel->GetModelType() == EDSModelType::E_House_Door))
					{
						Cast<UDSHouseWallModel>(LinkModel)->AddDoorAndWindow(NewModel);
						Cast<UDSDoorAndWindowBaseModel>(NewModel)->AddLinkWall(LinkModel);
						LinkModel->OnExecuteAction(FDSModelExecuteType::ExecuteAll, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
					}
				}
			}
		}
	}
}

void UDSRevokeLibrary::SpawnLessMultiData(const TArray<FDSRevokeDataInner>& LessRevokeData)
{
	for (const auto& LRD : LessRevokeData)
	{
		if (LRD.GetModelType() == EDSModelType::E_MultiSelect)
		{
			for (const auto& IMD : LRD.GetIncludeModelDatas())
			{
				if (!IMD.IsValid())
				{
				}
			}
		}
		else if (LRD.GetModelType() == EDSModelType::E_Group)
		{
		}
		// if (LRD.GetModelType() == EDSModelType::E_Custom_Furniture)
		// {
		// 	NewModel = NewObject<UDSCupboardModel>();
		// }
		// else if (LRD.GetModelType() == EDSModelType::E_Furniture_HouseFurniture)
		// {
		// 	NewModel = NewObject<UDSSoftFurnitureModel>();
		// }
		//
		// if (DS_MODEL_VALID_FOR_USE(NewModel))
		// {
		// 	NewModel->SetUUID(LRD.GetUUID());
		// 	UDSToolLibrary::ParsePropertyCopy(NewModel, LRD.GetProperty());
		// 	UDSMVCSubsystem::GetInstance()->OnGenerateView_NeedConfirm(NewModel);
		// }
	}
}

UDSBaseModel* UDSRevokeLibrary::SpawnLessCustomData(const UDSCustomRevokeData* LessRevokeData)
{
	UDSCupboardModel* NewModel = NewObject<UDSCupboardModel>();
	NewModel->SetNoNewGenerate();

	if (DS_MODEL_VALID_FOR_USE(NewModel))
	{
		//self info
		NewModel->SetUUID(LessRevokeData->GetModelBaseData().GetUUID());
		NewModel->SetModelType(LessRevokeData->GetModelBaseData().GetModelType());
		UDSToolLibrary::ParsePropertyCopy(NewModel, LessRevokeData->GetModelBaseData().GetPropertyRef());

		NewModel->SetModelInfo(LessRevokeData->GetCupboardModelInfo());

		UDSMVCSubsystem::GetInstance()->SpawnViewUnion(NewModel);

		//belong info
		UDSBaseModel* BelongModel = nullptr;
		if (LessRevokeData->GetBelongModelBaseData().IsValid())
		{
			BelongModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(
				LessRevokeData->GetBelongModelBaseData().GetModelType(), LessRevokeData->GetBelongModelBaseData().GetUUID()
			);
			if (BelongModel != nullptr)
			{
				NewModel->SetOwnerModel(BelongModel);

				//parent params 
				if (Cast<UDSCupboardModel>(BelongModel))
				{
					TMap<FString, FParameterData> ParentComponentParams;
					Cast<UDSCupboardModel>(BelongModel)->GetSelfComponentOverriderParametersRef(ParentComponentParams);
					NewModel->SetParentParams(ParentComponentParams);

					if (UDSCupboardLibrary::IsFunctionalCupboardModel(LessRevokeData->GetCupboardModelInfo().ComponentTreeData->ComponentParameters))
					{
						Cast<UDSCupboardModel>(BelongModel)->AddFunctionalCupboardModel(NewModel);
					}
				}
			}
		}

		//TODO : depend info
		if (LessRevokeData->ComponentDataPtr.IsValid() && LessRevokeData->ComponentDataPtr->GetDoorDependencyInfo().IsValid())
		{
			FString ModelInfoUUID = LessRevokeData->GetCupboardModelInfo().GetModelUUID();
			if (!ModelInfoUUID.IsEmpty())
			{
				UDSModelDependencySubsystem::GetInstance()->AddDoorDependencyInfo(ModelInfoUUID, LessRevokeData->ComponentDataPtr->GetDoorDependencyInfo());
			}

		}

		//refresh
		//if (!NeedBelongRefresh)
		{
			NewModel->UpdateDirect();
		}

		if (LessRevokeData->GroupMarkData.IsGroupItem())
		{
			UDSBaseModel* GroupModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(
				EDSModelType::E_Group, LessRevokeData->GroupMarkData.GetGroupUUID());
			if (GroupModel != nullptr && Cast<UDSGroupModel>(GroupModel))
			{
				Cast<UDSGroupModel>(GroupModel)->AddItem(NewModel, false);
			}
		}
	}

	return NewModel;
}

// UDSRevokeData UDSRevokeLibrary::ExecuteCommandRetOpposite_Single(const UDSRevokeData& InRevokeData)
// {
// 	UDSRevokeData OppositeData;
// 	OppositeData.CommandType = UDSRevokeLibrary::GetOppositeCommandType(InRevokeData.CommandType);
// 	if (InRevokeData.CommandType == EActionCommandType::E_Delete || InRevokeData.CommandType == EActionCommandType::E_DeleteSelf)
// 	{
// 		//delete ---> spawn
// 		UDSRevokeLibrary::SpawnLessHouseData_Inner(InRevokeData.RevokeData);
// 		OppositeData.RevokeData = InRevokeData.RevokeData;
// 	}
// 	else if (InRevokeData.CommandType == EActionCommandType::E_Spawn)
// 	{
// 		//spawn ---> delete
// 		TArray<UDSBaseModel*> ToDeleteModel;
// 		for (const auto& KD : InRevokeData.RevokeData)
// 		{
// 			UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(
// 				KD.GetModelType(), KD.GetUUID());
// 			if (DS_MODEL_VALID_FOR_USE(EditModel))
// 			{
// 				FDSRevokeDataInner ModelCurrentData = UDSRevokeLibrary::ConstructRevokeInnerData(EditModel);
// 				OppositeData.AddData(ModelCurrentData);
// 				ToDeleteModel.AddUnique(EditModel);
// 			}
// 		}
// 		UDSRevokeLibrary::DeleteMoreHouseData_Inner(ToDeleteModel);
// 	}
// 	else
// 	{
// 		for (const auto& KD : InRevokeData.RevokeData)
// 		{
// 			UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(
// 				KD.GetModelType(), KD.GetUUID());
// 			if (DS_MODEL_VALID_FOR_USE(EditModel))
// 			{
// 				FDSRevokeDataInner ModelCurrentData = UDSRevokeLibrary::ConstructRevokeInnerData(EditModel);
// 				OppositeData.AddData(ModelCurrentData);
//
//
// 				UDSRevokeLibrary::RefreshModel(EditModel, KD);
// 				EditModel->OnExecuteAction(OppositeData.CommandType, FDSBroadcastMarkData(true, false));
// 			}
// 		}
// 	}
// 	return OppositeData;
// }

void UDSRevokeLibrary::ExecuteCommandRetOpposite_Single(const UDSRevokeData* InRevokeDataPtr, UDSRevokeData* OutRevokeDataPtr)
{
	checkf(InRevokeDataPtr != nullptr, TEXT("ExecuteCommandRetOpposite_Single -- InRevokeDataPtr is nullptr"));
	checkf(OutRevokeDataPtr != nullptr, TEXT("ExecuteCommandRetOpposite_Single -- OutRevokeDataPtr is nullptr"));
	
	OutRevokeDataPtr->ModelExecuteType = GetOppositeExecuteType(InRevokeDataPtr->ModelExecuteType);
	if (InRevokeDataPtr->ModelExecuteType.IsDeleteExecute())
	{
		//delete ---> spawn
		SpawnLessHouseData_Inner(InRevokeDataPtr->RevokeData);
		OutRevokeDataPtr->RevokeData = InRevokeDataPtr->RevokeData;
	}
	else if (InRevokeDataPtr->ModelExecuteType.IsSpawnExecute())
	{
		//spawn ---> delete
		TArray<UDSBaseModel*> ToDeleteModel;
		for (const auto& KD : InRevokeDataPtr->RevokeData)
		{
			UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(
				KD.GetModelType(), KD.GetUUID());
			if (DS_MODEL_VALID_FOR_USE(EditModel))
			{
				FDSRevokeDataInner ModelCurrentData = ConstructRevokeInnerData(EditModel);
				OutRevokeDataPtr->AddData(ModelCurrentData);
				ToDeleteModel.AddUnique(EditModel);
			}
		}
		DeleteMoreHouseData_Inner(ToDeleteModel);
	}
	else
	{
		for (const auto& KD : InRevokeDataPtr->RevokeData)
		{
			UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(
				KD.GetModelType(), KD.GetUUID());
			if (DS_MODEL_VALID_FOR_USE(EditModel))
			{
				FDSRevokeDataInner ModelCurrentData = ConstructRevokeInnerData(EditModel);
				OutRevokeDataPtr->AddData(ModelCurrentData);
				
				RefreshModel(EditModel, KD, true);
				EditModel->OnExecuteAction(OutRevokeDataPtr->ModelExecuteType, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
			}
		}
	}
}

// UDSRevokeData UDSRevokeLibrary::ExecuteCommandRetOpposite_Multi(const UDSRevokeData& InRevokeData)
// {
// 	UDSRevokeData OppositeData;
// 	OppositeData.CommandType = UDSRevokeLibrary::GetOppositeCommandType(InRevokeData.CommandType);
// 	if (InRevokeData.CommandType == EActionCommandType::E_Delete || InRevokeData.CommandType == EActionCommandType::E_DeleteSelf)
// 	{
// 		//delete revoke to spawn
// 		UDSRevokeLibrary::SpawnLessMultiData(InRevokeData.RevokeData);
// 		OppositeData.RevokeData = InRevokeData.RevokeData;
// 	}
// 	else if (InRevokeData.CommandType == EActionCommandType::E_MultiAdd)
// 	{
// 		//multi select add revoke to release
// 	}
// 	else if (InRevokeData.CommandType == EActionCommandType::E_MultiRelease)
// 	{
// 		//multi select release revoke to add
// 	}
// 	else if (InRevokeData.CommandType == EActionCommandType::E_GroupCombine)
// 	{
// 		//group combine revoke to release 
// 	}
// 	else if (InRevokeData.CommandType == EActionCommandType::E_GroupRelease)
// 	{
// 		//group release revoke to combine
// 	}
// 	else
// 	{
// 	}
// 	return OppositeData;
// }

void UDSRevokeLibrary::ExecuteCommandRetOpposite_Multi(const UDSRevokeData* InRevokeDataPtr, UDSRevokeData* OutRevokeDataPtr)
{
	checkf(OutRevokeDataPtr != nullptr, TEXT("ExecuteCommandRetOpposite_Single -- OutRevokeDataPtr is nullptr"));

	//OutRevokeDataPtr->CommandType = GetOppositeCommandType(InRevokeDataPtr->CommandType);
	if (InRevokeDataPtr->ModelExecuteType.IsDeleteExecute())
	{
		//delete revoke to spawn
		SpawnLessMultiData(InRevokeDataPtr->RevokeData);
		OutRevokeDataPtr->RevokeData = InRevokeDataPtr->RevokeData;
	}
	else if (InRevokeDataPtr->ModelExecuteType == FDSModelExecuteType::ExecuteMultiAdd)
	{
		//multi select add revoke to release
	}
	else if (InRevokeDataPtr->ModelExecuteType == FDSModelExecuteType::ExecuteMultiRelease)
	{
		//multi select release revoke to add
	}
	else if (InRevokeDataPtr->ModelExecuteType == FDSModelExecuteType::ExecuteGroupCombine)
	{
		//group combine revoke to release 
	}
	else if (InRevokeDataPtr->ModelExecuteType == FDSModelExecuteType::ExecuteGroupRelease)
	{
		//group release revoke to combine
	}
	else
	{
	}
}

void UDSRevokeLibrary::ExecuteCommandRetOpposite_Custom(UDSRevokeData* InRevokeDataPtr, UDSRevokeData* OutRevokeDataPtr)
{
	checkf(InRevokeDataPtr != nullptr, TEXT("ExecuteCommandRetOpposite_Single -- InRevokeDataPtr is nullptr"));
	checkf(OutRevokeDataPtr != nullptr, TEXT("ExecuteCommandRetOpposite_Single -- OutRevokeDataPtr is nullptr"));
	if (Cast<UDSCustomRevokeData>(InRevokeDataPtr) == nullptr || Cast<UDSCustomRevokeData>(OutRevokeDataPtr) == nullptr)
	{
		return;
	}
	
	OutRevokeDataPtr->ModelExecuteType = GetOppositeExecuteType(InRevokeDataPtr->ModelExecuteType);
	if (InRevokeDataPtr->ModelExecuteType.IsDeleteExecute())
	{
		//delete ---> spawn
		UDSCustomRevokeData* Origin = Cast<UDSCustomRevokeData>(InRevokeDataPtr);
		SpawnLessCustomData(Origin);

		//linked
        for (auto& LinkedData : Origin->LinkedData)
		{
			if (LinkedData != nullptr)
			{
				if (LinkedData->IsA<UDSSinkRevokeData>())
				{
					SpawnLessSink(Cast<UDSSinkRevokeData>(LinkedData));
				}
				else if (LinkedData->IsA<UDSSRHRevokeData>())
				{
					SpawnLessStoveRangeHood(Cast<UDSSRHRevokeData>(LinkedData));
				}

			}
		}

		Origin->LinkedData.Empty();
		OutRevokeDataPtr->CopyData(InRevokeDataPtr);
	}
	else if (InRevokeDataPtr->ModelExecuteType.IsSpawnExecute())
	{
		//spawn ---> delete
		UDSCustomRevokeData* CRD = Cast<UDSCustomRevokeData>(InRevokeDataPtr);
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(
			CRD->GetModelBaseData().GetModelType(), CRD->GetModelBaseData().GetUUID());
		if (DS_MODEL_VALID_FOR_USE(EditModel))
		{
			ConstructRevokeModelData_Custom(EditModel, OutRevokeDataPtr);
			EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
		}
	}
	else
	{
		UDSCustomRevokeData* CRD = Cast<UDSCustomRevokeData>(InRevokeDataPtr);
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(
			CRD->GetModelBaseData().GetModelType(), CRD->GetModelBaseData().GetUUID());
		if (DS_MODEL_VALID_FOR_USE(EditModel) && Cast<UDSCupboardModel>(EditModel))
		{
			UDSRevokeLibrary::ConstructRevokeModelData_Custom(EditModel, CRD, OutRevokeDataPtr);
			FDSModelExecuteType ExecuteType = InRevokeDataPtr->ModelExecuteType;
			if (ExecuteType.IsLockExecute() || ExecuteType.IsHiddenExecute() || ExecuteType.IsTransformExecute())
			{
				UDSToolLibrary::ParsePropertyCopy(EditModel, CRD->GetModelBaseData().GetPropertyRef());
			}
			else if (ExecuteType.IsUpdateExecute())
			{
				UDSToolLibrary::ParsePropertyCopy(EditModel, CRD->GetModelBaseData().GetPropertyRef());
				EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteTransform, FDSBroadcastMarkData::NotBroadcastToMVCMark);
				UDSRevokeLibrary::RefreshModel_Custom(EditModel, CRD);
				Cast<UDSCupboardModel>(EditModel)->UpdateDirect();
			}

			EditModel->OnExecuteAction(OutRevokeDataPtr->ModelExecuteType, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
		}
	}
}

void UDSRevokeLibrary::ExecuteCommandRetOpposite_CT(UDSRevokeData* InRevokeDataPtr, UDSRevokeData* OutRevokeDataPtr)
{
	checkf(InRevokeDataPtr != nullptr, TEXT("ExecuteCommandRetOpposite_CT -- InRevokeDataPtr is nullptr"));
	checkf(OutRevokeDataPtr != nullptr, TEXT("ExecuteCommandRetOpposite_CT -- OutRevokeDataPtr is nullptr"));

	if (Cast<UDSCounterTopRevokeData>(InRevokeDataPtr) == nullptr || Cast<UDSCounterTopRevokeData>(OutRevokeDataPtr) == nullptr)
		return;

	EDSCTRevokeType OppositeCommand = UDSRevokeLibrary::GetOppositeCTCommandType(Cast<UDSCounterTopRevokeData>(InRevokeDataPtr)->RevokeType);
	UDSRevokeLibrary::ConstructOppositeRevokeData_CT(Cast<UDSCounterTopRevokeData>(InRevokeDataPtr), Cast<UDSCounterTopRevokeData>(OutRevokeDataPtr));

	if (OppositeCommand == EDSCTRevokeType::E_PropertyChange)
	{
		UDSCounterTopRevokeData* CounterTopRevokeData = Cast<UDSCounterTopRevokeData>(InRevokeDataPtr);

		if (CounterTopRevokeData->ModelType != EDSModelType::E_None && !CounterTopRevokeData->ModelUUID.IsEmpty())
		{
			UDSBaseModel* UpdateModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(CounterTopRevokeData->ModelType, CounterTopRevokeData->ModelUUID);
			if (IsValid(UpdateModel))
			{
				TSharedPtr<FDSBaseProperty> UpdateModelProperty = UpdateModel->GetPropertySharedPtr();
				switch (CounterTopRevokeData->ModelType)
				{
				case EDSModelType::E_Generated_CounterTop:
				{
					UpdateModelProperty->CopyData(CounterTopRevokeData->GetPropertyData());
				}
				break;
				case EDSModelType::E_Generated_SideCounterTop:
				{
					UpdateModelProperty->CopyData(CounterTopRevokeData->GetSidePropertyData());
				}
				break;
				case EDSModelType::E_Generated_CounterTop_Line:
				{
					UpdateModelProperty->CopyData(CounterTopRevokeData->GetLinePropertyData());
				}
				break;
				case EDSModelType::E_Generated_CounterTop_Point:
				{
					UpdateModelProperty->CopyData(CounterTopRevokeData->GetPointPropertyData());
				}
				break;
				default:
					break;
				}
			}
		}
		else
		{
			UDSCounterTopProperty* UE_CT_Property = NewObject<UDSCounterTopProperty>();
			UDSRevokeLibrary::ConstructBroadcastPropertyFromRevokeData(UE_CT_Property, CounterTopRevokeData);
			UDSRevokeSubsystem::GetInstance()->GetCTGeneratePoolStateDelegate().ExecuteIfBound(UE_CT_Property, 0, 0);
		}
	}
	else if (OppositeCommand == EDSCTRevokeType::E_Create)
	{
		UDSRevokeLibrary::SpawnLessCT(Cast<UDSCounterTopRevokeData>(InRevokeDataPtr));
	}
	else if (OppositeCommand == EDSCTRevokeType::E_Delete)
	{
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(
			Cast<UDSCounterTopRevokeData>(InRevokeDataPtr)->ModelType,
			Cast<UDSCounterTopRevokeData>(InRevokeDataPtr)->ModelUUID);
		if (IsValid(EditModel))
		{
			EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);

			UDSModelDependencySubsystem::GetInstance()->RemoveCounterTopRelation(EditModel->GetUUID());
			UDSModelDependencySubsystem::GetInstance()->RemoveSideCounterTopDependency(EditModel->GetUUID());
		}

		UDSRevokeSubsystem::GetInstance()->GetCTGeneratePoolStateDelegate().ExecuteIfBound(nullptr, 0, 0);
	}
	else if (OppositeCommand == EDSCTRevokeType::E_Update)
	{
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(
			Cast<UDSCounterTopRevokeData>(InRevokeDataPtr)->ModelType,
			Cast<UDSCounterTopRevokeData>(InRevokeDataPtr)->ModelUUID);
		if (IsValid(EditModel))
		{
			UDSRevokeLibrary::ParseCTProperty(EditModel, Cast<UDSCounterTopRevokeData>(InRevokeDataPtr));
			EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
		}
	}
	else if (OppositeCommand == EDSCTRevokeType::E_Trans)
	{//
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(
			Cast<UDSCounterTopRevokeData>(InRevokeDataPtr)->ModelType,
			Cast<UDSCounterTopRevokeData>(InRevokeDataPtr)->ModelUUID);
		if (IsValid(EditModel))
		{
			UDSRevokeLibrary::ParseCTProperty(EditModel, Cast<UDSCounterTopRevokeData>(InRevokeDataPtr));
			EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteTransform);
			if (Cast<UDSCounterTopRevokeData>(InRevokeDataPtr)->GetVisualState())
			{
				EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
				UDSCounterTopLibrary::RegenerateCounterTopEditPointsAndLines(EditModel);
			}
		}
	}
	else if (OppositeCommand == EDSCTRevokeType::E_ReGenerateLinePoint)
	{
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(
			Cast<UDSCounterTopRevokeData>(InRevokeDataPtr)->ModelType,
			Cast<UDSCounterTopRevokeData>(InRevokeDataPtr)->ModelUUID);
		if (IsValid(EditModel))
		{
			UDSRevokeLibrary::ParseCTProperty(EditModel, Cast<UDSCounterTopRevokeData>(InRevokeDataPtr));
			EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
			UDSCounterTopLibrary::RegenerateCounterTopEditPointsAndLines(EditModel);
		}
	}
	else if (OppositeCommand == EDSCTRevokeType::E_Hidden)
	{
		UDSRevokeLibrary::FlipHiddenUnion(
			Cast<UDSCounterTopRevokeData>(InRevokeDataPtr)->ModelUUID, 
			Cast<UDSCounterTopRevokeData>(InRevokeDataPtr)->ModelType
		);
	}
}

TArray<UDSRevokeData*> UDSRevokeLibrary::ExecuteSnapshotCommandRetOpposite_CT(const TArray<UDSRevokeData*>& InRevokeDataList)
{
	TArray<UDSRevokeData*> OppositeDataList;

	TArray<UDSBaseModel*> ModelsForDelete;

	TArray<UDSBaseModel*> AllModels = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_Generated_CounterTop, EDSModelType::E_Generated_SideCounterTop });
	for (UDSBaseModel* Model : AllModels)
	{
		if (!IsValid(Model))
		{
			continue;
		}

		// Construct new opposite data

		UDSCounterTopRevokeData* OppositeData = NewObject<UDSCounterTopRevokeData>();
		OppositeData->ModelExecuteType = FDSModelExecuteType::ExecuteAll;
		OppositeData->SetModelInfo(Model->GetUUID(), Model->GetModelType(), Model->GetModelType() == EDSModelType::E_Generated_SideCounterTop);

		FDSCounterTopPushData PushData;
		if (Model->GetModelType() == EDSModelType::E_Generated_CounterTop)
		{
			PushData.SetCTPropertyData(Model->GetTypedProperty<FDSCounterTopProperty>());

			TArray<FString> RelationModels = UDSModelDependencySubsystem::GetInstance()->FindCounterTopRelations(Model->GetUUID());
			ConstructRevokeCTRelation(RelationModels, OppositeData->GetCTDependencyInfoRef());
		}
		else if (Model->GetModelType() == EDSModelType::E_Generated_SideCounterTop)
		{
			PushData.SetCTPropertyData(Model->GetTypedProperty<FDSSideCounterTopProperty>());

			FSideCounterTopDependencyInfo* RelationInfo = UDSModelDependencySubsystem::GetInstance()->FindSideCounterTopDependency(Model->GetUUID());
			ConstructRevokeSideCTRelation(RelationInfo, OppositeData->GetSideCTDependencyInfoRef());
		}

		OppositeData->ConstructDataFromPushData(PushData);

		OppositeDataList.Add(OppositeData);

		// Find models for delete, delete counter top will delete related side counter top automatically, so we need collect them but not delete them here
		bool bIsModelNeedDelete = !InRevokeDataList.ContainsByPredicate([&](UDSRevokeData* InData)
			{
				if (UDSCounterTopRevokeData* CounterTopData = Cast<UDSCounterTopRevokeData>(InData))
				{
					return CounterTopData->ModelUUID == Model->GetUUID();
				}

				return false;
			});

		if (bIsModelNeedDelete)
		{
			ModelsForDelete.Add(Model);
		}
	}

	// Delete all counter top models
	for (UDSBaseModel* Model : ModelsForDelete)
	{
		if (IsValid(Model))
		{
			Model->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
		}
	}

	AllModels = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_Generated_CounterTop, EDSModelType::E_Generated_SideCounterTop });

	for (UDSRevokeData* RevokeData : InRevokeDataList)
	{
		UDSCounterTopRevokeData* CounterTopData = Cast<UDSCounterTopRevokeData>(RevokeData);
		if (CounterTopData == nullptr || CounterTopData->ModelUUID.IsEmpty())
		{
			// Need to process any other type?
			continue;
		}

		int32 ExistModelPos = AllModels.IndexOfByPredicate([&](UDSBaseModel* InModel)
			{
				return IsValid(InModel) && InModel->GetUUID() == CounterTopData->ModelUUID;
			});

		if (ExistModelPos == INDEX_NONE)
		{
			// Create model if not exist
			UDSCounterTopBaseModel* NewModel = nullptr;
			if (CounterTopData->IsSideCT)
			{
				NewModel = NewObject<UDSSideCounterTopModel>();
				NewModel->GetPropertySharedPtr()->CopyData(CounterTopData->GetSidePropertyData().Get());


			}
			else
			{
				NewModel = NewObject<UDSCounterTopModel>();
				NewModel->GetPropertySharedPtr()->CopyData(CounterTopData->GetPropertyData().Get());
			}

			NewModel->SetUUID(CounterTopData->ModelUUID);
			NewModel->SetModelType(CounterTopData->ModelType);

			ExistModelPos = AllModels.Add(NewModel);

			NewModel->SetNoNewGenerate();

			UDSMVCSubsystem::GetInstance()->SpawnViewUnion(NewModel);
		}
		else
		{
			// Recover model if exist
			if (CounterTopData->IsSideCT)
			{
				AllModels[ExistModelPos]->GetPropertySharedPtr()->CopyData(CounterTopData->GetSidePropertyData().Get());
			}
			else
			{
				AllModels[ExistModelPos]->GetPropertySharedPtr()->CopyData(CounterTopData->GetPropertyData().Get());
			}
		}

		if (CounterTopData->IsSideCT)
		{
			FSideCounterTopDependencyInfo SideCTDependency;
			UDSRevokeLibrary::ConstructSideCTRelation(CounterTopData->GetSideCTDependencyInfoRef(), &SideCTDependency);
			UDSModelDependencySubsystem::GetInstance()->SetSideCounterTopDependency(CounterTopData->ModelUUID, SideCTDependency);
		}
		else
		{
			TArray<FString> CTDependency;
			UDSRevokeLibrary::ConstructCTRelation(CounterTopData->GetCTDependencyInfoRef(), CTDependency);
			if (!CTDependency.IsEmpty())
			{
				UDSModelDependencySubsystem::GetInstance()->SetCounterTopRelations(CounterTopData->ModelUUID, CTDependency);
			}
		}
	}

	for (UDSBaseModel* Model : AllModels)
	{
		if (IsValid(Model))
		{
			Model->OnExecuteAction(FDSModelExecuteType::ExecuteTransform);
			Model->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
		}
	}

	return OppositeDataList;
}

void UDSRevokeLibrary::ExecuteCommandRetOpposite_MultiGroup(UDSRevokeData* InRevokeDataPtr, UDSRevokeData* OutRevokeDataPtr)
{
	checkf(InRevokeDataPtr != nullptr, TEXT("ExecuteCommandRetOpposite_MultiGroup -- InRevokeDataPtr is nullptr"));
	checkf(OutRevokeDataPtr != nullptr, TEXT("ExecuteCommandRetOpposite_MultiGroup -- OutRevokeDataPtr is nullptr"));

	UDSMultiGroupRevokeData* InRevokeData = Cast<UDSMultiGroupRevokeData>(InRevokeDataPtr);
	UDSMultiGroupRevokeData* OutRevokeData = Cast<UDSMultiGroupRevokeData>(OutRevokeDataPtr);
	if (InRevokeData == nullptr || OutRevokeData == nullptr)
		return;

	EDSMultiGroupRevokeType OppositeType = UDSRevokeLibrary::GetOppositeCommandType(InRevokeData->RevokeType);
	UDSRevokeLibrary::ConstructOppositeRevokeData_MultiGroup(InRevokeData, OutRevokeData);


	if (OppositeType == EDSMultiGroupRevokeType::E_Delete)
	{
		if (InRevokeData->CurModelInfo.IsValid())
		{
			UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InRevokeData->CurModelInfo.GetModelType(), InRevokeData->CurModelInfo.GetUUID());
			if (IsValid(EditModel))
			{
				EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
			}
		}
	}
	else if (OppositeType == EDSMultiGroupRevokeType::E_ReleaseGroup)
	{//原有操作为多选到成组
		if (InRevokeData->CurModelInfo.IsValid())
		{
			UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InRevokeData->CurModelInfo.GetModelType(), InRevokeData->CurModelInfo.GetUUID());
			if (EditModel == nullptr)
			{
				EditModel = UDSRevokeLibrary::GetGroupModelByInnerModelInfo(InRevokeData->ModelInfos);
			}
			if (IsValid(EditModel) && Cast<UDSGroupModel>(EditModel))
			{
				UDSGroupModel* EditGroupModel = Cast<UDSGroupModel>(EditModel);
				TArray<UDSBaseModel*> IncludeModels = EditGroupModel->ReleaseGroupItem();
				EditGroupModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
				
				for (auto& MGID : InRevokeData->ModelInfos)
				{
					if (MGID != nullptr && MGID->IsGroup())
					{//原来是组合，需重新成组
						TArray<UDSBaseModel*> InnerInclude;
						for(auto& GRDI : MGID->GroupRevokeData->GroupIncludeRevokeData)
						{
							if(GRDI != nullptr)
							{
								EDSModelType CurType;
								FString CurUUID;
								if(GRDI->GetActualTypeAndUUID(CurType, CurUUID))
								{
									if(UDSBaseModel* InnerGroupModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(CurType, CurUUID))
									{
										InnerInclude.AddUnique(InnerGroupModel);
									}
								}
								
							}
						}
						if(!InnerInclude.IsEmpty())
						{
							UDSGroupModel* NewGroup = UDSGroupModel::CreateGroupModel(MGID->GroupRevokeData->ModelInfo.UUID);
							NewGroup->SetNoNewGenerate();
							NewGroup->AddItem(InnerInclude, true);
							NewGroup->CombineGroupBroadcast();
							NewGroup->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelect);
						}
					}
				}
				
			}
		}
	}
	else if (OppositeType == EDSMultiGroupRevokeType::E_CombineGroup)
	{//原有操作为成组到多选
		TArray<UDSBaseModel*> ToGroupModel;
		for (auto& MGID : InRevokeData->ModelInfos)
		{
			if(MGID == nullptr) continue;

			EDSModelType CurType;
			FString CurUUID;
			if (MGID->GetActualTypeAndUUID(CurType, CurUUID))
			{
				UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(CurType, CurUUID);
				if (EditModel != nullptr)
				{
					ToGroupModel.AddUnique(EditModel);
				}
			}
		}
		if (!ToGroupModel.IsEmpty())
		{
            UDSGroupModel* NewGroup = UDSGroupModel::CreateGroupModel(InRevokeData->CurModelInfo.UUID);
            NewGroup->SetNoNewGenerate();
            NewGroup->AddItem(ToGroupModel, true);
			NewGroup->CombineGroupBroadcast();
			NewGroup->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelect);
		}
	}
	else if (OppositeType == EDSMultiGroupRevokeType::E_FlipHidden)
	{
		FDSRevokeModelInner CurModelInfo = InRevokeData->CurModelInfo;
		if (CurModelInfo.GetModelType() == EDSModelType::E_MultiSelect)
		{
			for (const auto& MI : InRevokeData->ModelInfos)
			{
				if (MI == nullptr) continue;

				FString UUID = TEXT("");
				EDSModelType Type = EDSModelType::E_None;
				if (MI->IsCustom())
				{
					UUID = MI->CustomRevokeData->GetModelBaseData().GetUUID();
					Type = MI->CustomRevokeData->GetModelBaseData().GetModelType();
				}
				else if (MI->IsCounterTop())
				{
					UUID = MI->CounterTopRevokeData->ModelUUID;
					Type = MI->CounterTopRevokeData->ModelType;
				}
				else if (MI->IsGeneratedLine())
				{
					UUID = MI->GeneratedLineRevokeData->ModelInfo.GetUUID();
					Type = MI->GeneratedLineRevokeData->ModelInfo.GetModelType();
				}
				else if (MI->IsLineEntity())
				{
					UUID = MI->LineEntityRevokeData->ModelInfo.GetUUID();
					Type = MI->LineEntityRevokeData->ModelInfo.GetModelType();
				}
				else if (MI->IsSink())
				{
					UUID = MI->SinkRevokeData->GetModelInfo().GetUUID();
					Type = MI->SinkRevokeData->GetModelInfo().GetModelType();
				}
				else if (MI->IsSoftFurniture())
				{
					UUID = MI->SoftFurnitureRevokeData->GetModelInfo().GetUUID();
					Type = MI->SoftFurnitureRevokeData->GetModelInfo().GetModelType();
				}
				else if (MI->IsSRH())
				{
					UUID = MI->SRHRevokeData->GetModelInfo().GetUUID();
					Type = MI->SRHRevokeData->GetModelInfo().GetModelType();
				}
				else if (MI->IsModelCeiling())
				{
                    UUID = MI->SRHRevokeData->GetModelInfo().GetUUID();
                    Type = MI->SRHRevokeData->GetModelInfo().GetModelType();
				}

				UDSRevokeLibrary::FlipHiddenUnion(UUID, Type);
			}
		}
		else if (CurModelInfo.GetModelType() == EDSModelType::E_Group)
		{
			UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(CurModelInfo.GetModelType(), CurModelInfo.GetUUID());
			if (IsValid(EditModel))
			{
				bool IsEditHidden = EditModel->HasHidden();
				FDSModelExecuteType CurCommand = IsEditHidden ? FDSModelExecuteType::ExecuteUnHidden : FDSModelExecuteType::ExecuteHidden;
				EditModel->OnExecuteAction(CurCommand);
			}
		}

	}
	else if (OppositeType == EDSMultiGroupRevokeType::E_Transform)
	{
		if (InRevokeData->CurModelInfo.IsValid())
		{
			UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InRevokeData->CurModelInfo.GetModelType(), InRevokeData->CurModelInfo.GetUUID());
			if (IsValid(EditModel) && EditModel->GetModelType() == EDSModelType::E_Group)
			{
				UDSRevokeLibrary::TransformUnion(EditModel, InRevokeData->CurModelInfo.GetPropertyRef());
			}
			else
			{//多选单独处理
				for (const auto& MI : InRevokeData->ModelInfos)
				{
					if (MI == nullptr) continue;

					FString UUID = TEXT("");
					EDSModelType Type = EDSModelType::E_None;
					TSharedPtr<FDSBaseProperty> Property = nullptr;
					if (MI->IsCustom())
					{
						UUID = MI->CustomRevokeData->GetModelBaseData().GetUUID();
						Type = MI->CustomRevokeData->GetModelBaseData().GetModelType();
						Property = MI->CustomRevokeData->GetModelBaseData().Property;
					}
					else if (MI->IsCounterTop())
					{

					}
					else if (MI->IsGeneratedLine())
					{

					}
					else if (MI->IsLineEntity())
					{
					}
					else if (MI->IsSink())
					{
						UUID = MI->SinkRevokeData->GetModelInfo().GetUUID();
						Type = MI->SinkRevokeData->GetModelInfo().GetModelType();
						Property = MI->SinkRevokeData->GetModelInfo().Property;
					}
					else if (MI->IsSoftFurniture())
					{
						UUID = MI->SoftFurnitureRevokeData->GetModelInfo().GetUUID();
						Type = MI->SoftFurnitureRevokeData->GetModelInfo().GetModelType();
						Property = MI->SoftFurnitureRevokeData->GetModelInfo().Property;
					}
					else if (MI->IsSRH())
					{
						UUID = MI->SRHRevokeData->GetModelInfo().GetUUID();
						Type = MI->SRHRevokeData->GetModelInfo().GetModelType();
						Property = MI->SRHRevokeData->GetModelInfo().Property;
					}
					else if (MI->IsModelCeiling())
					{
                        UUID = MI->ModelCeilingRevokeData->GetModelInfo().GetUUID();
                        Type = MI->ModelCeilingRevokeData->GetModelInfo().GetModelType();
                        Property = MI->ModelCeilingRevokeData->GetModelInfo().Property;
					}

					UDSRevokeLibrary::TransformUnion(UUID, Type, Property);
				}
			}
		}
	}
	else if (OppositeType == EDSMultiGroupRevokeType::E_Spawn)
	{
		TArray<UDSBaseModel*> IncludeModels;
		for (const auto& MI : InRevokeData->ModelInfos)
		{
			if (MI == nullptr) continue;

			UDSBaseModel* NewModel = nullptr;
			if (MI->IsCustom())
			{
				NewModel = UDSRevokeLibrary::SpawnLessCustomData(MI->CustomRevokeData);
			}
			else if (MI->IsCounterTop())
			{
			}
			else if (MI->IsGeneratedLine())
			{
				NewModel = UDSRevokeLibrary::SpawnLessGeneratedLine(MI->GeneratedLineRevokeData);
			}
			else if (MI->IsLineEntity())
			{
				UE_LOG(LogTemp, Error, TEXT("UDSRevokeLibrary::SpawnLineEntity ---- can not spawn inner single line"));
				checkNoEntry();
			}
			else if (MI->IsSink())
			{
				NewModel = UDSRevokeLibrary::SpawnLessSink(MI->SinkRevokeData);
			}
			else if (MI->IsSoftFurniture())
			{
				NewModel = UDSRevokeLibrary::SpawnLessSoftFurniture(MI->SoftFurnitureRevokeData);
			}
			else if (MI->IsSRH())
			{
				NewModel = UDSRevokeLibrary::SpawnLessStoveRangeHood(MI->SRHRevokeData);
			}
			else if (MI->IsModelCeiling())
			{
                NewModel = UDSRevokeLibrary::SpawnLessModelCeiling(MI->ModelCeilingRevokeData);
			}

			if (NewModel != nullptr)
			{
				IncludeModels.Add(NewModel);
			}

		}
		if (IncludeModels.IsEmpty())
			return;

		UDSGroupModel* EditModel = nullptr;
		if (InRevokeData->CurModelInfo.GetModelType() == EDSModelType::E_Group)
		{
			EditModel = UDSGroupModel::CreateGroupModel(InRevokeData->CurModelInfo.GetUUID());
			EditModel->SetNoNewGenerate();
			EditModel->AddItem(IncludeModels);
			EditModel->CombineGroupBroadcast();
			EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteUnSelect);
		}
	}
}

void UDSRevokeLibrary::ConstructOppositeRevokeData_MultiGroup(const UDSMultiGroupRevokeData* InPtr, UDSMultiGroupRevokeData* OutPtr)
{
	if (InPtr == nullptr || OutPtr == nullptr)
	{
		return;
	}

	EDSMultiGroupRevokeType OppositeType = UDSRevokeLibrary::GetOppositeCommandType(InPtr->RevokeType);
	OutPtr->RevokeType = OppositeType;

	UDSBaseModel* TopModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InPtr->CurModelInfo.GetModelType(), InPtr->CurModelInfo.GetUUID());
	if (TopModel == nullptr)
	{
		TopModel = UDSRevokeLibrary::GetGroupModelByInnerModelInfo(InPtr->ModelInfos);
	}
	if (IsValid(TopModel))
	{
		OutPtr->SetCurModelInfo(TopModel->GetUUID(), TopModel->GetModelType(), TopModel->GetPropertySharedPtr());
	}
	else
	{
		OutPtr->SetCurModelInfo(InPtr->CurModelInfo);
	}

	if (!InPtr->ModelInfos.IsEmpty())
	{
		for (const auto& MGID : InPtr->ModelInfos)
		{
			if (MGID != nullptr)
			{
				UDSMultiGroupItemData* MGIDItem = NewObject<UDSMultiGroupItemData>();
				if (MGID->IsCustom())
				{
					UDSRevokeData* OutRevokeDataPtr = NewObject<UDSCustomRevokeData>();
					UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(
						MGID->CustomRevokeData->GetModelBaseData().GetModelType(), MGID->CustomRevokeData->GetModelBaseData().GetUUID());
					if (DS_MODEL_VALID_FOR_USE(EditModel))
					{
						UDSRevokeLibrary::ConstructRevokeModelData_Custom(EditModel, OutRevokeDataPtr);
					}
					else
					{
						Cast<UDSCustomRevokeData>(OutRevokeDataPtr)->CopyData(MGID->CustomRevokeData);
					}
					MGIDItem->SetRevokeData(Cast<UDSCustomRevokeData>(OutRevokeDataPtr));
				}
				else if (MGID->IsCounterTop())
				{
					UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(MGID->CounterTopRevokeData->ModelType, MGID->CounterTopRevokeData->ModelUUID);
					if (EditModel != nullptr)
					{
						FDSModelExecuteType OppositeExecuteType = UDSRevokeLibrary::GetOppositeExecuteType(MGID->CounterTopRevokeData->ModelExecuteType);
						MGIDItem = UDSRevokeLibrary::ConstructInnerIncludeRevokeData(EditModel, OppositeExecuteType);
					}
					else
					{
						UDSCounterTopRevokeData* CTRevokePtr = NewObject<UDSCounterTopRevokeData>();
						CTRevokePtr->CopyData(MGID->CounterTopRevokeData);
                        MGIDItem->SetRevokeData(CTRevokePtr);
					}
				}
				else if (MGID->IsGeneratedLine())
				{
				}
				else if (MGID->IsLineEntity())
				{
				}
				else if (MGID->IsSink())
				{
					UDSRevokeData* OutRevokeDataPtr = NewObject<UDSSinkRevokeData>();
					UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(MGID->SinkRevokeData->GetModelInfo().GetModelType(), MGID->SinkRevokeData->GetModelInfo().GetUUID());
					if (EditModel)
					{
						UDSRevokeLibrary::ConstructRevokeData_Sink(EditModel, Cast<UDSSinkRevokeData>(OutRevokeDataPtr));
					}
					else
					{
						Cast<UDSSinkRevokeData>(OutRevokeDataPtr)->CopyData(MGID->SinkRevokeData);
					}
                    MGIDItem->SetRevokeData(Cast<UDSSinkRevokeData>(OutRevokeDataPtr));
				}
				else if (MGID->IsSoftFurniture())
				{
					UDSRevokeData* OutRevokeDataPtr = NewObject<UDSSoftFurnitureRevokeData>();
					UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(MGID->SoftFurnitureRevokeData->GetModelInfo().GetModelType(), MGID->SoftFurnitureRevokeData->GetModelInfo().GetUUID());
					if (EditModel)
					{
						UDSRevokeLibrary::ConstructRevokeData_SoftFurniture(EditModel, Cast<UDSSoftFurnitureRevokeData>(OutRevokeDataPtr));
					}
					else
					{
						Cast<UDSSoftFurnitureRevokeData>(OutRevokeDataPtr)->CopyData(MGID->SoftFurnitureRevokeData);
					}
                    MGIDItem->SetRevokeData(Cast<UDSSoftFurnitureRevokeData>(OutRevokeDataPtr));
				}
				else if (MGID->IsSRH())
				{
					UDSRevokeData* OutRevokeDataPtr = NewObject<UDSSRHRevokeData>();
					UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(MGID->SRHRevokeData->GetModelInfo().GetModelType(), MGID->SRHRevokeData->GetModelInfo().GetUUID());
					if (EditModel)
					{
						UDSRevokeLibrary::ConstructRevokeData_StoveRangeHood(EditModel, Cast<UDSSRHRevokeData>(OutRevokeDataPtr));
					}
					else
					{
						Cast<UDSSRHRevokeData>(OutRevokeDataPtr)->CopyData(MGID->SRHRevokeData);
					}
                    MGIDItem->SetRevokeData(Cast<UDSSRHRevokeData>(OutRevokeDataPtr));
				}
				else if (MGID->IsGroup())
				{
					UDSGroupIncludeData* OutRevokeDataPtr = NewObject<UDSGroupIncludeData>();
					UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(
						MGID->GroupRevokeData->ModelInfo.ModelType, MGID->GroupRevokeData->ModelInfo.UUID);
					if (EditModel == nullptr)
					{
						EditModel = UDSRevokeLibrary::GetGroupModelByInnerModelInfo(MGID->GroupRevokeData->GroupIncludeRevokeData);
					}
					if (EditModel && Cast<UDSGroupModel>(EditModel))
					{
						TArray<UDSBaseModel*> IncludeModels = Cast<UDSGroupModel>(EditModel)->GetIncludeModel();
						for (const auto& IM : IncludeModels)
						{
							if (IM == nullptr) continue;

                            UDSMultiGroupItemData* InnerData = UDSRevokeLibrary::ConstructInnerIncludeRevokeData(IM, InPtr->ModelExecuteType);
						}
					}
					else
					{
						OutRevokeDataPtr = MGID->GroupRevokeData;
					}
					MGIDItem->SetRevokeData(OutRevokeDataPtr);
				}

				OutPtr->AddData(MGIDItem);
			}
		}
	}
}

UDSMultiGroupItemData* UDSRevokeLibrary::ConstructInnerIncludeRevokeData(UDSBaseModel* InModel, const FDSModelExecuteType& InExecuteType)
{
	UDSMultiGroupItemData* InnerItem = nullptr;
	if (IsValid(InModel))
	{
		InnerItem = NewObject<UDSMultiGroupItemData>();

		EDSModelType ModelType = InModel->GetModelType();
		if (UDSToolLibrary::IsCustomCabinetType(ModelType))
		{//定制
			UDSRevokeData* NewData = NewObject<UDSCustomRevokeData>();
			NewData->ModelExecuteType = InExecuteType;
			TSharedPtr<FDSCustomComponentRevokeData> ComponentData = nullptr;
			UDSRevokeLibrary::ConstructRevokeModelData_Custom(InModel, ComponentData, NewData);
			InnerItem->SetRevokeData(Cast<UDSCustomRevokeData>(NewData));
		}
		else if (UDSToolLibrary::IsGeneratedLineType(ModelType))
		{
			UDSGeneratedLineRevokeData* NewData = NewObject<UDSGeneratedLineRevokeData>();
			NewData->ModelExecuteType = InExecuteType;
			UDSRevokeLibrary::ConstructRevokeData_GeneratedLine(InModel, NewData);
			InnerItem->SetRevokeData(NewData);
		}
		else if (ModelType == EDSModelType::E_Generated_LineEntity)
		{
			//自我删除插入，不在重复构建
			if (InExecuteType.IsDeleteExecute())
			{
				return nullptr;
			}

			UDSLineEntityRevokeData* NewData = NewObject<UDSLineEntityRevokeData>();
			NewData->ModelExecuteType = InExecuteType;
			UDSRevokeLibrary::ConstructRevokeData_LineEntity(InModel, NewData);
			InnerItem->SetRevokeData(NewData);
		}
		else if (ModelType == EDSModelType::E_Generated_CounterTop || ModelType == EDSModelType::E_Generated_SideCounterTop)
		{//台面
			UDSCounterTopRevokeData* NewData = NewObject<UDSCounterTopRevokeData>();
            NewData->ModelExecuteType = InExecuteType;
			bool IsSide = ModelType == EDSModelType::E_Generated_SideCounterTop;
			NewData->SetModelInfo(InModel->GetUUID(), InModel->GetModelType(), IsSide);

			//relation
			if (Cast<UDSCounterTopModel>(InModel))
			{//CT
				TArray<FString> RelationModel = UDSModelDependencySubsystem::GetInstance()->FindCounterTopRelations(InModel->GetUUID());
				UDSRevokeLibrary::ConstructRevokeCTRelation(RelationModel, NewData->GetCTDependencyInfoRef());
			}
			else if (Cast<UDSSideCounterTopModel>(InModel))
			{//side CT
				FSideCounterTopDependencyInfo* RelationInfo = UDSModelDependencySubsystem::GetInstance()->FindSideCounterTopDependency(InModel->GetUUID());
				UDSRevokeLibrary::ConstructRevokeSideCTRelation(RelationInfo, NewData->GetSideCTDependencyInfoRef());
			}

            InnerItem->SetRevokeData(NewData);
		}
		else if (ModelType == EDSModelType::E_Custom_Sink)
		{//水槽
			UDSSinkRevokeData* NewData = NewObject<UDSSinkRevokeData>();
			NewData->ModelExecuteType = InExecuteType;
			UDSRevokeLibrary::ConstructRevokeData_Sink(InModel, NewData);
			InnerItem->SetRevokeData(NewData);
		}
		else if (ModelType == EDSModelType::E_Custom_Stove || ModelType == EDSModelType::E_Custom_RangeHood)
		{//灶具
			UDSSRHRevokeData* NewData = NewObject<UDSSRHRevokeData>();
			NewData->ModelExecuteType = InExecuteType;
			UDSRevokeLibrary::ConstructRevokeData_StoveRangeHood(InModel, NewData);
			InnerItem->SetRevokeData(NewData);
		}
		else if (ModelType == EDSModelType::E_Furniture_HouseFurniture)
		{//成品
			UDSSoftFurnitureRevokeData* NewData = NewObject<UDSSoftFurnitureRevokeData>();
			NewData->ModelExecuteType = InExecuteType;
			UDSRevokeLibrary::ConstructRevokeData_SoftFurniture(InModel, NewData);
			InnerItem->SetRevokeData(NewData);
		}
		else if(ModelType == EDSModelType::E_Furniture_MoldingCeiling)
		{//成型吊顶
			UDSModelCeilingRevokeData* NewData = NewObject<UDSModelCeilingRevokeData>();
			NewData->ModelExecuteType = InExecuteType;
			UDSRevokeLibrary::ConstructRevokeData_ModelCeiling(InModel, NewData);
			InnerItem->SetRevokeData(NewData);
		}
		else if (ModelType == EDSModelType::E_Group)
		{//组合
			UDSGroupIncludeData* NewData = NewObject<UDSGroupIncludeData>();
			NewData->SetModelInfo(InModel->GetUUID());
			TArray<UDSBaseModel*> IncludeModels = Cast<UDSGroupModel>(InModel)->GetIncludeModel();
            for (UDSBaseModel* Model : IncludeModels)
			{
				UDSMultiGroupItemData* MGIDItem = UDSRevokeLibrary::ConstructInnerIncludeRevokeData(Model, InExecuteType);
				if (MGIDItem != nullptr)
				{
					NewData->AddData(MGIDItem);
				}
			}
			InnerItem->SetRevokeData(NewData);
		}
	}

	return InnerItem;
}

UDSMultiGroupItemData* UDSRevokeLibrary::ConstructInnerIncludeRevokeData_GenerateLine(UDSGeneratedLineRevokeData* InData)
{
	if (InData == nullptr)
		return nullptr;

	UDSMultiGroupItemData* InnerItem = NewObject<UDSMultiGroupItemData>();
	InnerItem->SetRevokeData(InData);
	return InnerItem;
}

UDSBaseModel* UDSRevokeLibrary::GetGroupModelByInnerModelInfo(const TArray<UDSMultiGroupItemData*>& IncludeDatas)
{
	for (const auto& MGID : IncludeDatas)
	{
		if (MGID == nullptr) continue;
		
		EDSModelType ModelType;
		FString UUID;
		if (MGID->GetActualTypeAndUUID(ModelType, UUID))
		{
			UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(ModelType, UUID);
			if (EditModel)
			{
				FString GroupUUID = EditModel->GetGroupUUID();
                if (!GroupUUID.IsEmpty())
				{
					UDSBaseModel* GroupModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(EDSModelType::E_Group, GroupUUID);
					if (IsValid(GroupModel))
					{
						return GroupModel;
					}
				}
			}
		}
	}

	return nullptr;
}

void UDSRevokeLibrary::ExecuteCommandRetOpposite_Sink(UDSRevokeData* InRevokeDataPtr, UDSRevokeData* OutRevokeDataPtr)
{
	checkf(InRevokeDataPtr != nullptr, TEXT("ExecuteCommandRetOpposite_Sink -- InRevokeDataPtr is nullptr"));
	checkf(OutRevokeDataPtr != nullptr, TEXT("ExecuteCommandRetOpposite_Sink -- OutRevokeDataPtr is nullptr"));

	UDSSinkRevokeData* InRevokeData = Cast<UDSSinkRevokeData>(InRevokeDataPtr);
	UDSSinkRevokeData* OutRevokeData = Cast<UDSSinkRevokeData>(OutRevokeDataPtr);
	if (InRevokeData == nullptr || OutRevokeData == nullptr)
		return;

	EDSSinkRevokeType OppositeCommand = UDSRevokeLibrary::GetOppositeCommandType(InRevokeData->RevokeType);
	UDSRevokeLibrary::ConstructOppositeRevokeData_Sink(InRevokeData, OutRevokeData);

	if (OppositeCommand == EDSSinkRevokeType::E_Delete)
	{
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InRevokeData->GetModelInfo().GetModelType(), InRevokeData->GetModelInfo().GetUUID());
		if (IsValid(EditModel))
		{
			EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);

			UDSModelDependencySubsystem::GetInstance()->RemoveSink(EditModel->GetUUID());

			UDSBaseModel* LinkModel = UDSMVCSubsystem::GetInstance()->GetModelByID(InRevokeData->GetLinkUUID());
			if (IsValid(LinkModel))
			{
				LinkModel->DeleteLinkModel(EditModel);
			}
		}
	}
	else if (OppositeCommand == EDSSinkRevokeType::E_Refresh)
	{
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InRevokeData->GetModelInfo().GetModelType(), InRevokeData->GetModelInfo().GetUUID());
		if (IsValid(EditModel))
		{
			UDSToolLibrary::ParsePropertyCopy(EditModel, InRevokeData->GetProperty());
			EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateByProperty);
		}
	}
	else if (OppositeCommand == EDSSinkRevokeType::E_Spawn)
	{
		UDSRevokeLibrary::SpawnLessSink(InRevokeData);
	}
	else if (OppositeCommand == EDSSinkRevokeType::E_Transform)
	{
		UDSRevokeLibrary::TransformUnion(
			InRevokeData->GetModelInfo().GetUUID(),
			InRevokeData->GetModelInfo().GetModelType(),
			InRevokeData->GetProperty()
		);

		UDSCounterTopLibrary::RefreshAllCounterTop();
	}
	else if (OppositeCommand == EDSSinkRevokeType::E_FlipHidden)
	{
		UDSRevokeLibrary::FlipHiddenUnion(InRevokeData->GetModelInfo().GetUUID(), InRevokeData->GetModelInfo().GetModelType());
	}
}

UDSBaseModel* UDSRevokeLibrary::SpawnLessSink(const UDSSinkRevokeData* LessRevokePtr)
{
	if (LessRevokePtr == nullptr)
		return nullptr;

	UDSSinkModel* NewModel = NewObject<UDSSinkModel>();
	NewModel->SetNoNewGenerate();
	NewModel->SetUUID(LessRevokePtr->GetModelInfo().GetUUID());
	UDSToolLibrary::ParsePropertyCopy(NewModel, LessRevokePtr->GetProperty());

	UDSMVCSubsystem::GetInstance()->SpawnViewUnion(NewModel);

	NewModel->OnExecuteAction(FDSModelExecuteType::ExecuteGenerateFormProperty);

	UDSModelDependencySubsystem::GetInstance()->AddSinkAndCupboardRelation(NewModel->GetUUID(), LessRevokePtr->GetLinkUUID());

	UDSBaseModel* LinkModel = UDSMVCSubsystem::GetInstance()->GetModelByID(LessRevokePtr->GetLinkUUID());
	if (IsValid(LinkModel))
	{
		LinkModel->AddLinkModel(NewModel);
	}

	return NewModel;
}

void UDSRevokeLibrary::ConstructOppositeRevokeData_Sink(const UDSSinkRevokeData* InPtr, UDSSinkRevokeData* OutPtr)
{
	if (InPtr == nullptr || OutPtr == nullptr)
	{
		return;
	}

	EDSSinkRevokeType OppositeCommand = UDSRevokeLibrary::GetOppositeCommandType(InPtr->RevokeType);
	OutPtr->SetRevokeType(OppositeCommand);
	OutPtr->SetModelInfo(InPtr->ModelInfo.GetUUID(), InPtr->ModelInfo.GetModelType());

	if (!InPtr->ModelInfo.GetUUID().IsEmpty() && InPtr->ModelInfo.GetModelType() != EDSModelType::E_None)
	{//
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InPtr->ModelInfo.GetModelType(), InPtr->ModelInfo.GetUUID());
		if (IsValid(EditModel))
		{
			OutPtr->SetProperty(StaticCastSharedPtr<FDSSinkProperty>(EditModel->GetPropertySharedPtr()), false);
		}
		else
		{
			OutPtr->SetProperty(StaticCastSharedPtr<FDSSinkProperty>(InPtr->GetProperty()), true);
		}

		OutPtr->SetLinkUUID(InPtr->GetLinkUUID());
	}
	else
	{//property change
		OutPtr->SetProperty(StaticCastSharedPtr<FDSSinkProperty>(InPtr->GetProperty()), true);
	}
}

void UDSRevokeLibrary::ConstructRevokeData_Sink(UDSBaseModel* InModel, UDSSinkRevokeData* OutPtr)
{
	if (InModel != nullptr && OutPtr != nullptr)
	{
		OutPtr->SetModelInfo(InModel->GetUUID(), InModel->GetModelType());
		OutPtr->SetProperty(StaticCastSharedPtr<FDSSinkProperty>(InModel->GetPropertySharedPtr()), false);
	}
}

void UDSRevokeLibrary::ExecuteCommandRetOpposite_StoveRangeHood(UDSRevokeData* InRevokeDataPtr, UDSRevokeData* OutRevokeDataPtr)
{
	//灶台烟机撤销实现
	checkf(InRevokeDataPtr != nullptr, TEXT("ExecuteCommandRetOpposite_StoveRangeHood -- InRevokeDataPtr is nullptr"));
	checkf(OutRevokeDataPtr != nullptr, TEXT("ExecuteCommandRetOpposite_StoveRangeHood -- OutRevokeDataPtr is nullptr"));

	UDSSRHRevokeData* InRevokeData = Cast<UDSSRHRevokeData>(InRevokeDataPtr);
	UDSSRHRevokeData* OutRevokeData = Cast<UDSSRHRevokeData>(OutRevokeDataPtr);
	if (InRevokeData == nullptr || OutRevokeData == nullptr)
		return;

	EDSSRHRevokeType OppositeCommand = UDSRevokeLibrary::GetOppositeCommandType(InRevokeData->RevokeType);
	UDSRevokeLibrary::ConstructOppositeRevokeData_StoveRangeHood(InRevokeData, OutRevokeData);

	if (OppositeCommand == EDSSRHRevokeType::E_Delete)
	{
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InRevokeData->GetModelInfo().GetModelType(), InRevokeData->GetModelInfo().GetUUID());
		if (IsValid(EditModel))
		{
			EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);

			UDSModelDependencySubsystem::GetInstance()->RemoveStove(EditModel->GetUUID());

			UDSBaseModel* LinkModel = UDSMVCSubsystem::GetInstance()->GetModelByID(InRevokeData->GetLinkUUID());
			if (IsValid(LinkModel))
			{
				LinkModel->DeleteLinkModel(EditModel);
			}
		}
	}
	else if (OppositeCommand == EDSSRHRevokeType::E_Refresh)
	{
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InRevokeData->GetModelInfo().GetModelType(), InRevokeData->GetModelInfo().GetUUID());
		if (IsValid(EditModel))
		{
			UDSToolLibrary::ParsePropertyCopy(EditModel, InRevokeData->GetProperty());
			EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateByProperty);
		}
	}
	else if (OppositeCommand == EDSSRHRevokeType::E_Spawn)
	{
		UDSRevokeLibrary::SpawnLessStoveRangeHood(InRevokeData);
	}
	else if (OppositeCommand == EDSSRHRevokeType::E_Transform)
	{
		UDSRevokeLibrary::TransformUnion(
			InRevokeData->GetModelInfo().GetUUID(),
			InRevokeData->GetModelInfo().GetModelType(),
			InRevokeData->GetProperty()
		);
	}
	else if (OppositeCommand == EDSSRHRevokeType::E_FlipHidden)
	{
		UDSRevokeLibrary::FlipHiddenUnion(InRevokeData->GetModelInfo().GetUUID(), InRevokeData->GetModelInfo().GetModelType());
	}
}

UDSBaseModel* UDSRevokeLibrary::SpawnLessStoveRangeHood(const UDSSRHRevokeData* LessRevokePtr)
{
	if (LessRevokePtr == nullptr)
		return nullptr;

	UDSBaseModel* NewModel = nullptr;
	//根据类型生成灶具、烟机
	EDSModelType NewType = LessRevokePtr->GetModelInfo().GetModelType();
	if (NewType == EDSModelType::E_Custom_Stove)
	{
		NewModel = NewObject<UDSStoveModel>();
		NewModel->SetUUID(LessRevokePtr->GetModelInfo().GetUUID());
		UDSModelDependencySubsystem::GetInstance()->AddStoveAndCupboardRelation(NewModel->GetUUID(), LessRevokePtr->GetLinkUUID());
	}
	else if (NewType == EDSModelType::E_Custom_RangeHood)
	{
		NewModel = NewObject<UDSRangeHoodModel>();
		NewModel->SetUUID(LessRevokePtr->GetModelInfo().GetUUID());
		UDSModelDependencySubsystem::GetInstance()->AddStoveAndRangeHoodRelation(LessRevokePtr->GetLinkUUID(), NewModel->GetUUID());
	}

	if (NewModel == nullptr)
		return nullptr;

	NewModel->SetNoNewGenerate();

	UDSToolLibrary::ParsePropertyCopy(NewModel, LessRevokePtr->GetProperty());

	UDSMVCSubsystem::GetInstance()->SpawnViewUnion(NewModel);

	NewModel->OnExecuteAction(FDSModelExecuteType::ExecuteGenerateFormProperty);

	UDSBaseModel* LinkModel = UDSMVCSubsystem::GetInstance()->GetModelByID(LessRevokePtr->GetLinkUUID());
	if (IsValid(LinkModel))
	{
		LinkModel->AddLinkModel(NewModel);
	}

	return NewModel;
}

void UDSRevokeLibrary::ConstructOppositeRevokeData_StoveRangeHood(const UDSSRHRevokeData* InPtr, UDSSRHRevokeData* OutPtr)
{
	if (InPtr == nullptr || OutPtr == nullptr)
	{
		return;
	}

	EDSSRHRevokeType OppositeCommand = UDSRevokeLibrary::GetOppositeCommandType(InPtr->RevokeType);
	OutPtr->SetRevokeType(OppositeCommand);
	OutPtr->SetModelInfo(InPtr->ModelInfo.GetUUID(), InPtr->ModelInfo.GetModelType());

	if (!InPtr->ModelInfo.GetUUID().IsEmpty() && InPtr->ModelInfo.GetModelType() != EDSModelType::E_None)
	{//
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InPtr->ModelInfo.GetModelType(), InPtr->ModelInfo.GetUUID());

		if (IsValid(EditModel))
		{
			if (InPtr->ModelInfo.GetModelType() == EDSModelType::E_Custom_Stove)
			{
				OutPtr->SetProperty(StaticCastSharedPtr<FDSStoveProperty>(EditModel->GetPropertySharedPtr()), false);
			}
			else if (InPtr->ModelInfo.GetModelType() == EDSModelType::E_Custom_RangeHood)
			{
				OutPtr->SetProperty(StaticCastSharedPtr<FDSRangeHoodProperty>(EditModel->GetPropertySharedPtr()), false);
			}
		}
		else
		{
			if (InPtr->ModelInfo.GetModelType() == EDSModelType::E_Custom_Stove)
			{
				OutPtr->SetProperty(StaticCastSharedPtr<FDSStoveProperty>(InPtr->GetProperty()), true);
			}
			else if (InPtr->ModelInfo.GetModelType() == EDSModelType::E_Custom_RangeHood)
			{
				OutPtr->SetProperty(StaticCastSharedPtr<FDSRangeHoodProperty>(InPtr->GetProperty()), true);
			}
		}

		OutPtr->SetLinkUUID(InPtr->GetLinkUUID());
	}
	else
	{//property change
		if (InPtr->ModelInfo.GetModelType() == EDSModelType::E_Custom_Stove)
		{
			OutPtr->SetProperty(StaticCastSharedPtr<FDSStoveProperty>(InPtr->GetProperty()), true);
		}
		else if (InPtr->ModelInfo.GetModelType() == EDSModelType::E_Custom_RangeHood)
		{
			OutPtr->SetProperty(StaticCastSharedPtr<FDSRangeHoodProperty>(InPtr->GetProperty()), true);
		}

	}
}

void UDSRevokeLibrary::ConstructRevokeData_StoveRangeHood(UDSBaseModel* InModel, UDSSRHRevokeData* OutPtr)
{
	if (IsValid(InModel) && OutPtr != nullptr)
	{
		OutPtr->SetModelInfo(InModel->GetUUID(), InModel->GetModelType());
		if (InModel->GetModelType() == EDSModelType::E_Custom_Stove)
		{
			OutPtr->SetProperty(StaticCastSharedPtr<FDSStoveProperty>(InModel->GetPropertySharedPtr()), false);
		}
		else if (InModel->GetModelType() == EDSModelType::E_Custom_RangeHood)
		{
			OutPtr->SetProperty(StaticCastSharedPtr<FDSRangeHoodProperty>(InModel->GetPropertySharedPtr()), false);
		}
	}
}

void UDSRevokeLibrary::ExecuteCommandRetOpposite_GeneratedLine(UDSRevokeData* InRevokeData,
	UDSRevokeData* OutRevokeData)
{
	UDSGeneratedLineRevokeData* OldRevokeData = Cast<UDSGeneratedLineRevokeData>(InRevokeData);
	UDSGeneratedLineRevokeData* OppositeRevokeData = Cast<UDSGeneratedLineRevokeData>(OutRevokeData);
	if (OldRevokeData == nullptr || OppositeRevokeData == nullptr)
	{
		return;
	}

	EDSGeneratedLineRevokeType OppositeCommand = GetOppositeCommandType(OldRevokeData->RevokeType);
	ConstructOppositeRevokeData_GeneratedLine(OldRevokeData, OppositeRevokeData);

	switch (OppositeCommand)
	{
	case EDSGeneratedLineRevokeType::E_Spawn:
	{
		UDSRevokeLibrary::SpawnLessGeneratedLine(OldRevokeData);
	}
	break;
	case EDSGeneratedLineRevokeType::E_Delete:
	{
		UDSBaseModel* LineModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(OldRevokeData->ModelInfo.GetModelType(), OldRevokeData->ModelInfo.GetUUID());
		if (LineModel != nullptr)
		{
			LineModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
		}
	}
	break;
	case EDSGeneratedLineRevokeType::E_Update:
	{
		UDSBaseModel* LineModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(OldRevokeData->ModelInfo.GetModelType(), OldRevokeData->ModelInfo.GetUUID());
		if (LineModel != nullptr)
		{
			LineModel->GetPropertySharedPtr()->CopyData(OldRevokeData->ModelInfo.Property);
			LineModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
		}
	}
	break;
	default:
		break;
	}
}

UDSBaseModel* UDSRevokeLibrary::SpawnLessGeneratedLine(const UDSGeneratedLineRevokeData* LessRevokeData)
{
	if (LessRevokeData != nullptr)
	{
		UDSBaseModel* NewModel = nullptr;
		switch (LessRevokeData->ModelInfo.ModelType)
		{
		case EDSModelType::E_Generated_CrownMoulding:
			NewModel = NewObject<UDSCrownMouldingModel>();
			break;
		case EDSModelType::E_Generated_LightCord:
			NewModel = NewObject<UDSLightCordModel>();
			break;
		case EDSModelType::E_Generated_SkirtingBoard:
			NewModel = NewObject<UDSSkirtingBoardModel>();
			break;
		default:
			break;
		}

		if (NewModel == nullptr)
		{
			return nullptr;
		}

		NewModel->SetNoNewGenerate();
		NewModel->SetUUID(LessRevokeData->ModelInfo.UUID);
		NewModel->GetPropertySharedPtr()->CopyData(LessRevokeData->ModelInfo.Property);

		UDSModelDependencySubsystem::GetInstance()->SetGeneratedLineRelations(NewModel->GetUUID(), LessRevokeData->RelatedModels);

		UDSMVCSubsystem::GetInstance()->SpawnViewUnion(NewModel);

		NewModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);

		return NewModel;
	}
	return nullptr;
}

void UDSRevokeLibrary::ConstructOppositeRevokeData_GeneratedLine(const UDSGeneratedLineRevokeData* InRevokeData,
	UDSGeneratedLineRevokeData* OutRevokeData)
{
	if (InRevokeData == nullptr || OutRevokeData == nullptr)
	{
		return;
	}

	EDSGeneratedLineRevokeType OppositeCommand = GetOppositeCommandType(InRevokeData->RevokeType);
	OutRevokeData->RevokeType = OppositeCommand;
	OutRevokeData->ModelInfo.UUID = InRevokeData->ModelInfo.UUID;
	OutRevokeData->ModelInfo.ModelType = InRevokeData->ModelInfo.ModelType;

	if (InRevokeData->ModelInfo.UUID.IsEmpty() || InRevokeData->ModelInfo.ModelType == EDSModelType::E_None)
	{
		OutRevokeData->ModelInfo.SetProperty(StaticCastSharedPtr<FDSGeneratedLineBaseProperty>(InRevokeData->ModelInfo.Property), false);
		return;
	}

	UDSBaseModel* LineModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InRevokeData->ModelInfo.ModelType, InRevokeData->ModelInfo.UUID);
	if (IsValid(LineModel))
	{
		OutRevokeData->ModelInfo.SetProperty(LineModel->GetTypedProperty<FDSGeneratedLineBaseProperty>(), false);
	}
	else
	{
		if (InRevokeData->ModelInfo.Property)
		{
			OutRevokeData->ModelInfo.SetProperty(StaticCastSharedPtr<FDSGeneratedLineBaseProperty>(InRevokeData->ModelInfo.Property), false);
		}
	}
}

void UDSRevokeLibrary::ConstructRevokeData_GeneratedLine(UDSBaseModel* InModel, UDSGeneratedLineRevokeData* OutPtr)
{
	if (IsValid(InModel) && OutPtr != nullptr)
	{
		OutPtr->ModelInfo.UUID = InModel->GetUUID();
		OutPtr->ModelInfo.ModelType = InModel->GetModelType();
		OutPtr->ModelInfo.SetProperty(StaticCastSharedPtr<FDSGeneratedLineBaseProperty>(InModel->GetPropertySharedPtr()), false);
	}
}

void UDSRevokeLibrary::ExecuteCommandRetOpposite_LineEntity(UDSRevokeData* InRevokeData, UDSRevokeData* OutRevokeData)
{
	UDSLineEntityRevokeData* OldRevokeData = Cast<UDSLineEntityRevokeData>(InRevokeData);
	UDSLineEntityRevokeData* OppositeRevokeData = Cast<UDSLineEntityRevokeData>(OutRevokeData);
	if (OldRevokeData == nullptr || OppositeRevokeData == nullptr)
	{
		return;
	}

	EDSLineEntityRevokeType OppositeCommand = GetOppositeCommandType(OldRevokeData->RevokeType);
	ConstructOppositeRevokeData_LineEntity(OldRevokeData, OppositeRevokeData);

	switch (OppositeCommand)
	{
	case EDSLineEntityRevokeType::E_Hidden:
	{
		UDSBaseModel* Model = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(OldRevokeData->LinkModelInfo.ModelType, OldRevokeData->LinkModelInfo.UUID);
		if (Model == nullptr)
		{
			return;
		}

		TArray<TWeakObjectPtr<UDSBaseModel>> LinkModels = Model->GetLinkModels().Array();
		for (const TWeakObjectPtr<UDSBaseModel>& LinkModel : LinkModels)
		{
			if (LinkModel->GetModelType() != EDSModelType::E_Generated_LineEntity)
			{
				continue;
			}

			TSharedPtr<FDSGeneratedLineEntityProperty> Property = LinkModel->GetTypedProperty<FDSGeneratedLineEntityProperty>();
			if (Property->RelatedLines == StaticCastSharedPtr<FDSGeneratedLineEntityProperty>(OldRevokeData->ModelInfo.Property)->RelatedLines)
			{
				FDSModelExecuteType NewAction = LinkModel->HasHidden() ? FDSModelExecuteType::ExecuteUnHidden : FDSModelExecuteType::ExecuteHidden;
				LinkModel->OnExecuteAction(NewAction);
				break;
			}
		}
	}
	break;
	default:
		break;
	}
}

UDSBaseModel* UDSRevokeLibrary::SpawnLessLineEntity(const UDSLineEntityRevokeData* LessRevokeData)
{
	return nullptr;
}

void UDSRevokeLibrary::ConstructOppositeRevokeData_LineEntity(const UDSLineEntityRevokeData* InRevokeData,
	UDSLineEntityRevokeData* OutRevokeData)
{
	if (InRevokeData == nullptr || OutRevokeData == nullptr)
	{
		return;
	}

	EDSLineEntityRevokeType OppositeCommand = GetOppositeCommandType(InRevokeData->RevokeType);
	OutRevokeData->RevokeType = OppositeCommand;
	OutRevokeData->ModelInfo.UUID = InRevokeData->ModelInfo.UUID;
	OutRevokeData->ModelInfo.ModelType = InRevokeData->ModelInfo.ModelType;
	OutRevokeData->LinkModelInfo = InRevokeData->LinkModelInfo;

	if (InRevokeData->ModelInfo.UUID.IsEmpty() || InRevokeData->ModelInfo.ModelType == EDSModelType::E_None)
	{
		OutRevokeData->ModelInfo.SetProperty(StaticCastSharedPtr<FDSGeneratedLineEntityProperty>(InRevokeData->ModelInfo.Property), false);
		return;
	}

	bool bShouldUseOldProperty = true;

	UDSBaseModel* LineModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InRevokeData->LinkModelInfo.ModelType, InRevokeData->LinkModelInfo.UUID);
	if (IsValid(LineModel))
	{
		TArray<TWeakObjectPtr<UDSBaseModel>> LinkModels = LineModel->GetLinkModels().Array();
		for (const TWeakObjectPtr<UDSBaseModel>& LinkModel : LinkModels)
		{
			if (LinkModel->GetModelType() != EDSModelType::E_Generated_LineEntity)
			{
				continue;
			}

			TSharedPtr<FDSGeneratedLineEntityProperty> EntityProperty = LinkModel->GetTypedProperty<FDSGeneratedLineEntityProperty>();
			if (EntityProperty->RelatedLines == StaticCastSharedPtr<FDSGeneratedLineEntityProperty>(InRevokeData->ModelInfo.Property)->RelatedLines)
			{
				OutRevokeData->ModelInfo.SetProperty(EntityProperty, false);
				bShouldUseOldProperty = false;
				break;
			}
		}
	}

	if (bShouldUseOldProperty)
	{
		if (InRevokeData->ModelInfo.Property)
		{
			OutRevokeData->ModelInfo.SetProperty(StaticCastSharedPtr<FDSGeneratedLineEntityProperty>(InRevokeData->ModelInfo.Property), false);
		}
	}
}

void UDSRevokeLibrary::ConstructRevokeData_LineEntity(UDSBaseModel* InModel, UDSLineEntityRevokeData* OutPtr)
{
	if (IsValid(InModel) && OutPtr != nullptr)
	{
		OutPtr->ModelInfo.UUID = InModel->GetUUID();
		OutPtr->ModelInfo.ModelType = InModel->GetModelType();
		OutPtr->ModelInfo.SetProperty(StaticCastSharedPtr<FDSGeneratedLineEntityProperty>(InModel->GetPropertySharedPtr()), false);
	}
}

void UDSRevokeLibrary::ExecuteCommandRetOpposite_SoftFurniture(UDSRevokeData* InRevokeDataPtr, UDSRevokeData* OutRevokeDataPtr)
{
	//成品撤销实现
	checkf(InRevokeDataPtr != nullptr, TEXT("ExecuteCommandRetOpposite_SoftFurniture -- InRevokeDataPtr is nullptr"));
	checkf(OutRevokeDataPtr != nullptr, TEXT("ExecuteCommandRetOpposite_SoftFurniture -- OutRevokeDataPtr is nullptr"));

	UDSSoftFurnitureRevokeData* InRevokeData = Cast<UDSSoftFurnitureRevokeData>(InRevokeDataPtr);
	UDSSoftFurnitureRevokeData* OutRevokeData = Cast<UDSSoftFurnitureRevokeData>(OutRevokeDataPtr);
	if (InRevokeData == nullptr || OutRevokeData == nullptr)
		return;

	EDSSoftFurnitureRevokeType OppositeCommand = UDSRevokeLibrary::GetOppositeCommandType(InRevokeData->RevokeType);
	UDSRevokeLibrary::ConstructOppositeRevokeData_SoftFurniture(InRevokeData, OutRevokeData);

	if (OppositeCommand == EDSSoftFurnitureRevokeType::E_Delete)
	{
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InRevokeData->GetModelInfo().GetModelType(), InRevokeData->GetModelInfo().GetUUID());
		if (IsValid(EditModel))
		{
			EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);

			UDSBaseModel* LinkModel = UDSMVCSubsystem::GetInstance()->GetModelByID(InRevokeData->GetLinkUUID());
			if (IsValid(LinkModel))
			{
				LinkModel->DeleteLinkModel(EditModel);
			}
		}
	}
	else if (OppositeCommand == EDSSoftFurnitureRevokeType::E_Refresh)
	{
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InRevokeData->GetModelInfo().GetModelType(), InRevokeData->GetModelInfo().GetUUID());
		if (IsValid(EditModel))
		{
			UDSToolLibrary::ParsePropertyCopy(EditModel, InRevokeData->GetProperty());
			EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateByProperty);
		}
	}
	else if (OppositeCommand == EDSSoftFurnitureRevokeType::E_Spawn)
	{
		UDSRevokeLibrary::SpawnLessSoftFurniture(InRevokeData);
	}
	else if (OppositeCommand == EDSSoftFurnitureRevokeType::E_Transform)
	{
		UDSRevokeLibrary::TransformUnion(
			InRevokeData->GetModelInfo().GetUUID(),
			InRevokeData->GetModelInfo().GetModelType(),
            InRevokeData->GetProperty()
		);
	}
	else if (OppositeCommand == EDSSoftFurnitureRevokeType::E_FlipHidden)
	{
		UDSRevokeLibrary::FlipHiddenUnion(InRevokeData->GetModelInfo().GetUUID(), InRevokeData->GetModelInfo().GetModelType());

	}
}

UDSBaseModel* UDSRevokeLibrary::SpawnLessSoftFurniture(const UDSSoftFurnitureRevokeData* LessRevokeData)
{
	UDSBaseModel* NewModel = nullptr;
	EDSModelType NewType = LessRevokeData->GetModelInfo().GetModelType();
	if (NewType == EDSModelType::E_Furniture_HouseFurniture)
	{
		NewModel = NewObject<UDSSoftFurnitureModel>();
		NewModel->SetUUID(LessRevokeData->GetModelInfo().GetUUID());
	}

	if (NewModel == nullptr)
		return nullptr;

	NewModel->SetNoNewGenerate();
	UDSToolLibrary::ParsePropertyCopy(NewModel, LessRevokeData->GetProperty());

	UDSMVCSubsystem::GetInstance()->SpawnViewUnion(NewModel);

	NewModel->OnExecuteAction(FDSModelExecuteType::ExecuteGenerateFormProperty);

	UDSBaseModel* LinkModel = UDSMVCSubsystem::GetInstance()->GetModelByID(LessRevokeData->GetLinkUUID());
	if (IsValid(LinkModel))
	{
		LinkModel->AddLinkModel(NewModel);
	}

	if (LessRevokeData->GroupMarkData.IsGroupItem())
	{
		UDSBaseModel* GroupModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(
			EDSModelType::E_Group, LessRevokeData->GroupMarkData.GetGroupUUID());
		if (GroupModel != nullptr && Cast<UDSGroupModel>(GroupModel))
		{
			Cast<UDSGroupModel>(GroupModel)->AddItem(NewModel, false);
		}
	}

	return NewModel;
}

void UDSRevokeLibrary::ConstructOppositeRevokeData_SoftFurniture(const UDSSoftFurnitureRevokeData* InPtr, UDSSoftFurnitureRevokeData* OutPtr)
{
	if (InPtr == nullptr || OutPtr == nullptr)
	{
		return;
	}

	if (InPtr == nullptr || OutPtr == nullptr)
	{
		return;
	}

	EDSSoftFurnitureRevokeType OppositeCommand = UDSRevokeLibrary::GetOppositeCommandType(InPtr->RevokeType);
	OutPtr->SetRevokeType(OppositeCommand);
	OutPtr->SetModelInfo(InPtr->ModelInfo.GetUUID(), InPtr->ModelInfo.GetModelType());

	if (!InPtr->ModelInfo.GetUUID().IsEmpty() && InPtr->ModelInfo.GetModelType() != EDSModelType::E_None)
	{//
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InPtr->ModelInfo.GetModelType(), InPtr->ModelInfo.GetUUID());

		if (IsValid(EditModel))
		{
			if (InPtr->ModelInfo.GetModelType() == EDSModelType::E_Furniture_HouseFurniture)
			{
				OutPtr->SetProperty(StaticCastSharedPtr<FDSSoftFurnitureProperty>(EditModel->GetPropertySharedPtr()), false);
			}
		}
		else
		{
			if (InPtr->ModelInfo.GetModelType() == EDSModelType::E_Furniture_HouseFurniture)
			{
				OutPtr->SetProperty(StaticCastSharedPtr<FDSSoftFurnitureProperty>(InPtr->GetProperty()), true);
			}
		}

		OutPtr->SetLinkUUID(InPtr->GetLinkUUID());
	}
	else
	{//property change
		if (InPtr->ModelInfo.GetModelType() == EDSModelType::E_Furniture_HouseFurniture)
		{
			OutPtr->SetProperty(StaticCastSharedPtr<FDSSoftFurnitureProperty>(InPtr->GetProperty()), true);
		}
	}
}

void UDSRevokeLibrary::ConstructRevokeData_SoftFurniture(UDSBaseModel* InModel, UDSSoftFurnitureRevokeData* OutPtr)
{
	if (InModel != nullptr || OutPtr != nullptr)
	{
		OutPtr->SetModelInfo(InModel->GetUUID(), InModel->GetModelType());
		OutPtr->SetProperty(StaticCastSharedPtr<FDSSoftFurnitureProperty>(InModel->GetPropertySharedPtr()), false);
	}
}

void UDSRevokeLibrary::ExecuteCommandRetOpposite_ModelCeiling(UDSRevokeData* InRevokeDataPtr, UDSRevokeData* OutRevokeDataPtr)
{
	checkf(InRevokeDataPtr != nullptr, TEXT("ExecuteCommandRetOpposite_ModelCeiling -- InRevokeDataPtr is nullptr"));
	checkf(OutRevokeDataPtr != nullptr, TEXT("ExecuteCommandRetOpposite_ModelCeiling -- OutRevokeDataPtr is nullptr"));

	UDSModelCeilingRevokeData* InRevokeData = Cast<UDSModelCeilingRevokeData>(InRevokeDataPtr);
	UDSModelCeilingRevokeData* OutRevokeData = Cast<UDSModelCeilingRevokeData>(OutRevokeDataPtr);
	if (InRevokeData == nullptr || OutRevokeData == nullptr)
		return;

	EDSModelCeilingRevokeType OppositeCommand = UDSRevokeLibrary::GetOppositeCommandType(InRevokeData->RevokeType);
	UDSRevokeLibrary::ConstructOppositeRevokeData_ModelCeiling(InRevokeData, OutRevokeData);

	if (OppositeCommand == EDSModelCeilingRevokeType::E_Delete)
	{
		UDSRevokeLibrary::DeleteModelCeiling(InRevokeData);
	}
	else if (OppositeCommand == EDSModelCeilingRevokeType::E_Refresh)
	{
		UDSRevokeLibrary::UpdateModelCeiling(InRevokeData);
	}
	else if (OppositeCommand == EDSModelCeilingRevokeType::E_Spawn)
	{
		UDSRevokeLibrary::SpawnLessModelCeiling(InRevokeData);
	}
	else if (OppositeCommand == EDSModelCeilingRevokeType::E_Transform)
	{
		UDSRevokeLibrary::TransformUnion(
			InRevokeData->GetModelInfo().GetUUID(),
			InRevokeData->GetModelInfo().GetModelType(),
            InRevokeData->GetProperty()
		);
	}
	else if (OppositeCommand == EDSModelCeilingRevokeType::E_FlipHidden)
	{
		UDSRevokeLibrary::FlipHiddenUnion(InRevokeData->GetModelInfo().GetUUID(), InRevokeData->GetModelInfo().GetModelType());
	}
}

UDSBaseModel* UDSRevokeLibrary::SpawnLessModelCeiling(const UDSModelCeilingRevokeData* LessRevokeData)
{
	UDSBaseModel* NewModel = NewObject<UDSMoldingCeilingModel>();
	NewModel->SetNoNewGenerate();
	UDSToolLibrary::ParsePropertyCopy(NewModel, LessRevokeData->GetProperty());

	UDSMVCSubsystem::GetInstance()->SpawnViewUnion(NewModel);

	NewModel->OnExecuteAction(FDSModelExecuteType::ExecuteGenerateFormProperty);

	return NewModel;
}

void UDSRevokeLibrary::DeleteModelCeiling(const UDSModelCeilingRevokeData* InRevokeData)
{
	if(InRevokeData != nullptr)
	{
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InRevokeData->GetModelInfo().GetModelType(), InRevokeData->GetModelInfo().GetUUID());
		if (IsValid(EditModel))
		{
			EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
		}
	}
}

void UDSRevokeLibrary::UpdateModelCeiling(const UDSModelCeilingRevokeData* InRevokeData)
{
	if(InRevokeData != nullptr)
	{
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InRevokeData->GetModelInfo().GetModelType(), InRevokeData->GetModelInfo().GetUUID());
		if (IsValid(EditModel))
		{
			UDSToolLibrary::ParsePropertyCopy(EditModel, InRevokeData->GetProperty());
			EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateByProperty);
		}
	}
}

void UDSRevokeLibrary::ConstructOppositeRevokeData_ModelCeiling(const UDSModelCeilingRevokeData* InPtr, UDSModelCeilingRevokeData* OutPtr)
{
	if (InPtr == nullptr || OutPtr == nullptr)
	{
		return;
	}

	if (InPtr == nullptr || OutPtr == nullptr)
	{
		return;
	}

	EDSModelCeilingRevokeType OppositeCommand = UDSRevokeLibrary::GetOppositeCommandType(InPtr->RevokeType);
	OutPtr->SetRevokeType(OppositeCommand);
	OutPtr->SetModelInfo(InPtr->ModelInfo.GetUUID(), InPtr->ModelInfo.GetModelType());

	if (!InPtr->ModelInfo.GetUUID().IsEmpty() && InPtr->ModelInfo.GetModelType() != EDSModelType::E_None)
	{//
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InPtr->ModelInfo.GetModelType(), InPtr->ModelInfo.GetUUID());

		if (IsValid(EditModel))
		{
			if (InPtr->ModelInfo.GetModelType() == EDSModelType::E_Furniture_HouseFurniture)
			{
				OutPtr->SetProperty(StaticCastSharedPtr<FDSMoldingCeilingProperty>(EditModel->GetPropertySharedPtr()), false);
			}
		}
		else
		{
			if (InPtr->ModelInfo.GetModelType() == EDSModelType::E_Furniture_HouseFurniture)
			{
				OutPtr->SetProperty(StaticCastSharedPtr<FDSMoldingCeilingProperty>(InPtr->GetProperty()), true);
			}
		}
	}
	else
	{//property change
		if (InPtr->ModelInfo.GetModelType() == EDSModelType::E_Furniture_HouseFurniture)
		{
			OutPtr->SetProperty(StaticCastSharedPtr<FDSMoldingCeilingProperty>(InPtr->GetProperty()), true);
		}
	}
}

void UDSRevokeLibrary::ConstructRevokeData_ModelCeiling(UDSBaseModel* InModel, UDSModelCeilingRevokeData* OutPtr)
{
	if (InModel != nullptr || OutPtr != nullptr)
	{
		OutPtr->SetModelInfo(InModel->GetUUID(), InModel->GetModelType());
		OutPtr->SetProperty(StaticCastSharedPtr<FDSMoldingCeilingProperty>(InModel->GetPropertySharedPtr()), false);
	}
}

UDSBaseModel* UDSRevokeLibrary::SpawnLessCT(UDSCounterTopRevokeData* LessRevokeData)
{
	if (LessRevokeData != nullptr)
	{
		if (LessRevokeData->IsSideCT)
		{//side
			UDSSideCounterTopModel* NewModel = NewObject<UDSSideCounterTopModel>();
			NewModel->SetUUID(LessRevokeData->ModelUUID);
			NewModel->SetProperty(LessRevokeData->GetSidePropertyData().Get());
			NewModel->SetNoNewGenerate();
			UDSMVCSubsystem::GetInstance()->SpawnViewUnion(NewModel);
			NewModel->SetVisual(LessRevokeData->GetVisualState());

			//recover dependent info before upadte
			FSideCounterTopDependencyInfo SideCTDependency;
			UDSRevokeLibrary::ConstructSideCTRelation(LessRevokeData->GetSideCTDependencyInfoRef(), &SideCTDependency);
			UDSModelDependencySubsystem::GetInstance()->SetSideCounterTopDependency(NewModel->GetUUID(), SideCTDependency);


			NewModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);

			if (NewModel->GetVisual())
			{
				UDSCounterTopLibrary::GenerateCounterTopEditPointsAndLines(NewModel);
			}

		}
		else
		{
			UDSCounterTopModel* NewModel = NewObject<UDSCounterTopModel>();
			NewModel->SetUUID(LessRevokeData->ModelUUID);
			NewModel->SetProperty(LessRevokeData->GetPropertyData().Get());
			NewModel->SetNoNewGenerate();
			UDSMVCSubsystem::GetInstance()->SpawnViewUnion(NewModel);
			NewModel->SetVisual(LessRevokeData->GetVisualState());

			TArray<FString> CTDependency;
			UDSRevokeLibrary::ConstructCTRelation(LessRevokeData->GetCTDependencyInfoRef(), CTDependency);
			if (!CTDependency.IsEmpty())
			{
				UDSModelDependencySubsystem::GetInstance()->SetCounterTopRelations(NewModel->GetUUID(), CTDependency);
			}

			NewModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);

			if (NewModel->GetVisual())
			{
				UDSCounterTopLibrary::GenerateCounterTopEditPointsAndLines(NewModel);
			}

		}
	}
	return nullptr;
}

void UDSRevokeLibrary::ConstructOppositeRevokeData_CT(UDSCounterTopRevokeData* InPtr, UDSCounterTopRevokeData* OutPtr)
{
	if (InPtr == nullptr || OutPtr == nullptr)
	{
		return;
	}

	EDSCTRevokeType OppositeCommand = UDSRevokeLibrary::GetOppositeCTCommandType(InPtr->RevokeType);
	OutPtr->SetRevokeType(OppositeCommand);
	OutPtr->SetModelInfo(InPtr->ModelUUID, InPtr->ModelType, InPtr->IsSideCT);
	OutPtr->SetVisualState(InPtr->GetVisualState());
	if (!InPtr->ModelUUID.IsEmpty() && InPtr->ModelType != EDSModelType::E_None)
	{//
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InPtr->ModelType, InPtr->ModelUUID);
		if (IsValid(EditModel))
		{
			EDSModelType EditType = EditModel->GetModelType();
			if (EditType == EDSModelType::E_Generated_CounterTop)
			{
				OutPtr->SetPropertyData(StaticCastSharedPtr<FDSCounterTopProperty>(EditModel->GetPropertySharedPtr()), false);
			}
			else if (EditType == EDSModelType::E_Generated_SideCounterTop)
			{
				OutPtr->SetSidePropertyData(StaticCastSharedPtr<FDSSideCounterTopProperty>(EditModel->GetPropertySharedPtr()), false);
			}
			else if (EditType == EDSModelType::E_Generated_CounterTop_Line)
			{
				OutPtr->SetLinePropertyData(StaticCastSharedPtr<FDSCounterTopLineProperty>(EditModel->GetPropertySharedPtr()), false);
			}
			else if (EditType == EDSModelType::E_Generated_CounterTop_Point)
			{
				OutPtr->SetPointPropertyData(StaticCastSharedPtr<FDSCounterTopPointProperty>(EditModel->GetPropertySharedPtr()), false);
			}
		}
		else
		{
			if (InPtr->GetPropertyData().IsValid())
			{
				OutPtr->SetPropertyData(StaticCastSharedPtr<FDSCounterTopProperty>(InPtr->GetPropertyData()), false);
			}
			if (InPtr->GetSidePropertyData().IsValid())
			{
				OutPtr->SetSidePropertyData(StaticCastSharedPtr<FDSSideCounterTopProperty>(InPtr->GetSidePropertyData()), false);
			}
			if (InPtr->GetLinePropertyData().IsValid())
			{
				OutPtr->SetLinePropertyData(StaticCastSharedPtr<FDSCounterTopLineProperty>(InPtr->GetLinePropertyData()), false);
			}
			if (InPtr->GetPointPropertyData().IsValid())
			{
				OutPtr->SetPointPropertyData(StaticCastSharedPtr<FDSCounterTopPointProperty>(InPtr->GetPointPropertyData()), false);
			}
		}

		OutPtr->SetCTDependencyInfo(InPtr->GetCTDependencyInfoRef());
		OutPtr->SetSideCTDependencyInfo(InPtr->GetSideCTDependencyInfoRef());
		OutPtr->SetLinkUUIDs(InPtr->GetLinkUUIDs());
	}
	else
	{//property change
		TSharedPtr<FDSCounterTopProperty> CurrentProperty = InPtr->GetNewWidgetProperty();
		OutPtr->SetNewWidgetProperty(InPtr->GetPropertyData());
		OutPtr->SetPropertyData(CurrentProperty, true);
	}
}

void UDSRevokeLibrary::UpdatePushDataProperty(UDSBaseModel* EditModel, FDSCounterTopPushData& ModifyPushData)
{
	if (IsValid(EditModel))
	{
		EDSModelType EditType = EditModel->GetModelType();
		if (EditType == EDSModelType::E_Generated_CounterTop)
		{
			ModifyPushData.SetCTPropertyData(StaticCastSharedPtr<FDSCounterTopProperty>(EditModel->GetPropertySharedPtr()), false);
		}
		else if (EditType == EDSModelType::E_Generated_SideCounterTop)
		{
			ModifyPushData.SetCTPropertyData(StaticCastSharedPtr<FDSSideCounterTopProperty>(EditModel->GetPropertySharedPtr()), false);
		}
		else if (EditType == EDSModelType::E_Generated_CounterTop_Line)
		{
			ModifyPushData.SetCTLinePropertyData(StaticCastSharedPtr<FDSCounterTopLineProperty>(EditModel->GetPropertySharedPtr()), false);
		}
		else if (EditType == EDSModelType::E_Generated_CounterTop_Point)
		{
			ModifyPushData.SetCTPointPropertyData(StaticCastSharedPtr<FDSCounterTopPointProperty>(EditModel->GetPropertySharedPtr()), false);
		}
	}
}

void UDSRevokeLibrary::ParseCTProperty(UDSBaseModel* EditModel, UDSCounterTopRevokeData* RevokePtr)
{
	if (IsValid(EditModel) && RevokePtr != nullptr)
	{
		EDSModelType EditType = EditModel->GetModelType();
		if (EditType == EDSModelType::E_Generated_CounterTop)
		{
			EditModel->GetPropertySharedPtr()->CopyData(RevokePtr->GetPropertyData());
		}
		else if (EditType == EDSModelType::E_Generated_SideCounterTop)
		{
			EditModel->GetPropertySharedPtr()->CopyData(RevokePtr->GetSidePropertyData());
		}
		else if (EditType == EDSModelType::E_Generated_CounterTop_Line)
		{
			EditModel->GetPropertySharedPtr()->CopyData(RevokePtr->GetLinePropertyData());
		}
		else if (EditType == EDSModelType::E_Generated_CounterTop_Point)
		{
			EditModel->GetPropertySharedPtr()->CopyData(RevokePtr->GetPointPropertyData());
		}
	}
}

TArray<UDSRevokeData*> UDSRevokeLibrary::ExecuteSnapshotCommandRetOpposite_GeneratedLine(
	const TArray<UDSRevokeData*>& InRevokeDataList)
{
	TArray<UDSRevokeData*> OppositeDataList;

	TArray<UDSBaseModel*> AllLineModels = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_Generated_CrownMoulding, EDSModelType::E_Generated_LightCord, EDSModelType::E_Generated_SkirtingBoard });
	for (TArray<UDSBaseModel*>::TIterator It = AllLineModels.CreateIterator(); It; ++It)
	{
		UDSGeneratedLineRevokeData* NewRevokeData = NewObject<UDSGeneratedLineRevokeData>();
		NewRevokeData->ModelInfo.UUID = (*It)->GetUUID();
		NewRevokeData->ModelInfo.ModelType = (*It)->GetModelType();
		NewRevokeData->ModelInfo.SetProperty((*It)->GetTypedProperty<FDSGeneratedLineBaseProperty>(), false);
		NewRevokeData->RelatedModels = UDSModelDependencySubsystem::GetInstance()->FindGeneratedLineRelations(NewRevokeData->ModelInfo.UUID);

		OppositeDataList.Add(NewRevokeData);

		bool bNeedDeleteModel = !InRevokeDataList.ContainsByPredicate([&](UDSRevokeData* InRevokeData)
			{
				if (UDSGeneratedLineRevokeData* LineRevokeData = Cast<UDSGeneratedLineRevokeData>(InRevokeData))
				{
					return LineRevokeData->ModelInfo.UUID == (*It)->GetUUID() && LineRevokeData->ModelInfo.ModelType == (*It)->GetModelType();
				}
				return false;
			});

		if (bNeedDeleteModel)
		{
			UDSModelDependencySubsystem::GetInstance()->RemoveGeneratedLineRelation(NewRevokeData->ModelInfo.UUID);

			(*It)->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
			It.RemoveCurrent();
		}
	}

	for (UDSRevokeData* RevokeData : InRevokeDataList)
	{
		UDSGeneratedLineRevokeData* LineRevokeData = Cast<UDSGeneratedLineRevokeData>(RevokeData);
		if (LineRevokeData == nullptr)
		{
			continue;
		}

		int32 ModelPos = AllLineModels.IndexOfByPredicate([&](UDSBaseModel* InModel)
			{
				return LineRevokeData->ModelInfo.UUID == InModel->GetUUID() && LineRevokeData->ModelInfo.ModelType == InModel->GetModelType();
			});

		if (ModelPos == INDEX_NONE)
		{
			UDSBaseModel* NewLineModel = nullptr;
			switch (LineRevokeData->ModelInfo.ModelType)
			{
			case EDSModelType::E_Generated_CrownMoulding:
				NewLineModel = NewObject<UDSCrownMouldingModel>();
				break;
			case EDSModelType::E_Generated_LightCord:
				NewLineModel = NewObject<UDSLightCordModel>();
				break;
			case EDSModelType::E_Generated_SkirtingBoard:
				NewLineModel = NewObject<UDSSkirtingBoardModel>();
				break;
			default:
				break;
			}

			if (NewLineModel != nullptr)
			{
				NewLineModel->SetNoNewGenerate();
				NewLineModel->SetUUID(LineRevokeData->ModelInfo.UUID);
				NewLineModel->SetProperty(LineRevokeData->ModelInfo.Property.Get());

				UDSMVCSubsystem::GetInstance()->SpawnViewUnion(NewLineModel);

				NewLineModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
			}
		}
		else
		{
			AllLineModels[ModelPos]->SetProperty(LineRevokeData->ModelInfo.Property.Get());
			AllLineModels[ModelPos]->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
		}

		UDSModelDependencySubsystem::GetInstance()->SetGeneratedLineRelations(LineRevokeData->ModelInfo.UUID, LineRevokeData->RelatedModels);
	}

	return OppositeDataList;
}

TArray<UDSRevokeData*> UDSRevokeLibrary::ExecuteCommandRetOpposite_SnapShot(UDSCommandCore* InCore)
{
	TArray<UDSRevokeData*> Res = TArray<UDSRevokeData*>();
	if (InCore != nullptr)
	{
		if(InCore->GetCommandType() == EDSRevokeType::E_House)
		{
			Res = UDSRevokeLibrary::ExecuteCommandRetOpposite_House_SnapShot(Cast<UHouseCommand>(InCore));
		}
		else if (InCore->GetCommandType() == EDSRevokeType::E_CounterTop)
		{
			Res = ExecuteCommandRetOpposite_CT_SnapShot(Cast<UCounterTopCommand>(InCore));
		}
		else if (InCore->GetCommandType() == EDSRevokeType::E_Sink)
		{
			Res = UDSRevokeLibrary::ExecuteCommandRetOpposite_SWT_SnapShot(Cast<USinkCommand>(InCore));
		}
		else if (InCore->GetCommandType() == EDSRevokeType::E_StoveRangeHood)
		{
			Res = UDSRevokeLibrary::ExecuteCommandRetOpposite_SRH_SnapShot(Cast<USRHCommand>(InCore));
		}
	}

	return Res;
}

TArray<UDSRevokeData*> UDSRevokeLibrary::ExecuteCommandRetOpposite_House_SnapShot(UHouseCommand* InCore)
{
	// current house data	
	UDSRevokeData* CurData = NewObject<UDSRevokeData>();
	UDSRevokeLibrary::ConstructRevokeData_CurrentHouse(CurData);
	UDSRevokeData* WinDoorData = NewObject<UDSRevokeData>();
    UDSRevokeLibrary::ConstructRevokeData_CurrentHouse_WinDoor(WinDoorData);
	UDSRevokeData* AreaData = NewObject<UDSRevokeData>();
    UDSRevokeLibrary::ConstructRevokeData_CurrentHouse_Area(AreaData);

	//revoke data
	UDSRevokeLibrary::CompareHouseData_Area(InCore->GetRevokeDataPtr_Area());
    UDSRevokeLibrary::CompareHouseData_WinDoor(InCore->GetRevokeDataPtr_WinDoor());
	UDSRevokeLibrary::CompareHouseData(InCore->GetRevokeDataPtr());
	UDSPathLibrary::GeneratePaths(EDSModelType::E_House_Wall);
	UDSPathLibrary::GenerateAreas();
	UDSPathLibrary::GeneratePaths(EDSModelType::E_House_Platform);
	UDSPathLibrary::GeneratePaths(EDSModelType::E_House_Beam);
	
	return { CurData, WinDoorData, AreaData };
}

TArray<UDSRevokeData*> UDSRevokeLibrary::ExecuteCommandRetOpposite_CT_SnapShot(UCounterTopCommand* InCore)
{
	TArray<UDSRevokeData*> Res;
	if (InCore != nullptr)
	{
		Res = UDSRevokeLibrary::ExecuteSnapshotCommandRetOpposite_CT(InCore->GetRevokeDataList());
	}
	return Res;
}

TArray<UDSRevokeData*> UDSRevokeLibrary::ExecuteCommandRetOpposite_SWT_SnapShot(USinkCommand* InCore)
{
	TArray<UDSRevokeData*> Res;
	if (InCore != nullptr)
	{
		TArray<UDSBaseModel*> CurrentSink = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_Custom_Sink });
		for (const auto& ASWT : CurrentSink)
		{
			if (!IsValid(ASWT)) continue;

			UDSSinkRevokeData* OppositeData = NewObject<UDSSinkRevokeData>();
			OppositeData->SetModelInfo(ASWT->GetUUID(), ASWT->GetModelType());
			OppositeData->SetExecuteType(FDSModelExecuteType::ExecuteUpdateByProperty);
			OppositeData->SetProperty(StaticCastSharedPtr<FDSSinkProperty>(ASWT->GetPropertySharedPtr()));
			OppositeData->SetLinkUUID(UDSModelDependencySubsystem::GetInstance()->FindCupboardBySink(ASWT->GetUUID()));

			Res.Add(OppositeData);
		}


		TArray<UDSBaseModel*> CurrentWaterTap = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_WaterTap });
		for (auto& CWT : CurrentWaterTap)
		{//delete all water tap
			if (!IsValid(CWT)) continue;

			CWT->OnExecuteAction(FDSModelExecuteType::ExecuteDelete, FDSBroadcastMarkData::NotBroadcastToMVCMark);
		}

		TArray<UDSSinkRevokeData*> ToSpawn;
		TMap<UDSBaseModel*, UDSSinkRevokeData*> ToUpdate;
		for (const auto& IT : InCore->GetRevokeDataList())
		{
			if (UDSSinkRevokeData* SSRData = Cast<UDSSinkRevokeData>(IT))
			{
				const int32 FindIndex = CurrentSink.IndexOfByPredicate(
					[SSRData](UDSBaseModel* CModel)->bool
					{
						if (CModel != nullptr)
						{
							FDSRevokeModelInner ModelInfo = SSRData->GetModelInfo();
							return ModelInfo.GetUUID().Equals(CModel->GetUUID()) && ModelInfo.GetModelType() == CModel->GetModelType();
						}

						return false;
					});
				if (FindIndex != INDEX_NONE)
				{
					ToUpdate.Add(CurrentSink[FindIndex], SSRData);

					CurrentSink.RemoveAt(FindIndex);
				}
				else
				{
					ToSpawn.AddUnique(SSRData);
				}
			}
		}

		//delete
		for (auto& CS : CurrentSink)
		{
			if (CS != nullptr)
			{
				CS->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
				UDSModelDependencySubsystem::GetInstance()->RemoveSink(CS->GetUUID());

				UDSBaseModel* LinkModel = UDSMVCSubsystem::GetInstance()->GetModelByID(CS->GetUUID());
				if (IsValid(LinkModel))
				{
					LinkModel->DeleteLinkModel(CS);
				}
			}
		}

		//spawn
		for (auto& SS : ToSpawn)
		{
			if (SS == nullptr) continue;

			UDSSinkModel* NewModel = NewObject<UDSSinkModel>();
			NewModel->SetNoNewGenerate();
			NewModel->SetUUID(SS->GetModelInfo().GetUUID());
			UDSToolLibrary::ParsePropertyCopy(NewModel, SS->GetProperty());

			UDSMVCSubsystem::GetInstance()->SpawnViewUnion(NewModel);

			NewModel->OnExecuteAction(FDSModelExecuteType::ExecuteGenerateFormProperty);

			UDSModelDependencySubsystem::GetInstance()->AddSinkAndCupboardRelation(NewModel->GetUUID(), SS->GetLinkUUID());

			UDSBaseModel* LinkModel = UDSMVCSubsystem::GetInstance()->GetModelByID(SS->GetLinkUUID());
			if (IsValid(LinkModel))
			{
				LinkModel->AddLinkModel(NewModel);
			}
		}

		//update
		for (auto& TU : ToUpdate)
		{
			if (TU.Key == nullptr || TU.Value == nullptr) continue;

			UDSToolLibrary::ParsePropertyCopy(TU.Key, TU.Value->GetProperty());
			TU.Key->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateByProperty);
		}
	}
	return Res;
}

TArray<UDSRevokeData*> UDSRevokeLibrary::ExecuteCommandRetOpposite_SRH_SnapShot(USRHCommand* InCore)
{
	TArray<UDSRevokeData*> Res;
	if (InCore != nullptr)
	{
		TArray<UDSBaseModel*> CurrentSRH = UDSMVCSubsystem::GetInstance()->GetModels({ EDSModelType::E_Custom_Stove, EDSModelType::E_Custom_RangeHood });
		for (const auto& CSRH : CurrentSRH)
		{//construct current srh redo data
			if (CSRH == nullptr) continue;

			UDSSRHRevokeData* NewData = NewObject<UDSSRHRevokeData>();
			NewData->SetExecuteType(FDSModelExecuteType::ExecuteUpdateByProperty);
			NewData->SetRevokeType(EDSSRHRevokeType::E_Refresh);
			NewData->SetModelInfo(CSRH->GetUUID(), CSRH->GetModelType());
			if (CSRH->GetModelType() == EDSModelType::E_Custom_Stove)
			{
				NewData->SetProperty(StaticCastSharedPtr<FDSStoveProperty>(CSRH->GetPropertySharedPtr()));
				NewData->SetLinkUUID(UDSModelDependencySubsystem::GetInstance()->FindCupboardByStove(CSRH->GetUUID()));
			}
			else if (CSRH->GetModelType() == EDSModelType::E_Custom_RangeHood)
			{
				NewData->SetProperty(StaticCastSharedPtr<FDSRangeHoodProperty>(CSRH->GetPropertySharedPtr()));
				NewData->SetLinkUUID(UDSModelDependencySubsystem::GetInstance()->FindStoveByRangeHood(CSRH->GetUUID()));
			}
			else
			{
				checkNoEntry();
			}


			Res.Add(NewData);
		}


		TArray<UDSSRHRevokeData*> ToSpawn;
		TMap<UDSBaseModel*, UDSSRHRevokeData*> ToUpdate;
		for (const auto& IT : InCore->GetRevokeDataList())
		{
			if (UDSSRHRevokeData* SRHData = Cast<UDSSRHRevokeData>(IT))
			{
				const int32 FindIndex = CurrentSRH.IndexOfByPredicate(
					[SRHData](UDSBaseModel* CModel)->bool
					{
						if (CModel != nullptr)
						{
							FDSRevokeModelInner ModelInfo = SRHData->GetModelInfo();
							return ModelInfo.GetUUID().Equals(CModel->GetUUID()) && ModelInfo.GetModelType() == CModel->GetModelType();
						}

						return false;
					});
				if (FindIndex != INDEX_NONE)
				{
					ToUpdate.Add(CurrentSRH[FindIndex], SRHData);

					CurrentSRH.RemoveAt(FindIndex);
				}
				else
				{
					ToSpawn.AddUnique(SRHData);
				}
			}
		}

		//比对剩余的表示是新增的，删除
		for (auto& Remain : CurrentSRH)
		{
			if (Remain != nullptr)
			{
				Remain->OnExecuteAction(FDSModelExecuteType::ExecuteDelete);
				if (Remain->GetModelType() == EDSModelType::E_Custom_Stove)
				{
					UDSModelDependencySubsystem::GetInstance()->RemoveStove(Remain->GetUUID());
				}
				else if (Remain->GetModelType() == EDSModelType::E_Custom_RangeHood)
				{
					UDSModelDependencySubsystem::GetInstance()->RemoveRangeHoodRelatedToStove(Remain->GetUUID());
				}
			}
		}

		//spawn
		for (auto& SpawnData : ToSpawn)
		{
			if (SpawnData != nullptr)
			{
				UDSBaseModel* NewModel = nullptr;
				//根据类型生成灶具、烟机
				EDSModelType NewType = SpawnData->GetModelInfo().GetModelType();
				if (NewType == EDSModelType::E_Custom_Stove)
				{//stove -- cupboard
					NewModel = NewObject<UDSStoveModel>();
					NewModel->SetUUID(SpawnData->GetModelInfo().GetUUID());
					UDSModelDependencySubsystem::GetInstance()->AddStoveAndCupboardRelation(NewModel->GetUUID(), SpawnData->GetLinkUUID());
				}
				else if (NewType == EDSModelType::E_Custom_RangeHood)
				{// stove -- rangehood
					NewModel = NewObject<UDSRangeHoodModel>();
					NewModel->SetUUID(SpawnData->GetModelInfo().GetUUID());
					UDSModelDependencySubsystem::GetInstance()->AddStoveAndRangeHoodRelation(SpawnData->GetLinkUUID(), NewModel->GetUUID());
				}

				if (NewModel == nullptr)
					break;

				NewModel->SetNoNewGenerate();
				NewModel->SetUUID(SpawnData->GetModelInfo().GetUUID());
				UDSToolLibrary::ParsePropertyCopy(NewModel, SpawnData->GetProperty());

				UDSMVCSubsystem::GetInstance()->SpawnViewUnion(NewModel);

				NewModel->OnExecuteAction(FDSModelExecuteType::ExecuteGenerateFormProperty);



				UDSBaseModel* LinkModel = UDSMVCSubsystem::GetInstance()->GetModelByID(SpawnData->GetLinkUUID());
				if (IsValid(LinkModel))
				{
					LinkModel->AddLinkModel(NewModel);
				}
			}
		}

		//update
		for (auto& UpdateData : ToUpdate)
		{
			if (UpdateData.Key != nullptr && UpdateData.Value != nullptr)
			{
				UDSToolLibrary::ParsePropertyCopy(UpdateData.Key, UpdateData.Value->GetProperty());
				UpdateData.Key->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateByProperty);
			}
		}
	}

	return Res;
}

void UDSRevokeLibrary::TransformUnion(const FString& InUUID, const EDSModelType& InType, const TSharedPtr<FDSBaseProperty>& InProperty)
{
	if (!InUUID.IsEmpty() && InType != EDSModelType::E_None && InProperty.IsValid())
	{
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InType, InUUID);
		UDSRevokeLibrary::TransformUnion(EditModel, InProperty);
	}
}

void UDSRevokeLibrary::TransformUnion(UDSBaseModel* EditModel, const TSharedPtr<FDSBaseProperty>& InProperty)
{
	if (IsValid(EditModel) && InProperty.IsValid())
	{
		UDSToolLibrary::ParsePropertyCopy(EditModel, InProperty);
		EditModel->OnExecuteAction(FDSModelExecuteType::ExecuteTransformByAxis, FDSBroadcastMarkData::OnlyOutlineBroadcastMark);
	}
}

void UDSRevokeLibrary::FlipHiddenUnion(const FString& InUUID, const EDSModelType& InType)
{
	if (!InUUID.IsEmpty() && InType != EDSModelType::E_None)
	{
		UDSBaseModel* EditModel = UDSMVCSubsystem::GetInstance()->GetModelsByTypeAndID(InType, InUUID);
		UDSRevokeLibrary::FlipHiddenUnion(EditModel);
	}
}

void UDSRevokeLibrary::FlipHiddenUnion(UDSBaseModel* EditModel)
{
	if (EditModel != nullptr)
	{
		bool IsEditHidden = EditModel->HasHidden();
		FDSModelExecuteType CurCommand = IsEditHidden ? FDSModelExecuteType::ExecuteUnHidden : FDSModelExecuteType::ExecuteHidden;
		EditModel->OnExecuteAction(CurCommand);
	}
}

void UDSRevokeLibrary::UpdatePushDataPropertyUnion(UDSBaseModel* EditModel, const FDSModelExecuteType& InExecuteType, const FDSRevokePushData& InPushData, FDSRevokePushData& ModifyPushData)
{
	if (IsValid(EditModel))
	{
		EDSModelType EditType = EditModel->GetModelType();
		if(EditType == EDSModelType::E_House_Pillar)
		{
			UDSRevokeLibrary::UpdatePushDataPropertyUnion_Single(EditModel, InExecuteType, ModifyPushData.SingleData);
		}
		else if(UDSToolLibrary::IsCustomCupboardType(EditType))
		{
			UDSRevokeLibrary::UpdatePushDataPropertyUnion_Custom(EditModel, InExecuteType, ModifyPushData.CustomData);
		}
        else if (EditType == EDSModelType::E_Generated_CounterTop || EditType == EDSModelType::E_Generated_SideCounterTop
			|| EditType == EDSModelType::E_Generated_CounterTop_Line || EditType == EDSModelType::E_Generated_CounterTop_Point)
		{
			UDSRevokeLibrary::UpdatePushDataProperty(EditModel, ModifyPushData.CounterTopData);
        	ModifyPushData.CounterTopData.SetCTRevokeType(InPushData.CounterTopData.GetRevokeType());
		}
		else if (EditType == EDSModelType::E_Custom_Sink)
		{
			UDSRevokeLibrary::UpdatePushDataPropertyUnion_Sink(EditModel, InExecuteType, ModifyPushData.SinkData);
		}
		else if (EditType == EDSModelType::E_Custom_Stove || EditType == EDSModelType::E_Custom_RangeHood)
		{//更新炊具和烟机的属性SRH
			UDSRevokeLibrary::UpdatePushDataPropertyUnion_SRH(EditModel, InExecuteType, ModifyPushData.StoveRangeHoodData);
		}
		else if (UDSToolLibrary::IsGeneratedLineType(EditType))
		{
			UpdatePushDataPropertyUnion_GeneratedLine(EditModel, InExecuteType, ModifyPushData.GeneratedLineData);
		}
		else if (EditType == EDSModelType::E_Generated_LineEntity)
		{
			UpdatePushDataPropertyUnion_LineEntity(EditModel, InExecuteType, ModifyPushData.LineEntityRevokeData);
		}
		else if (EditType == EDSModelType::E_Furniture_HouseFurniture)
		{
			UDSRevokeLibrary::UpdatePushDataPropertyUnion_SoftFurniture(EditModel, InExecuteType, ModifyPushData.SoftFurnitureRevokeData);
		}
		else if (EditType == EDSModelType::E_Furniture_MoldingCeiling)
		{
            UDSRevokeLibrary::UpdatePushDataPropertyUnion_ModelCeiling(EditModel, InExecuteType, ModifyPushData.ModelCeilingData);
		}
		else if (EditType == EDSModelType::E_MultiSelect || EditType == EDSModelType::E_Group)
		{
			UDSRevokeLibrary::UpdatePushDataPropertyUnion_MultiGroup(EditModel, InExecuteType, ModifyPushData.MultiGroupPushData);
		}
	}
}

void UDSRevokeLibrary::UpdatePushDataPropertyUnion_Single(UDSBaseModel* EditModel, const FDSModelExecuteType& InExecuteType, FDSBasicPushData& ModifyPushData)
{
	if(EditModel != nullptr)
	{
		ModifyPushData.EditModel = EditModel;
		ModifyPushData.EditProperty = EditModel->GetPropertySharedPtr();
		ModifyPushData.ExecuteType = InExecuteType;
	}
}

void UDSRevokeLibrary::UpdatePushDataPropertyUnion_Custom(UDSBaseModel* EditModel, const FDSModelExecuteType& InExecuteType, FDSCustomPushData& ModifyPushData)
{
	if(EditModel != nullptr)
	{
		ModifyPushData.EditModel = EditModel;
		ModifyPushData.ExecuteType = InExecuteType;
		ModifyPushData.ComponentRevokeDataPtr = nullptr;
	}
}

void UDSRevokeLibrary::UpdatePushDataPropertyUnion_Sink(UDSBaseModel* EditModel, const FDSModelExecuteType& InExecuteType, FDSSinkPushData& ModifyPushData)
{
	if (IsValid(EditModel))
	{
		ModifyPushData.RevokeType = UDSRevokeLibrary::GetOppositeSinkCommandTypeFromExecute(InExecuteType);
		ModifyPushData.LinkUUID = UDSModelDependencySubsystem::GetInstance()->FindCupboardBySink(EditModel->GetUUID());
		ModifyPushData.SetProperty(StaticCastSharedPtr<FDSSinkProperty>(EditModel->GetPropertySharedPtr()), false);
	}
}

void UDSRevokeLibrary::UpdatePushDataPropertyUnion_SRH(UDSBaseModel* EditModel, const FDSModelExecuteType& InExecuteType, FDSSRHPushData& ModifyPushData)
{
	if (IsValid(EditModel))
	{//填充数据
		ModifyPushData.RevokeType = UDSRevokeLibrary::GetOppositeSRHCommandTypeFromExecute(InExecuteType);
		EDSModelType EditType = EditModel->GetModelType();
		if (EditType == EDSModelType::E_Custom_Stove)
		{
			ModifyPushData.LinkUUID = UDSModelDependencySubsystem::GetInstance()->FindCupboardByStove(EditModel->GetUUID());
		}
		if (EditType == EDSModelType::E_Custom_Stove)
		{
			ModifyPushData.SetProperty(StaticCastSharedPtr<FDSStoveProperty>(EditModel->GetPropertySharedPtr()), false);
		}
		else if (EditType == EDSModelType::E_Custom_RangeHood)
		{
			ModifyPushData.SetProperty(StaticCastSharedPtr<FDSRangeHoodProperty>(EditModel->GetPropertySharedPtr()), false);
		}
	}

}

void UDSRevokeLibrary::UpdatePushDataPropertyUnion_GeneratedLine(UDSBaseModel* EditModel, const FDSModelExecuteType& InExecuteType, FDSGeneratedLinePushData& ModifyPushData)
{
	if (!IsValid(EditModel))
	{
		return;
	}

	ModifyPushData.RevokeType = GetOppositeGeneratedLineCommandTypeFromExecute(InExecuteType);


	ModifyPushData.Relations = UDSModelDependencySubsystem::GetInstance()->FindGeneratedLineRelations(EditModel->GetUUID());
	ModifyPushData.SetProperty(EditModel->GetTypedProperty<FDSGeneratedLineBaseProperty>(), true);
}

void UDSRevokeLibrary::UpdatePushDataPropertyUnion_LineEntity(UDSBaseModel* EditModel,
	const FDSModelExecuteType& InExecuteType, FDSLineEntityPushData& ModifyPushData)
{
	if (!IsValid(EditModel))
	{
		return;
	}

	TArray<TWeakObjectPtr<UDSBaseModel>> LinkModels = EditModel->GetLinkModels().Array();
	if (LinkModels.IsEmpty())
	{
		return;
	}

	ModifyPushData.LinkModelType = LinkModels[0]->GetModelType();
	ModifyPushData.LinkUUID = LinkModels[0]->GetUUID();
	ModifyPushData.RevokeType = GetOppositeLineEntityCommandTypeFromExecute(InExecuteType);
	ModifyPushData.SetProperty(EditModel->GetTypedProperty<FDSGeneratedLineEntityProperty>(), true);
}

void UDSRevokeLibrary::UpdatePushDataPropertyUnion_SoftFurniture(UDSBaseModel* EditModel, const FDSModelExecuteType& InExecuteType, FDSSoftFurniturePushData& ModifyPushData)
{
	if (IsValid(EditModel))
	{
		ModifyPushData.RevokeType = UDSRevokeLibrary::GetOppositeSoftFurnitureCommandTypeFromExecute(InExecuteType);
		ModifyPushData.SetProperty(StaticCastSharedPtr<FDSSoftFurnitureProperty>(EditModel->GetPropertySharedPtr()), false);
	}
}

void UDSRevokeLibrary::UpdatePushDataPropertyUnion_ModelCeiling(UDSBaseModel* EditModel, const FDSModelExecuteType& InExecuteType, FDSModelCeilingPushData& ModifyPushData)
{
	if (IsValid(EditModel))
	{
		ModifyPushData.RevokeType = UDSRevokeLibrary::GetOppositeModelCeilingCommandTypeFromExecute(InExecuteType);
		ModifyPushData.SetProperty(StaticCastSharedPtr<FDSMoldingCeilingProperty>(EditModel->GetPropertySharedPtr()), false);
	}
}

void UDSRevokeLibrary::UpdatePushDataPropertyUnion_MultiGroup(UDSBaseModel* EditModel, const FDSModelExecuteType& InExecuteType, FDSMultiGroupPushData& ModifyPushData)
{
	if (IsValid(EditModel))
	{
		ModifyPushData.RevokeType = UDSRevokeLibrary::GetOppositeMultiGroupCommandTypeFromExecute(InExecuteType);
		ModifyPushData.SetProperty(EditModel->GetPropertySharedPtr(), false);
	}
}

void UDSRevokeLibrary::ConstructRevokeCTRelation(const TArray<FString>& InRelationModels,
	FDSCTRevokeDependencyInfo& OutCTDependencyInfo)
{
	for (const auto& IRM : InRelationModels)
	{
		if (!IRM.IsEmpty())
		{
			FDSRevokeModelInner TempInfo;
			TempInfo.UUID = IRM;
			TempInfo.ModelType = UDSMVCSubsystem::GetInstance()->GetCustomModelTypeByUUID(IRM);
			OutCTDependencyInfo.AddInfo(TempInfo);
		}
	}
}

void UDSRevokeLibrary::ConstructCTRelation(const FDSCTRevokeDependencyInfo& InRevokeRelation, TArray<FString>& OutCTDependencyInfo)
{
	for (const auto& IRR : InRevokeRelation.ModelInfos)
	{
		if (!IRR.GetUUID().IsEmpty())
		{
			OutCTDependencyInfo.Add(IRR.GetUUID());
		}
	}
}

void UDSRevokeLibrary::ConstructRevokeSideCTRelation(const FSideCounterTopDependencyInfo* InRelationPtr,
	FDSSideCTRevokeDependencyInfo& OutSideCTDependencyInfo)
{
	if (InRelationPtr != nullptr)
	{
		//upper model
		if (!InRelationPtr->UpperModel.IsEmpty())
		{
			OutSideCTDependencyInfo.UpperModel.SetUUID(InRelationPtr->UpperModel);
			OutSideCTDependencyInfo.UpperModel.SetModelType(UDSMVCSubsystem::GetInstance()->GetCustomModelTypeByUUID(InRelationPtr->UpperModel));
		}
		OutSideCTDependencyInfo.UpperPointIndex = InRelationPtr->UpperPointIndex;

		//lower model
		if (!InRelationPtr->LowerModel.IsEmpty())
		{
			OutSideCTDependencyInfo.LowerModel.SetUUID(InRelationPtr->LowerModel);
			OutSideCTDependencyInfo.LowerModel.SetModelType(UDSMVCSubsystem::GetInstance()->GetCustomModelTypeByUUID(InRelationPtr->LowerModel));
		}
		OutSideCTDependencyInfo.LowerPointIndex = InRelationPtr->LowerPointIndex;

		//overlap
		OutSideCTDependencyInfo.OverlapPointIndex = InRelationPtr->OverlapPointIndex;
	}
}

void UDSRevokeLibrary::ConstructSideCTRelation(const FDSSideCTRevokeDependencyInfo& InRevokeRelation,
	FSideCounterTopDependencyInfo* OutRelationPtr)
{
	if (OutRelationPtr != nullptr)
	{
		//upper model
		if (!InRevokeRelation.UpperModel.GetUUID().IsEmpty())
		{
			OutRelationPtr->UpperModel = InRevokeRelation.UpperModel.GetUUID();
		}
		OutRelationPtr->UpperPointIndex = InRevokeRelation.UpperPointIndex;

		//lower model
		if (!InRevokeRelation.LowerModel.GetUUID().IsEmpty())
		{
			OutRelationPtr->LowerModel = InRevokeRelation.LowerModel.GetUUID();
		}
		OutRelationPtr->LowerPointIndex = InRevokeRelation.LowerPointIndex;

		OutRelationPtr->OverlapPointIndex = InRevokeRelation.OverlapPointIndex;
	}
}

void UDSRevokeLibrary::ConstructBroadcastPropertyFromRevokeData(UDSCounterTopProperty* BroadcastProperty, UDSCounterTopRevokeData* RevokePtr)
{
	if (BroadcastProperty == nullptr || RevokePtr == nullptr)
		return;

	BroadcastProperty->SetEditUUID(RevokePtr->ModelUUID);
	BroadcastProperty->SetPropertyData(RevokePtr->GetPropertyData());
	BroadcastProperty->SetNewWidgetProperty(RevokePtr->GetNewWidgetProperty());
	BroadcastProperty->SetPropertyData(RevokePtr->GetSidePropertyData());
	BroadcastProperty->SetPropertyData(RevokePtr->GetLinePropertyData());
	BroadcastProperty->SetPropertyData(RevokePtr->GetPointPropertyData());
}

// EActionCommandType UDSRevokeLibrary::GetOppositeCommandType(const EActionCommandType& InCommandType)
// {
// 	EActionCommandType Res = EActionCommandType::E_None;
// 	switch (InCommandType)
// 	{
// 	case EActionCommandType::E_All:
// 		Res = EActionCommandType::E_All;
// 		break;
// 	case EActionCommandType::E_Spawn:
// 		Res = EActionCommandType::E_Delete;
// 		break;
// 	case EActionCommandType::E_Transform:
// 		Res = EActionCommandType::E_Transform;
// 		break;
// 	case EActionCommandType::E_Delete:
// 	case EActionCommandType::E_DeleteSelf:
// 		Res = EActionCommandType::E_Spawn;
// 		break;
// 	case EActionCommandType::E_Lock:
// 		Res = EActionCommandType::E_Unlock;
// 		break;
// 	case EActionCommandType::E_Unlock:
// 		Res = EActionCommandType::E_Lock;
// 		break;
// 	case EActionCommandType::E_Hidden:
// 		Res = EActionCommandType::E_UnHidden;
// 		break;
// 	case EActionCommandType::E_UnHidden:
// 		Res = EActionCommandType::E_Hidden;
// 		break;
// 	case EActionCommandType::E_UpdateSelf:
// 		Res = EActionCommandType::E_UpdateSelf;
// 		break;
// 	case EActionCommandType::E_MultiAdd:
// 		Res = EActionCommandType::E_MultiRelease;
// 		break;
// 	case EActionCommandType::E_MultiRelease:
// 		Res = EActionCommandType::E_MultiAdd;
// 		break;
// 	case EActionCommandType::E_GroupCombine:
// 		Res = EActionCommandType::E_GroupRelease;
// 		break;
// 	case EActionCommandType::E_GroupRelease:
// 		Res = EActionCommandType::E_GroupCombine;
// 		break;
// 	default:
// 		break;
// 	}
// 	return Res;
// }

FDSModelExecuteType UDSRevokeLibrary::GetOppositeExecuteType(const FDSModelExecuteType& InExecuteType)
{
	FDSModelExecuteType Res = FDSModelExecuteType::ExecuteNone;

	if(InExecuteType == FDSModelExecuteType::ExecuteAll)
	{
		Res = FDSModelExecuteType::ExecuteAll;
	}
	else if(InExecuteType == FDSModelExecuteType::ExecuteSpawn || InExecuteType == FDSModelExecuteType::ExecuteGenerateFormProperty)
	{
		Res = FDSModelExecuteType::ExecuteDelete;
	}
	else if(InExecuteType == FDSModelExecuteType::ExecuteTransform)
	{
		Res = FDSModelExecuteType::ExecuteTransform;
	}
	else if(InExecuteType == FDSModelExecuteType::ExecuteTransformByAxis)
	{
		Res = FDSModelExecuteType::ExecuteTransformByAxis;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteTransformNormal)
	{
		Res = FDSModelExecuteType::ExecuteTransformNormal;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteDelete || InExecuteType == FDSModelExecuteType::ExecuteDeleteSelf || InExecuteType == FDSModelExecuteType::ExecuteDeleteSelfOnly)
	{
		Res = FDSModelExecuteType::ExecuteSpawn;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteLock)
	{
		Res = FDSModelExecuteType::ExecuteUnLock;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteUnLock)
	{
		Res = FDSModelExecuteType::ExecuteLock;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteHidden)
	{
		Res = FDSModelExecuteType::ExecuteUnHidden;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteUnHidden)
	{
		Res = FDSModelExecuteType::ExecuteHidden;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteUpdateSelf)
	{
		Res = FDSModelExecuteType::ExecuteUpdateSelf;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteUpdateByProperty)
	{
		Res = FDSModelExecuteType::ExecuteUpdateByProperty;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteMultiAdd)
	{
		Res = FDSModelExecuteType::ExecuteMultiRelease;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteMultiRelease)
	{
		Res = FDSModelExecuteType::ExecuteMultiAdd;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteGroupCombine)
	{
		Res = FDSModelExecuteType::ExecuteGroupRelease;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteGroupRelease)
	{
		Res = FDSModelExecuteType::ExecuteGroupCombine;
	}

	return Res;
}

//EActionCommandType UDSRevokeLibrary::GetOppositeCommandTypeFromCT(const EDSCTRevokeType& InCTType)
//{
//	EActionCommandType Res = EActionCommandType::E_None;
//	switch (InCTType)
//	{
//	case EDSCTRevokeType::E_None:
//	{
//		Res = EActionCommandType::E_None;
//		break;
//	}
//	case EDSCTRevokeType::E_PropertyChange:
//	case EDSCTRevokeType::E_Update:
//	case EDSCTRevokeType::E_LineShift:
//	{
//		Res = EActionCommandType::E_UpdateSelf;
//		break;
//	}
//	case EDSCTRevokeType::E_Create:
//	{
//		Res = EActionCommandType::E_Spawn;
//		break;
//	}
//	case EDSCTRevokeType::E_Delete:
//	{
//		Res = EActionCommandType::E_Delete;
//		break;
//	}
//	default:
//		break;
//	}
//	return Res;
//}

FDSModelExecuteType UDSRevokeLibrary::GetOppositeExecuteTypeFromCT(const EDSCTRevokeType& InCTType)
{
	FDSModelExecuteType Res = FDSModelExecuteType::ExecuteNone;
	switch (InCTType)
	{
	case EDSCTRevokeType::E_None:
	{
		Res = FDSModelExecuteType::ExecuteNone;
		break;
	}
	case EDSCTRevokeType::E_PropertyChange:
	case EDSCTRevokeType::E_Update:
	case EDSCTRevokeType::E_LineShift:
	{
		Res = FDSModelExecuteType::ExecuteUpdateSelf;
		break;
	}
	case EDSCTRevokeType::E_Create:
	{
		Res = FDSModelExecuteType::ExecuteSpawn;
		break;
	}
	case EDSCTRevokeType::E_Delete:
	{
		Res = FDSModelExecuteType::ExecuteDelete;
		break;
	}
	default:
		break;
	}
	return Res;
}

EDSRevokeComponentType UDSRevokeLibrary::GetOppositeComponentCommandType(const EDSRevokeComponentType& InCommandType)
{
	if (InCommandType == EDSRevokeComponentType::E_Component_Add)
	{
		return EDSRevokeComponentType::E_Component_Delete;
	}
	else if (InCommandType == EDSRevokeComponentType::E_Component_Update)
	{
		return EDSRevokeComponentType::E_Component_Update;
	}
	else if (InCommandType == EDSRevokeComponentType::E_Component_Delete)
	{
		return EDSRevokeComponentType::E_Component_Add;
	}
	else if (InCommandType == EDSRevokeComponentType::E_Component_CustomReplace)
	{
		return EDSRevokeComponentType::E_Component_CustomReplace;
	}
	else if (InCommandType == EDSRevokeComponentType::E_Component_MatReplace)
	{
		return EDSRevokeComponentType::E_Component_MatReplace;
	}
	else if (InCommandType == EDSRevokeComponentType::E_Component_ModelReplace)
	{
		return EDSRevokeComponentType::E_Component_ModelReplace;
	}
	return EDSRevokeComponentType::E_None;
}

EDSCTRevokeType UDSRevokeLibrary::GetOppositeCTCommandType(const EDSCTRevokeType& InCommandType)
{
	switch (InCommandType)
	{
	case EDSCTRevokeType::E_Create: return EDSCTRevokeType::E_Delete;
	case EDSCTRevokeType::E_Delete: return EDSCTRevokeType::E_Create;
	case EDSCTRevokeType::E_Update: return EDSCTRevokeType::E_Update;
	case EDSCTRevokeType::E_None: return EDSCTRevokeType::E_None;
	case EDSCTRevokeType::E_PropertyChange: return EDSCTRevokeType::E_PropertyChange;
	case EDSCTRevokeType::E_LineShift: return EDSCTRevokeType::E_LineShift;
	case EDSCTRevokeType::E_ReGenerateLinePoint: return EDSCTRevokeType::E_ReGenerateLinePoint;
	case EDSCTRevokeType::E_Trans: return EDSCTRevokeType::E_Trans;
	case EDSCTRevokeType::E_Hidden: return EDSCTRevokeType::E_Hidden;
	default: return EDSCTRevokeType::E_None;
	}

}

EDSCTRevokeType UDSRevokeLibrary::GetOppositeCTCommandTypeFromCommand(const FDSModelExecuteType& InExecuteType)
{
	if (InExecuteType.IsDeleteExecute())
	{
		return EDSCTRevokeType::E_Delete;
	}
	else if (InExecuteType.IsSpawnExecute())
	{
		return EDSCTRevokeType::E_Create;
	}
	return EDSCTRevokeType::E_None;
}

EDSSinkRevokeType UDSRevokeLibrary::GetOppositeCommandType(const EDSSinkRevokeType& InCommandType)
{
	switch (InCommandType)
	{
	case EDSSinkRevokeType::E_None: return EDSSinkRevokeType::E_None;
	case EDSSinkRevokeType::E_Refresh: return EDSSinkRevokeType::E_Refresh;
	case EDSSinkRevokeType::E_Transform: return EDSSinkRevokeType::E_Transform;
	case EDSSinkRevokeType::E_Delete: return EDSSinkRevokeType::E_Spawn;
	case EDSSinkRevokeType::E_Spawn: return EDSSinkRevokeType::E_Delete;
	case EDSSinkRevokeType::E_FlipHidden: return EDSSinkRevokeType::E_FlipHidden;
	default: return EDSSinkRevokeType::E_None;
	}
}

EDSSRHRevokeType UDSRevokeLibrary::GetOppositeCommandType(const EDSSRHRevokeType& InCommandType)
{
	switch (InCommandType)
	{
	case EDSSRHRevokeType::E_None: return EDSSRHRevokeType::E_None; break;
	case EDSSRHRevokeType::E_Refresh: return EDSSRHRevokeType::E_Refresh; break;
	case EDSSRHRevokeType::E_Transform: return EDSSRHRevokeType::E_Transform; break;
	case EDSSRHRevokeType::E_Delete: return EDSSRHRevokeType::E_Spawn; break;
	case EDSSRHRevokeType::E_Spawn: return EDSSRHRevokeType::E_Delete; break;
	case EDSSRHRevokeType::E_FlipHidden: return EDSSRHRevokeType::E_FlipHidden; break;
	default: return EDSSRHRevokeType::E_None;
	}
}

EDSSinkRevokeType UDSRevokeLibrary::GetOppositeSinkCommandTypeFromExecute(const FDSModelExecuteType& InExecuteType)
{
	EDSSinkRevokeType Res = EDSSinkRevokeType::E_None;
	if (InExecuteType == FDSModelExecuteType::ExecuteSpawn || InExecuteType == FDSModelExecuteType::ExecuteGenerateFormProperty)
	{
		Res = EDSSinkRevokeType::E_Spawn;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteDelete || InExecuteType == FDSModelExecuteType::ExecuteDeleteSelf || InExecuteType == FDSModelExecuteType::ExecuteDeleteSelfOnly)
	{
		Res = EDSSinkRevokeType::E_Delete;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteTransform || InExecuteType == FDSModelExecuteType::ExecuteTransformByAxis || InExecuteType == FDSModelExecuteType::ExecuteTransformNormal)
	{
		Res = EDSSinkRevokeType::E_Transform;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteUpdateSelf || InExecuteType == FDSModelExecuteType::ExecuteAll || InExecuteType == FDSModelExecuteType::ExecuteUpdateByProperty)
	{
		Res = EDSSinkRevokeType::E_Refresh;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteHidden || InExecuteType == FDSModelExecuteType::ExecuteUnHidden)
	{
		Res = EDSSinkRevokeType::E_FlipHidden;
	}
	return Res;
}

EDSSRHRevokeType UDSRevokeLibrary::GetOppositeSRHCommandTypeFromExecute(const FDSModelExecuteType& InExecuteType)
{
	EDSSRHRevokeType Res = EDSSRHRevokeType::E_None;
	if (InExecuteType == FDSModelExecuteType::ExecuteSpawn || InExecuteType == FDSModelExecuteType::ExecuteGenerateFormProperty)
	{
		Res = EDSSRHRevokeType::E_Spawn;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteDelete || InExecuteType == FDSModelExecuteType::ExecuteDeleteSelf || InExecuteType == FDSModelExecuteType::ExecuteDeleteSelfOnly)
	{
		Res = EDSSRHRevokeType::E_Delete;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteTransform || InExecuteType == FDSModelExecuteType::ExecuteTransformByAxis || InExecuteType == FDSModelExecuteType::ExecuteTransformNormal)
	{
		Res = EDSSRHRevokeType::E_Transform;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteUpdateSelf || InExecuteType == FDSModelExecuteType::ExecuteAll || InExecuteType == FDSModelExecuteType::ExecuteUpdateByProperty)
	{
		Res = EDSSRHRevokeType::E_Refresh;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteHidden || InExecuteType == FDSModelExecuteType::ExecuteUnHidden)
	{
		Res = EDSSRHRevokeType::E_FlipHidden;
	}
	return Res;
}

EDSMultiGroupRevokeType UDSRevokeLibrary::GetOppositeCommandType(const EDSMultiGroupRevokeType& InCommandType)
{
	switch (InCommandType)
	{
	case EDSMultiGroupRevokeType::E_None: return EDSMultiGroupRevokeType::E_None;
	case EDSMultiGroupRevokeType::E_Delete: return EDSMultiGroupRevokeType::E_Spawn;
	case EDSMultiGroupRevokeType::E_Transform: return EDSMultiGroupRevokeType::E_Transform;
	case EDSMultiGroupRevokeType::E_FlipHidden: return EDSMultiGroupRevokeType::E_FlipHidden;
	case EDSMultiGroupRevokeType::E_CombineGroup: return EDSMultiGroupRevokeType::E_ReleaseGroup;
    case EDSMultiGroupRevokeType::E_ReleaseGroup: return EDSMultiGroupRevokeType::E_CombineGroup;
	default: return EDSMultiGroupRevokeType::E_None;
	}
}

EDSMultiGroupRevokeType UDSRevokeLibrary::GetOppositeMultiGroupCommandTypeFromExecute(const FDSModelExecuteType& InExecuteType)
{
	EDSMultiGroupRevokeType Res = EDSMultiGroupRevokeType::E_None;
	if (InExecuteType == FDSModelExecuteType::ExecuteSpawn || InExecuteType == FDSModelExecuteType::ExecuteGenerateFormProperty)
	{
		Res = EDSMultiGroupRevokeType::E_Spawn;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteDelete || InExecuteType == FDSModelExecuteType::ExecuteDeleteSelf || InExecuteType == FDSModelExecuteType::ExecuteDeleteSelfOnly)
	{
		Res = EDSMultiGroupRevokeType::E_Delete;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteTransform || InExecuteType == FDSModelExecuteType::ExecuteTransformByAxis || InExecuteType == FDSModelExecuteType::ExecuteTransformNormal)
	{
		Res = EDSMultiGroupRevokeType::E_Transform;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteHidden || InExecuteType == FDSModelExecuteType::ExecuteUnHidden)
	{
		Res = EDSMultiGroupRevokeType::E_FlipHidden;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteGroupCombine)
	{
		Res = EDSMultiGroupRevokeType::E_CombineGroup;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteGroupRelease)
	{
		Res = EDSMultiGroupRevokeType::E_ReleaseGroup;
	}
	return Res;
}

EDSGeneratedLineRevokeType UDSRevokeLibrary::GetOppositeCommandType(const EDSGeneratedLineRevokeType& InCommandType)
{
	switch (InCommandType)
	{
	case EDSGeneratedLineRevokeType::E_None: return EDSGeneratedLineRevokeType::E_None;
	case EDSGeneratedLineRevokeType::E_Spawn: return EDSGeneratedLineRevokeType::E_Delete;
	case EDSGeneratedLineRevokeType::E_Delete: return EDSGeneratedLineRevokeType::E_Spawn;
	case EDSGeneratedLineRevokeType::E_Update: return EDSGeneratedLineRevokeType::E_Update;
	default: return EDSGeneratedLineRevokeType::E_None;
	}
}

EDSGeneratedLineRevokeType UDSRevokeLibrary::GetOppositeGeneratedLineCommandTypeFromExecute(
	const FDSModelExecuteType& InExecuteType)
{
	EDSGeneratedLineRevokeType Result = EDSGeneratedLineRevokeType::E_None;

	if (InExecuteType == FDSModelExecuteType::ExecuteSpawn || InExecuteType == FDSModelExecuteType::ExecuteGenerateFormProperty)
	{
		Result = EDSGeneratedLineRevokeType::E_Spawn;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteDelete || InExecuteType == FDSModelExecuteType::ExecuteDeleteSelf || InExecuteType == FDSModelExecuteType::ExecuteDeleteSelfOnly)
	{
		Result = EDSGeneratedLineRevokeType::E_Delete;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteUpdateSelf || InExecuteType == FDSModelExecuteType::ExecuteAll || InExecuteType == FDSModelExecuteType::ExecuteUpdateByProperty)
	{
		Result = EDSGeneratedLineRevokeType::E_Update;
	}

	return Result;
}

EDSLineEntityRevokeType UDSRevokeLibrary::GetOppositeCommandType(const EDSLineEntityRevokeType& InCommandType)
{
	switch (InCommandType)
	{
	case EDSLineEntityRevokeType::E_Hidden: return EDSLineEntityRevokeType::E_Hidden;
	default: return EDSLineEntityRevokeType::E_None;
	}
}

EDSLineEntityRevokeType UDSRevokeLibrary::GetOppositeLineEntityCommandTypeFromExecute(
	const FDSModelExecuteType& InExecuteType)
{
	EDSLineEntityRevokeType Result = EDSLineEntityRevokeType::E_None;

	if (InExecuteType == FDSModelExecuteType::ExecuteHidden || InExecuteType == FDSModelExecuteType::ExecuteUnHidden)
	{
		Result = EDSLineEntityRevokeType::E_Hidden;
	}

	return Result;
}

EDSSoftFurnitureRevokeType UDSRevokeLibrary::GetOppositeCommandType(const EDSSoftFurnitureRevokeType& InCommandType)
{

	switch (InCommandType)
	{
	case EDSSoftFurnitureRevokeType::E_None: return EDSSoftFurnitureRevokeType::E_None; break;
	case EDSSoftFurnitureRevokeType::E_Refresh: return EDSSoftFurnitureRevokeType::E_Refresh; break;
	case EDSSoftFurnitureRevokeType::E_Transform: return EDSSoftFurnitureRevokeType::E_Transform; break;
	case EDSSoftFurnitureRevokeType::E_Delete: return EDSSoftFurnitureRevokeType::E_Spawn; break;
	case EDSSoftFurnitureRevokeType::E_Spawn: return EDSSoftFurnitureRevokeType::E_Delete; break;
	case EDSSoftFurnitureRevokeType::E_FlipHidden: return EDSSoftFurnitureRevokeType::E_FlipHidden; break;
	default: return EDSSoftFurnitureRevokeType::E_None;
	}
}

EDSSoftFurnitureRevokeType UDSRevokeLibrary::GetOppositeSoftFurnitureCommandTypeFromExecute(const FDSModelExecuteType& InExecuteType)
{

	EDSSoftFurnitureRevokeType Res = EDSSoftFurnitureRevokeType::E_None;

	if (InExecuteType == FDSModelExecuteType::ExecuteSpawn || InExecuteType == FDSModelExecuteType::ExecuteGenerateFormProperty)
	{
		Res = EDSSoftFurnitureRevokeType::E_Spawn;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteDelete || InExecuteType == FDSModelExecuteType::ExecuteDeleteSelf || InExecuteType == FDSModelExecuteType::ExecuteDeleteSelfOnly)
	{
		Res = EDSSoftFurnitureRevokeType::E_Delete;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteTransform || InExecuteType == FDSModelExecuteType::ExecuteTransformByAxis || InExecuteType == FDSModelExecuteType::ExecuteTransformNormal)
	{
		Res = EDSSoftFurnitureRevokeType::E_Transform;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteUpdateSelf || InExecuteType == FDSModelExecuteType::ExecuteAll || InExecuteType == FDSModelExecuteType::ExecuteUpdateByProperty)
	{
		Res = EDSSoftFurnitureRevokeType::E_Refresh;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteHidden || InExecuteType == FDSModelExecuteType::ExecuteUnHidden)
	{
		Res = EDSSoftFurnitureRevokeType::E_FlipHidden;
	}

	return Res;
}

EDSModelCeilingRevokeType UDSRevokeLibrary::GetOppositeCommandType(const EDSModelCeilingRevokeType& InCommandType)
{
	switch (InCommandType)
	{
	case EDSModelCeilingRevokeType::E_None: return EDSModelCeilingRevokeType::E_None; break;
	case EDSModelCeilingRevokeType::E_Refresh: return EDSModelCeilingRevokeType::E_Refresh; break;
	case EDSModelCeilingRevokeType::E_Transform: return EDSModelCeilingRevokeType::E_Transform; break;
	case EDSModelCeilingRevokeType::E_Delete: return EDSModelCeilingRevokeType::E_Spawn; break;
	case EDSModelCeilingRevokeType::E_Spawn: return EDSModelCeilingRevokeType::E_Delete; break;
	case EDSModelCeilingRevokeType::E_FlipHidden: return EDSModelCeilingRevokeType::E_FlipHidden; break;
	default: return EDSModelCeilingRevokeType::E_None;
	}
}

EDSModelCeilingRevokeType UDSRevokeLibrary::GetOppositeModelCeilingCommandTypeFromExecute(const FDSModelExecuteType& InExecuteType)
{
	EDSModelCeilingRevokeType Res = EDSModelCeilingRevokeType::E_None;

	if (InExecuteType == FDSModelExecuteType::ExecuteSpawn || InExecuteType == FDSModelExecuteType::ExecuteGenerateFormProperty)
	{
		Res = EDSModelCeilingRevokeType::E_Spawn;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteDelete || InExecuteType == FDSModelExecuteType::ExecuteDeleteSelf || InExecuteType == FDSModelExecuteType::ExecuteDeleteSelfOnly)
	{
		Res = EDSModelCeilingRevokeType::E_Delete;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteTransform || InExecuteType == FDSModelExecuteType::ExecuteTransformByAxis || InExecuteType == FDSModelExecuteType::ExecuteTransformNormal)
	{
		Res = EDSModelCeilingRevokeType::E_Transform;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteUpdateSelf || InExecuteType == FDSModelExecuteType::ExecuteAll || InExecuteType == FDSModelExecuteType::ExecuteUpdateByProperty)
	{
		Res = EDSModelCeilingRevokeType::E_Refresh;
	}
	else if (InExecuteType == FDSModelExecuteType::ExecuteHidden || InExecuteType == FDSModelExecuteType::ExecuteUnHidden)
	{
		Res = EDSModelCeilingRevokeType::E_FlipHidden;
	}

	return Res;
}

EDSRevokeComponentType UDSRevokeLibrary::GetOppositeComponentCommandTypeFromAction(const FDSModelExecuteType& InExecuteType)
{
	if (InExecuteType.IsDeleteExecute())
	{
		return EDSRevokeComponentType::E_Component_Add;
	}
	return EDSRevokeComponentType::E_None;
}

// EDSRevokeComponentType UDSRevokeLibrary::ConvertComponentCommandTypeFromAction(const EActionCommandType& InCommandType)
// {
// 	if (InCommandType == EActionCommandType::E_Delete || InCommandType == EActionCommandType::E_DeleteSelf)
// 	{
// 		return EDSRevokeComponentType::E_Component_Delete;
// 	}
// 	return EDSRevokeComponentType::E_None;
// }

EDSRevokeComponentType UDSRevokeLibrary::ConvertComponentExecuteTypeFromAction(const FDSModelExecuteType& InExecuteType)
{
	if (InExecuteType.IsDeleteExecute())
	{
		return EDSRevokeComponentType::E_Component_Delete;
	}
	return EDSRevokeComponentType::E_None;
}

bool UDSRevokeLibrary::IsRevokeDataConstructTypeChanged(UDSBaseModel* InEditModel, const FDSRevokeDataInner& InRevokeData)
{
	if (DS_MODEL_VALID_FOR_USE(InEditModel))
	{
		if (InEditModel->GetModelType() == EDSModelType::E_House_Window
			|| InEditModel->GetModelType() == EDSModelType::E_House_Door)
		{
			if (StaticCastSharedPtr<FDSDoorAndWindowProperty>(InEditModel->GetPropertySharedPtr())
				&& StaticCastSharedPtr<FDSDoorAndWindowProperty>(InRevokeData.GetProperty()))
			{
				EDoorAndWindowType CurType = StaticCastSharedPtr<FDSDoorAndWindowProperty>(InEditModel->GetPropertySharedPtr())->GetWindowDoorType();
				EDoorAndWindowType RevokeType = StaticCastSharedPtr<FDSDoorAndWindowProperty>(InRevokeData.GetProperty())->GetWindowDoorType();
				return CurType != RevokeType;
			}
		}
	}

	return false;
}

bool UDSRevokeLibrary::IsAddComponentModel(UDSBaseModel* InEditModel)
{
	if (InEditModel != nullptr)
	{
		EDSModelType ThisModelType = InEditModel->GetModelType();
		bool IsCustomDoor = ThisModelType == EDSModelType::E_Custom_SwingDoor || ThisModelType == EDSModelType::E_Custom_SlidingDoor
			|| ThisModelType == EDSModelType::E_Custom_FlipUpDoor || ThisModelType == EDSModelType::E_Custom_FlipDownDoor;
		//TODO : other type
		return IsCustomDoor;
	}
	return false;
}

bool UDSRevokeLibrary::ConstructPushDataTypeByModel(UDSBaseModel* InEditModel, FDSRevokePushData& ModifyPusData)
{
	if (IsValid(InEditModel))
	{
		EDSModelType Type = InEditModel->GetModelType();
		if (Type == EDSModelType::E_Custom_Stove || Type == EDSModelType::E_Custom_RangeHood)
		{
			ModifyPusData.DataType = EDSPushDataType::E_StoveRangeHood;
		}
		else if (Type == EDSModelType::E_Custom_Sink)
		{
			ModifyPusData.DataType = EDSPushDataType::E_Sink;
		}
		else if (UDSToolLibrary::IsCustomCabinetType(Type))
		{
			ModifyPusData.DataType = EDSPushDataType::E_Custom;
		}
		else if (Type == EDSModelType::E_MultiSelect || Type == EDSModelType::E_Group)
		{
			ModifyPusData.DataType = EDSPushDataType::E_Multi_Group;
		}
		else if (Type == EDSModelType::E_Furniture_HouseFurniture)
		{
			ModifyPusData.DataType = EDSPushDataType::E_SoftFurniture;
		}
		else if (Type == EDSModelType::E_Generated_LineEntity)
		{
            ModifyPusData.DataType = EDSPushDataType::E_LineEntity;
		}
		else if (UDSToolLibrary::IsGeneratedLineType(Type))
		{
			ModifyPusData.DataType = EDSPushDataType::E_GeneratedLine;
		}
		else if (Type == EDSModelType::E_Furniture_MoldingCeiling)
		{
            ModifyPusData.DataType = EDSPushDataType::E_ModelCeiling;
		}
	

		return true;
	}

	return false;
}
