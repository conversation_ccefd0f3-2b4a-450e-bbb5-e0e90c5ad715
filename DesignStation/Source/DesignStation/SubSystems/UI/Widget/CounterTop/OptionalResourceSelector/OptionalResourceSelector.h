// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "IHttpRequest.h"
#include "UserWidget.h"
#include "Subsystems/UI/Widget/Common/ResourceItem/Structures/ResourceInfo.h"
#include "OptionalResourceSelector.generated.h"

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FOptionalResourceParameters
{
	GENERATED_USTRUCT_BODY()

	FOptionalResourceParameters()
		: Code("")
		  , ID("")
		  , Type(-1) {}

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Code;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString ID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Type;

	bool IsValid() { return !Code.IsEmpty() && !ID.IsEmpty() && Type != -1; };
};

class UMenuAnchor;

class UImage;

class UButton;

class UTextBlock;

class UCheckBox;

class UResourceSelectorDialog;

class UOptionalResourceSelectorThumbnail;

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UOptionalResourceSelector : public UUserWidget
{
	GENERATED_BODY()

public:
	DECLARE_DELEGATE_OneParam(FOnResourceSelectionChanged, const TSharedPtr<FDSResourceInfo>&);

	DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnResourceAvailabilityChanged, bool, bAvailable);

	UOptionalResourceSelector(const FObjectInitializer& Initializer);

	virtual void NativeOnInitialized() override;

	virtual void SetResourceInfo(const TSharedPtr<FDSResourceInfo>& InInfo);

	TSharedPtr<FDSResourceInfo> GetSelectedResourceInfo() const;

	UFUNCTION(BlueprintPure)
	bool HasValidResourceInfo() const;

	UFUNCTION(BlueprintCallable)
	void SetSelectorIsEnabled(bool IsEnabled, bool bIgnoreCallback = true);

	UFUNCTION(BlueprintPure)
	bool IsSelectorEnabled() const;

protected:
	UFUNCTION()
	FText OnGetResourceName();

	UFUNCTION()
	void OnVisualImageDownloaded(UTexture2DDynamic* Texture);

	UFUNCTION()
	void OnChangeButtonClicked();

	void SendQueryRequestByCodeAndID();

	void SendQueryRequestByRelativeCode();

	UFUNCTION()
	void OnEnableCheckBoxStateChanged(bool bIsChecked);

	void OnQueryCategoryTreeByRelativeCodeComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully);

	void OnQueryCategoryTreeComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully);

	void HandleReplaceResourceFromDialog(const TSharedPtr<FDSResourceInfo>& NewResource);

	UFUNCTION()
	UUserWidget* OnGetThumbnailMenuContent();

	UFUNCTION()
	void OnDetailsButtonHovered();

	UFUNCTION()
	void OnDetailsButtonUnhovered();

public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="OptionalResourceSelector | Appearance")
	bool bCanDisable;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="OptionalResourceSelector | Appearance")
	FText SelectorName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="OptionalResourceSelector | Appearance")
	FButtonStyle AddResourceStyle;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="OptionalResourceSelector | Appearance")
	FButtonStyle ReplaceResourceStyle;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="OptionalResourceSelector | Classes")
	TSubclassOf<UResourceSelectorDialog> SelectorDialogClass;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="OptionalResourceSelector | Content")
	TArray<FString> RelativeCodes;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "OptionalResourceSelector | Content")
	FOptionalResourceParameters OptionalResourceParameter;

	FOnResourceSelectionChanged OnResourceSelectionChanged;

	UPROPERTY(BlueprintAssignable, Category = "OptionalResourceSelector | Events")
	FOnResourceAvailabilityChanged OnAvailabilityChanged;

protected:
	TSharedPtr<FDSResourceInfo> ResourceInfo;

	FHttpRequestPtr HttpRequestHandle;

	UPROPERTY(BlueprintReadOnly, meta=(BindWidget))
	UMenuAnchor* ThumbnailMenuAnchor;

	UPROPERTY(BlueprintReadOnly, meta=(BindWidget))
	UImage* ResourceImage;

	UPROPERTY(BlueprintReadOnly, meta=(BindWidget))
	UButton* ChangeButton;

	UPROPERTY(BlueprintReadOnly, meta=(BindWidget))
	UCheckBox* EnableCheckBox;

	UPROPERTY(BlueprintReadOnly, meta=(BindWidget))
	UTextBlock* SelectorNameText;

	UPROPERTY(BlueprintReadOnly, meta=(BindWidget))
	UTextBlock* ResourceNameText;

	UPROPERTY(BlueprintReadOnly, meta=(BindWidget))
	UButton* DetailsButton;

	UPROPERTY(BlueprintReadOnly)
	UOptionalResourceSelectorThumbnail* ThumbnailWidget;
};
