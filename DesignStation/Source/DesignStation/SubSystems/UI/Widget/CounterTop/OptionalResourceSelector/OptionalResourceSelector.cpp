// Fill out your copyright notice in the Description page of Project Settings.

#include "OptionalResourceSelector.h"
#include "AsyncTaskDownloadImage.h"
#include "HttpModule.h"
#include "IHttpResponse.h"
#include "OptionalResourceSelectorThumbnail.h"
#include "PlatformHttp.h"
#include "Blueprint/WidgetTree.h"
#include "Components/Button.h"
#include "Components/CheckBox.h"
#include "Components/Image.h"
#include "Components/MenuAnchor.h"
#include "Components/TextBlock.h"
#include "Subsystems/DSNetworkSubsystem.h"
#include "SubSystems/NetworkConfig.h"
#include "Subsystems/Resource/DSResourceSubsystem.h"
#include "Subsystems/UI/DSUISubsystem.h"
#include "Subsystems/UI/Widget/WindowLayoutWidget.h"
#include "Subsystems/UI/Widget/Common/ResourceSelectorDialog/ResourceSelectorDialog.h"

DECLARE_LOG_CATEGORY_CLASS(LogOptionalResourceSelector, Log, All);

UOptionalResourceSelector::UOptionalResourceSelector(const FObjectInitializer& Initializer)
	: Super(Initializer)
	  , bCanDisable(true)
	  , ThumbnailMenuAnchor(nullptr)
	  , ResourceImage(nullptr)
	  , ChangeButton(nullptr)
	  , EnableCheckBox(nullptr)
	  , SelectorNameText(nullptr)
	  , ResourceNameText(nullptr)
	  , DetailsButton(nullptr)
	  , ThumbnailWidget(nullptr) {}

void UOptionalResourceSelector::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	ThumbnailMenuAnchor->OnGetUserMenuContentEvent.BindDynamic(this, &ThisClass::OnGetThumbnailMenuContent);

	ResourceNameText->TextDelegate.BindDynamic(this, &ThisClass::OnGetResourceName);
	ChangeButton->OnClicked.AddDynamic(this, &ThisClass::OnChangeButtonClicked);

	EnableCheckBox->OnCheckStateChanged.AddDynamic(this, &ThisClass::OnEnableCheckBoxStateChanged);

	DetailsButton->OnHovered.AddDynamic(this, &ThisClass::OnDetailsButtonHovered);
	DetailsButton->OnUnhovered.AddDynamic(this, &ThisClass::OnDetailsButtonUnhovered);
}

void UOptionalResourceSelector::SetResourceInfo(const TSharedPtr<FDSResourceInfo>& InInfo)
{
	ResourceInfo = InInfo;

	if (ResourceInfo.IsValid())
	{
		FString Url = ResourceInfo->ProductImg;
		static const FString ImageProcessParam = TEXT("?x-image-process=image/resize,m_fixed,h_300,w_300");
		const FString EncodedUrl = FPaths::Combine(FPaths::GetPath(Url), FPlatformHttp::UrlEncode(FPaths::GetCleanFilename(Url)));

		UAsyncTaskDownloadImage* DownloadTask = UAsyncTaskDownloadImage::DownloadImage(EncodedUrl + ImageProcessParam);
		DownloadTask->OnSuccess.AddDynamic(this, &ThisClass::OnVisualImageDownloaded);

		if (!ThumbnailWidget)
		{
			ThumbnailWidget = UOptionalResourceSelectorThumbnail::CreateInstance(this);
		}
		if (ThumbnailWidget)
		{
			if (!ResourceInfo->Width.IsEmpty() && !ResourceInfo->Height.IsEmpty() && !ResourceInfo->Depth.IsEmpty())
			{
				ThumbnailWidget->ShowSize(true);
				ThumbnailWidget->SetName(ResourceInfo->Name);
				ThumbnailWidget->SetWidth(FMath::RoundToInt32(FCString::Atod(*ResourceInfo->Width)));
				ThumbnailWidget->SetHeight(FMath::RoundToInt32(FCString::Atod(*ResourceInfo->Height)));
				ThumbnailWidget->SetDepth(FMath::RoundToInt32(FCString::Atod(*ResourceInfo->Depth)));
			}
			else
			{
				ThumbnailWidget->ShowSize(false);
			}
		}
	}
	else
	{
		ResourceImage->SetBrushTintColor(FColor::FromHex(TEXT("#F5F5F5FF")));
		ResourceImage->SetBrushFromTexture(nullptr);
	}

	ChangeButton->SetStyle(ResourceInfo.IsValid() ? ReplaceResourceStyle : AddResourceStyle);
}

TSharedPtr<FDSResourceInfo> UOptionalResourceSelector::GetSelectedResourceInfo() const
{
	return ResourceInfo;
}

bool UOptionalResourceSelector::HasValidResourceInfo() const
{
	return ResourceInfo.IsValid();
}

void UOptionalResourceSelector::SetSelectorIsEnabled(bool IsEnabled, bool bIgnoreCallback)
{
	EnableCheckBox->SetIsChecked(IsEnabled);

	if (!bIgnoreCallback)
	{
		OnAvailabilityChanged.Broadcast(IsEnabled);
	}

	ChangeButton->SetCursor(IsEnabled ? EMouseCursor::Hand : EMouseCursor::SlashedCircle);
}

bool UOptionalResourceSelector::IsSelectorEnabled() const
{
	return EnableCheckBox->IsChecked();
}

FText UOptionalResourceSelector::OnGetResourceName()
{
	return ResourceInfo.IsValid() ? FText::FromString(ResourceInfo->Name) : FText::GetEmpty();
}

void UOptionalResourceSelector::OnVisualImageDownloaded(UTexture2DDynamic* Texture)
{
	ResourceImage->SetBrushTintColor(FColor::White);
	ResourceImage->SetBrushFromTextureDynamic(Texture);
}

void UOptionalResourceSelector::OnChangeButtonClicked()
{
	if (!EnableCheckBox->IsChecked() || !SelectorDialogClass)
	{
		return;
	}

	if (HttpRequestHandle.IsValid() && HttpRequestHandle->GetStatus() == EHttpRequestStatus::Processing)
	{
		HttpRequestHandle->CancelRequest();
		HttpRequestHandle.Reset();
	}

	if (OptionalResourceParameter.IsValid())
	{
		SendQueryRequestByCodeAndID();
	}
	else
	{
		SendQueryRequestByRelativeCode();
	}
}

void UOptionalResourceSelector::SendQueryRequestByCodeAndID()
{
	HttpRequestHandle = FHttpModule::Get().CreateRequest();
	HttpRequestHandle->SetURL(FString::Printf(TEXT("%s/bk-design/api/store/getCateListByBkId"), SERVER_URL));
	TSharedPtr<FJsonObject> QueryParams = MakeShared<FJsonObject>();

	TArray<TSharedPtr<FJsonValue>> CodeJsonArray;
	TSharedPtr<FJsonValueString> CodeJson = MakeShared<FJsonValueString>(OptionalResourceParameter.Code);
	CodeJsonArray.Add(CodeJson);

	QueryParams->SetArrayField(TEXT("code"), CodeJsonArray);
	QueryParams->SetStringField(TEXT("bkId"), OptionalResourceParameter.ID);
	QueryParams->SetNumberField(TEXT("type"), OptionalResourceParameter.Type);

	FString QueryParamsStr;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&QueryParamsStr);
	if (!FJsonSerializer::Serialize(QueryParams.ToSharedRef(), Writer))
	{
		UE_LOG(DSNetworkSubsystemLog, Warning, TEXT("%s - Serialize json into string failed."), __FUNCTIONW__);
		return;
	}

	HttpRequestHandle->SetContentAsString(QueryParamsStr);
	HttpRequestHandle->SetVerb(TEXT("POST"));
	HttpRequestHandle->SetHeader(TEXT("Content-Type"), TEXT("application/json"));
	HttpRequestHandle->SetHeader(TEXT("Authorization"), UDSNetworkSubsystem::GetInstance()->GetToken());

	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnQueryCategoryTreeComplete);
	HttpRequestHandle->ProcessRequest();
}

void UOptionalResourceSelector::SendQueryRequestByRelativeCode()
{
	FString QueryParams = TEXT("[");
	for (int32 Index = 0; Index < RelativeCodes.Num(); Index++)
	{
		QueryParams.Append(FString::Printf(TEXT("\"%s\""), *RelativeCodes[Index]));
		if (RelativeCodes.IsValidIndex(Index + 1))
		{
			QueryParams.Append(TEXT(","));
		}
	}

	QueryParams.Append(TEXT("]"));
	HttpRequestHandle = FHttpModule::Get().CreateRequest();
	HttpRequestHandle->SetURL(FString::Printf(TEXT("%s/bk-design/api/store/getCateListByCode"), SERVER_URL));
	HttpRequestHandle->SetContentAsString(QueryParams);

	HttpRequestHandle->SetVerb(TEXT("POST"));
	HttpRequestHandle->SetHeader(TEXT("Content-Type"), TEXT("application/json"));
	HttpRequestHandle->SetHeader(TEXT("Authorization"), UDSNetworkSubsystem::GetInstance()->GetToken());

	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnQueryCategoryTreeByRelativeCodeComplete);
	HttpRequestHandle->ProcessRequest();
}

void UOptionalResourceSelector::OnEnableCheckBoxStateChanged(bool bIsChecked)
{
	ChangeButton->SetCursor(bIsChecked ? EMouseCursor::Hand : EMouseCursor::SlashedCircle);

	OnAvailabilityChanged.Broadcast(bIsChecked);
}

void UOptionalResourceSelector::OnQueryCategoryTreeByRelativeCodeComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully)
{
	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		UE_LOG(LogOptionalResourceSelector, Error, TEXT("Failed to query the category tree by relative codes."));
		return;
	}

	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response->GetContentAsString());

	TSharedPtr<FJsonObject> ResponseObj;
	if (!FJsonSerializer::Deserialize(Reader, ResponseObj))
	{
		UE_LOG(LogTemp, Error, TEXT("%s - Failed to parse the response content as json."), __FUNCTIONW__);
		return;
	}

	if (!ResponseObj->HasTypedField<EJson::Boolean>(TEXT("success")) || !ResponseObj->GetBoolField(TEXT("success")))
	{
		UE_LOG(LogTemp, Error, TEXT("%s - Query category tree by relative codes failure, msg: %s."), __FUNCTIONW__, *ResponseObj->GetStringField(TEXT("msg")));
		return;
	}

	TArray<TSharedPtr<FDSCategoryInfo>> CategoryTree;

	const TArray<TSharedPtr<FJsonValue>>& RespArray = ResponseObj->GetArrayField(TEXT("resp"));
	for (const TSharedPtr<FJsonValue>& RespValue : RespArray)
	{
		const TSharedPtr<FJsonObject>& CategoryObj = RespValue->AsObject();
		if (!CategoryObj)
		{
			continue;
		}

		TSharedPtr<FDSCategoryInfo> NewCategory = MakeShared<FDSCategoryInfo>();
		FDSCategoryInfo::DeserializeFromJson(NewCategory, CategoryObj);
		CategoryTree.Add(NewCategory);
	}

	UResourceSelectorDialog* SelectorDialog = CreateWidget<UResourceSelectorDialog>(this, SelectorDialogClass);
	SelectorDialog->InitDialog(CategoryTree, GetSelectedResourceInfo());

	SelectorDialog->OnResourceSelectionChanged.BindUObject(this, &ThisClass::HandleReplaceResourceFromDialog);

	UDSUISubsystem::GetInstance()->GetWindowLayoutWidget()->SetDialogueBoxContent(SelectorDialog);
}

void UOptionalResourceSelector::OnQueryCategoryTreeComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully)
{
	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		UE_LOG(LogOptionalResourceSelector, Error, TEXT("Failed to query the category tree by relative codes."));
		return;
	}

	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response->GetContentAsString());

	TSharedPtr<FJsonObject> ResponseObj;
	if (!FJsonSerializer::Deserialize(Reader, ResponseObj))
	{
		UE_LOG(LogTemp, Error, TEXT("%s - Failed to parse the response content as json."), __FUNCTIONW__);
		return;
	}

	if (!ResponseObj->HasTypedField<EJson::Boolean>(TEXT("success")) || !ResponseObj->GetBoolField(TEXT("success")))
	{
		UE_LOG(LogTemp, Error, TEXT("%s - Query category tree by relative codes failure, msg: %s."), __FUNCTIONW__, *ResponseObj->GetStringField(TEXT("msg")));
		return;
	}

	TArray<TSharedPtr<FDSCategoryInfo>> CategoryTree;

	const TArray<TSharedPtr<FJsonValue>>& RespArray = ResponseObj->GetArrayField(TEXT("resp"));
	for (const TSharedPtr<FJsonValue>& RespValue : RespArray)
	{
		const TSharedPtr<FJsonObject>& CategoryObj = RespValue->AsObject();
		if (!CategoryObj)
		{
			continue;
		}

		TSharedPtr<FDSCategoryInfo> NewCategory = MakeShared<FDSCategoryInfo>();
		FDSCategoryInfo::DeserializeFromJson(NewCategory, CategoryObj);
		CategoryTree.Add(NewCategory);
	}

	if (CategoryTree.IsEmpty())
	{
		//换成后台处理，不用前端再去调用查询关联接口，后台按照优先关联，再所有的顺序统一返回类目树，这里只要处理类目树为空的情况即可
		//SendQueryRequestByRelativeCode();
	}
	else
	{
		UResourceSelectorDialog* SelectorDialog = CreateWidget<UResourceSelectorDialog>(this, SelectorDialogClass);
		if (OptionalResourceParameter.IsValid())
		{
			SelectorDialog->InitDialog(CategoryTree, GetSelectedResourceInfo(), OptionalResourceParameter.Code, OptionalResourceParameter.ID);
		}
		else
		{
			SelectorDialog->InitDialog(CategoryTree, GetSelectedResourceInfo());
		}
		SelectorDialog->OnResourceSelectionChanged.BindUObject(this, &ThisClass::HandleReplaceResourceFromDialog);
		UDSUISubsystem::GetInstance()->GetWindowLayoutWidget()->SetDialogueBoxContent(SelectorDialog);
	}
}

void UOptionalResourceSelector::HandleReplaceResourceFromDialog(const TSharedPtr<FDSResourceInfo>& NewResource)
{
	UDSResourceSubsystem::GetInstance()->AddResourceCache(*NewResource);
	SetResourceInfo(NewResource);

	if (OnResourceSelectionChanged.IsBound())
	{
		OnResourceSelectionChanged.Execute(NewResource);
	}
}

UUserWidget* UOptionalResourceSelector::OnGetThumbnailMenuContent()
{
	return ResourceInfo.IsValid() ? ThumbnailWidget : nullptr;
}

void UOptionalResourceSelector::OnDetailsButtonHovered()
{
	if (ThumbnailWidget == nullptr)
	{
		ThumbnailWidget = UOptionalResourceSelectorThumbnail::CreateInstance(this);
	}

	if (ThumbnailWidget != nullptr)
	{
		ThumbnailWidget->ThumbnailBrush.SetResourceObject(ResourceImage->GetBrush().GetResourceObject());
	}

	ThumbnailMenuAnchor->Open(true);
}

void UOptionalResourceSelector::OnDetailsButtonUnhovered()
{
	ThumbnailMenuAnchor->Close();
}
