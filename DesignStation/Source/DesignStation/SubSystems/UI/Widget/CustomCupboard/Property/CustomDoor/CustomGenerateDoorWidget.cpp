// Fill out your copyright notice in the Description page of Project Settings.
#include "CustomGenerateDoorWidget.h"
#include "BaseNetworkFileTask.h"
#include "Button.h"
#include "CheckBox.h"
#include "ComboBoxString.h"
#include "DoorStyleWidget.h"
#include "DoorTypeButtonWidget.h"
#include "EasyNetworkFileSubsystem.h"
#include "EditableTextBox.h"
#include "TextBlock.h"
#include "Geometry/DataDefines/GeometryDatas.h"
#include "Subsystems/DSNetworkSubsystem.h"
#include "SubSystems/File/Core/ProtoDataConvert/ProtobufOperatorFunctionLibrary.h"
#include "SubSystems/Library/DesignStationFunctionLibrary.h"
#include "SubSystems/ModelDependencySubsystem/DSModelDependencySubsystem.h"
#include "Subsystems/MVC/DSMVCSubsystem.h"
#include "SubSystems/MVC/Model/Custom/DownloadTaskPayload/CustomRelativeResourcePayload.h"
#include "SubSystems/MVC/Model/Custom/DownloadTaskPayload/SoftFurnitureResourcePayload.h"
#include "SubSystems/MVC/Model/Custom/Library/DSCupboardLibrary.h"
#include "SubSystems/MVC/Model/Group/DSMultiModel.h"
#include "Subsystems/Resource/DSResourceSubsystem.h"
#include "SubSystems/UI/DSUISubsystem.h"
#include "SubSystems/UI/Widget/WindowLayoutWidget.h"
#include "SubSystems/UI/Widget/CustomCupboard/ReplacementDialog/ReplacementDialog.h"
#include "SubSystems/Undo/DSRevokeSubsystem.h"

extern const FString PARAM_NAME_STR;

extern const FString PARAM_H_STR;

extern const FString PARAM_W_STR;

extern const FString PARAM_D_STR;

extern const FString PARAM_WJ_STR;

extern const FString PARAM_HJ_STR;

extern const FString PARAM_LOC_X_STR;

extern const FString PARAM_LOC_Y_STR;

extern const FString PARAM_LOC_Z_STR;

extern const FString PARAM_ANGLE_X_STR;

extern const FString PARAM_ANGLE_Y_STR;

extern const FString PARAM_ANGLE_Z_STR;

extern const FString PARAM_MYGLX_STR;

extern const FString PARAM_LXMB_STR;

extern const FString PARAM_LY_STR;

extern const FString PARAM_DY_STR;

extern const FString PARAM_RY_STR;

extern const FString PARAM_UY_STR;

extern const FString PARAM_KMLB_STR;

extern const FString PARAM_MSX_STR;

extern const FString PARAM_KX_STR;

extern const FString PARAM_MX_STR;

extern const FString PARAM_ZMKX_STR;

extern const FString PARAM_YMKX_STR;

extern const FString PARAM_GTLB_STR;

extern const FString PARAM_ZBFG;

extern const FString PARAM_YBFG;

extern const FString PARAM_SBFG;

extern const FString PARAM_XBFG;

extern const FString PARAM_ZCBJT_STR;

extern const FString PARAM_XCBJT_STR;

extern const FString PARAM_YCBJT_STR;

extern const FString PARAM_SCBJT_STR;

extern const FString PARAM_MBT_STR;

extern const FString PLATE_STRUCTURE_FIXED_INNER_INDENT;

extern const FString PLATE_STRUCTURE_MOVEABLE_INNER_INDENT;

extern const FString PARAM_MHFDX_STR;

extern const FString PARAM_MHFSL_STR;

extern const FString PARAM_DKJD_STR;

UCustomGenerateDoorWidget::UCustomGenerateDoorWidget(const FObjectInitializer& Initializer)
	: Super(Initializer)
	  , NeedsDownloadResourceCount(0)
	  , DownloadedResourceCount(0)
	  , SelectModel(nullptr)
	  , MaskingType_ComboBox(nullptr)
	  , MaskingUp_ComboBox(nullptr)
	  , MaskingDown_ComboBox(nullptr)
	  , MaskingLeft_ComboBox(nullptr)
	  , MaskingRight_ComboBox(nullptr)
	  , OpenLeftDoor(nullptr)
	  , OpenRightDoor(nullptr)
	  , OpenUpDoor(nullptr)
	  , OpenDownDoor(nullptr)
	  , OpenMulDoor(nullptr)
	  , OpenDoorNum_TextBlock(nullptr)
	  , OpenLeft_CheckBox(nullptr)
	  , OpenRight_CheckBox(nullptr)
	  , Indentation_ETB(nullptr)
	  , DoorCrack_ETB(nullptr)
	  , DoorMatRot_ComboBox(nullptr)
	  , GenerateButton(nullptr)
	  , DoorNum_HB(nullptr)
	  , HB_KX(nullptr)
	  , DoorBoardStyle(nullptr)
	  , DoorBoardMat(nullptr)
	  , DoorCoreStyle(nullptr)
	  , DoorCoreMat(nullptr)
	  , bHasSelectDoorType(false)
	  , DoorContainerFolderId(TEXT("9999999"))
	  , DoorFolderId(TEXT(""))
	  , ReplacementDialog(nullptr)
	  , CurrentStyleWidget(nullptr)
	  , BoardsModel(nullptr)
	  , bDoorResourceFromStyle(true) {}

void UCustomGenerateDoorWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	//根据柜体Foldid查询关联的门板门芯，及当前门板门板的关联材质，全级联接口。
	QueryRelativeDoorResource();

	CreateReplacementDialog();

	AllTypeButtons = {OpenLeftDoor, OpenRightDoor, OpenUpDoor, OpenDownDoor, OpenMulDoor};

	AllDoorStyleButtons = {DoorBoardStyle, DoorBoardMat, DoorCoreStyle, DoorCoreMat};

	AllMaskingComboBoxs = {MaskingUp_ComboBox, MaskingDown_ComboBox, MaskingLeft_ComboBox, MaskingRight_ComboBox};

	for (auto& It : AllTypeButtons)
	{
		It->OnClickedDelegate.BindUFunction(this, FName(TEXT("OnTypeButtonsClick")));
	}

	for (auto& It : AllDoorStyleButtons)
	{
		It->OnShowReplaceDialog.BindUFunction(this, FName(TEXT("OnStyleDetailsClick")));
	}

	for (auto& It : AllMaskingComboBoxs)
	{
		It->AddOption(TEXT("全盖"));
		It->AddOption(TEXT("半盖"));
		It->AddOption(TEXT("不盖"));
		It->SetSelectedIndex(0);
	}

	GenerateButton->bIsEnabledDelegate.BindDynamic(this, &ThisClass::OnIsEnable);
	GenerateButton->OnClicked.AddDynamic(this, &ThisClass::GetGenerateDoorData);

	MaskingType_ComboBox->OnSelectionChanged.AddDynamic(this, &ThisClass::OnMaskingTypeComboBoxChanged);
}

UCustomGenerateDoorWidget* UCustomGenerateDoorWidget::Create()
{
	return UDesignStationFunctionLibrary::CreateUIWidget<UCustomGenerateDoorWidget>(TEXT("WidgetBlueprint'/Game/UI/MainPage/Property/CustomDoor/UMG_GenerateCustomDoor.UMG_GenerateCustomDoor_C'"));
}

void UCustomGenerateDoorWidget::UpdateProperty(UDSBaseModel* InModel)
{
	BoardsModel = Cast<UDSMultiModel>(InModel);
	if (BoardsModel == nullptr)
	{
		return;
	}

	bDoorResourceFromStyle = true;
	SelectModel = Cast<UDSCupboardModel>(BoardsModel->GetIncludeModel()[0]->GetTopLevelOwnerModel());
	UpdateStyleDetails(SelectModel);
}

void UCustomGenerateDoorWidget::QueryRelativeDoorResource()
{
	//先查柜体关联的门板门芯
	TSharedPtr<FJsonObject> QueryParams = MakeShared<FJsonObject>();

	TArray<TSharedPtr<FJsonValue>> CodeJsonArray;
	TSharedPtr<FJsonValueString> CodeJson = MakeShared<FJsonValueString>(TEXT("GLMB"));
	CodeJsonArray.Add(CodeJson);

	QueryParams->SetArrayField(TEXT("code"), CodeJsonArray);
	QueryParams->SetStringField(TEXT("FolderId"), SelectModel->GetModelInfoRef().ComponentTreeData->ComponentID.GetFormattedValue());
	QueryParams->SetNumberField(TEXT("type"), 2);

	FString QueryParamsStr;
	TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&QueryParamsStr);
	if (!FJsonSerializer::Serialize(QueryParams.ToSharedRef(), Writer))
	{
		UE_LOG(DSNetworkSubsystemLog, Warning, TEXT("%s - Serialize json into string failed."), __FUNCTIONW__);
		return;
	}

	FOnQueryCategoryTreeCompleteDelegate CallBack;
	CallBack.BindDynamic(this, &ThisClass::OnQueryRelativeDoorResourceComplete);
	UDSNetworkSubsystem::GetInstance()->SendQueryRelativeCategoryTreeByBkId(QueryParamsStr, CallBack);
}

void UCustomGenerateDoorWidget::OnQueryRelativeDoorResourceComplete(const TArray<FDSCategoryInfo>& Categories)
{
	if (Categories.IsEmpty())
	{
		//用风格默认项
		UpdateStyleDetails(SelectModel);
	}
	else
	{
		Categories[0].Children
	}
}

void UCustomGenerateDoorWidget::SetSpaceData(const FDSCraftDoorInfo& InSpaceData)
{
	SpaceData = InSpaceData;
}

void UCustomGenerateDoorWidget::ConvertInnerPointToContainerPoint(const FVector& WidthStart, const FVector& HeightStart, const FVector& OperatePoint, double BoardWidth, double BoardHeight,
                                                                  const FTransform& RootTransform, FVector& OutPoint)
{
	FVector WidthDir = OperatePoint - WidthStart;
	FVector HeightDir = OperatePoint - HeightStart;

	WidthDir.Normalize();
	HeightDir.Normalize();

	OutPoint = OperatePoint + WidthDir * BoardWidth;
	OutPoint = OutPoint + HeightDir * BoardHeight;
	OutPoint = RootTransform.InverseTransformPosition(OutPoint);
}

void UCustomGenerateDoorWidget::OnTypeButtonsClick(const int32& Id)
{
	bHasSelectDoorType = true;

	switch (Id)
	{
	case 0:
		GenerateDoorData.OpenDoorType = EOpenDoorType::EOpen;
		GenerateDoorData.OpenDoorDir = EOpenDoorDir::EOpenLeft;
		break;
	case 1:
		GenerateDoorData.OpenDoorType = EOpenDoorType::EOpen;
		GenerateDoorData.OpenDoorDir = EOpenDoorDir::EOpenRight;
		break;
	case 2:
		GenerateDoorData.OpenDoorType = EOpenDoorType::EFlipUp;
		GenerateDoorData.OpenDoorDir = EOpenDoorDir::EOpenUp;
		break;
	case 3:
		GenerateDoorData.OpenDoorType = EOpenDoorType::EFlipDown;
		GenerateDoorData.OpenDoorDir = EOpenDoorDir::EOpenDown;
		break;
	default:
		break;
	}
	for (auto& It : AllTypeButtons)
	{
		if (It->Id != Id)
		{
			It->UnSelected();
		}
	}
	if (Id == 4)
	{
		DoorNum_HB->SetVisibility(ESlateVisibility::Visible);
		OpenDoorNum_TextBlock->SetText(FText::FromString(FString::FromInt(2)));
		HB_KX->SetVisibility(ESlateVisibility::Visible);
	}
	else
	{
		DoorNum_HB->SetVisibility(ESlateVisibility::Collapsed);
		HB_KX->SetVisibility(ESlateVisibility::Collapsed);
		OpenDoorNum_TextBlock->SetText(FText::FromString(FString::FromInt(1)));
	}
}

void UCustomGenerateDoorWidget::GenerateCustomDoor()
{
	FRefToLocalFileData ContainerFileData;
	FString ContainerFilePath = FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("Resources"), FString::Printf(TEXT("%s.dat"), *DoorContainerFolderId));
	if (!FPaths::FileExists(ContainerFilePath) || !FProtobufOperatorFunctionLibrary::LoadRelationFromFile(ContainerFilePath, ContainerFileData))
	{
		return;
	}

	//clean pre ContainerNode;
	if (OldContainerNode)
	{
		for (const TSharedPtr<FMultiComponentDataItem>& OldDoor : OldContainerNode->ChildComponent)
		{
			SelectModel->MarkNodeSource(OldDoor->UUID, false);
			UDSModelDependencySubsystem::GetInstance()->RemoveDoorDependencyInfo(OldDoor->UUID);
		}

		SelectModel->GetModelInfoRef().ComponentTreeData->ChildComponent.Remove(OldContainerNode);
	}

	TSharedPtr<FMultiComponentDataItem> ContainerNode = MakeShared<FMultiComponentDataItem>();
	OldContainerNode = ContainerNode;

	UDSCupboardLibrary::ConstructTreeRootNode(ContainerFileData, *ContainerNode);
	ContainerNode->UUID.Empty();
	ContainerNode->bNeedLoadChildrenFromFile = false;

	FRefToLocalFileData DoorFileData;
	FString DoorFilePath = FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("Resources"), FString::Printf(TEXT("%s.dat"), *DoorFolderId));
	if (!FPaths::FileExists(DoorFilePath) || !FProtobufOperatorFunctionLibrary::LoadRelationFromFile(DoorFilePath, DoorFileData))
	{
		return;
	}

	FDSDoorDependencyInfo DependencyInfo;

	FTransform SelectModelTransform = SelectModel->GetPropertySharedPtr()->TransformProperty.ToUETransform();
	ConvertInnerPointToContainerPoint(SpaceData.PlanePoints[1], SpaceData.PlanePoints[3], SpaceData.PlanePoints[0],
	                                  FCString::Atod(*GenerateDoorData.YCBJT) * 0.1f, FCString::Atod(*GenerateDoorData.SCBJT) * 0.1f, SelectModelTransform,
	                                  DependencyInfo.DoorContainerPlane.Points.AddDefaulted_GetRef());

	ConvertInnerPointToContainerPoint(SpaceData.PlanePoints[0], SpaceData.PlanePoints[2], SpaceData.PlanePoints[1],
	                                  FCString::Atod(*GenerateDoorData.ZCBJT) * 0.1f, FCString::Atod(*GenerateDoorData.SCBJT) * 0.1f, SelectModelTransform,
	                                  DependencyInfo.DoorContainerPlane.Points.AddDefaulted_GetRef());

	ConvertInnerPointToContainerPoint(SpaceData.PlanePoints[3], SpaceData.PlanePoints[1], SpaceData.PlanePoints[2],
	                                  FCString::Atod(*GenerateDoorData.ZCBJT) * 0.1f, FCString::Atod(*GenerateDoorData.XCBJT) * 0.1f, SelectModelTransform,
	                                  DependencyInfo.DoorContainerPlane.Points.AddDefaulted_GetRef());

	ConvertInnerPointToContainerPoint(SpaceData.PlanePoints[2], SpaceData.PlanePoints[0], SpaceData.PlanePoints[3],
	                                  FCString::Atod(*GenerateDoorData.YCBJT) * 0.1f, FCString::Atod(*GenerateDoorData.XCBJT) * 0.1f, SelectModelTransform,
	                                  DependencyInfo.DoorContainerPlane.Points.AddDefaulted_GetRef());

	DependencyInfo.DoorContainerPlane.Center = (DependencyInfo.DoorContainerPlane.Points[0] + DependencyInfo.DoorContainerPlane.Points[3]) * 0.5f;
	DependencyInfo.DoorContainerPlane.Normal = SelectModelTransform.InverseTransformVector(SpaceData.PlaneNormal);

	FVector DoorContainerPlaneUpVector = DependencyInfo.DoorContainerPlane.Points[1] - DependencyInfo.DoorContainerPlane.Points[2];
	FRotator DoorRotation = FRotationMatrix::MakeFromYZ(DependencyInfo.DoorContainerPlane.Normal, DoorContainerPlaneUpVector.GetSafeNormal()).Rotator();

	for (UDSBaseModel* DependentModel : SpaceData.Dependencies)
	{
		UDSCupboardModel* BoardModel = Cast<UDSCupboardModel>(DependentModel);
		if (BoardModel == nullptr)
		{
			continue;
		}

		UDSCupboardModel* RootCupboard = BoardModel->GetRootCupboardModel();
		if (RootCupboard == nullptr)
		{
			continue;
		}

		DependencyInfo.DependentBoards.Add({BoardModel->GetModelInfoRef().ComponentTreeData->UUID, RootCupboard->GetModelInfoRef().ComponentTreeData->UUID});
	}

	TSharedPtr<FMultiComponentDataItem> OriginalDoorNode = MakeShared<FMultiComponentDataItem>();
	UDSCupboardLibrary::ConstructTreeRootNode(DoorFileData, *OriginalDoorNode);

	SetDataProperty(*OriginalDoorNode, GenerateDoorData);

	FVector WidthDir = DependencyInfo.DoorContainerPlane.Points[0] - DependencyInfo.DoorContainerPlane.Points[1];
	WidthDir.Normalize();

	DependencyInfo.DoorPlane.Points.Init(FVector::ZeroVector, 4);

	float WidthFactor = 1.0 / GenerateDoorData.OpenDoorNum;
	double PlaneWidth = FVector::Distance(DependencyInfo.DoorContainerPlane.Points[0], DependencyInfo.DoorContainerPlane.Points[1]);
	for (int32 Index = 0; Index < GenerateDoorData.OpenDoorNum; Index++)
	{
		TSharedPtr<FMultiComponentDataItem> NewDoorNode = MakeShared<FMultiComponentDataItem>();
		NewDoorNode->DeepCopy(*OriginalDoorNode);
		NewDoorNode->GenerateUUID();
		ContainerNode->ChildComponent.Add(NewDoorNode);

		EOpenDoorDir InverseDoorDir = GenerateDoorData.OpenDoorDir == EOpenDoorDir::EOpenLeft ? EOpenDoorDir::EOpenRight : EOpenDoorDir::EOpenLeft;
		EOpenDoorDir CurrentDoorDir = (Index % 2) == 0 ? GenerateDoorData.OpenDoorDir : InverseDoorDir;

		FParameterData* MaterialParam = NewDoorNode->ComponentParameters.FindByPredicate([](const FParameterData& InParam) { return InParam.Data.name == TEXT("MV"); });
		if (MaterialParam != nullptr)
		{
			SelectModel->PushNodeRecord(NewDoorNode->UUID, *MaterialParam, false);
			//change door material,add door material folder id
			SetDataParam(*NewDoorNode, TEXT("MV"), DoorMaterialFolderId);
		}
		else
		{
			//if no mv,query style material from DoorBoardMat UI widget's property
			NewDoorNode->ComponentParameters.Add(DoorBoardMat->Params);
			SetDataParam(*NewDoorNode, TEXT("MV"), DoorMaterialFolderId);
			SelectModel->PushNodeRecord(NewDoorNode->UUID, DoorBoardMat->Params, true);
		}

		SetDataParam(*NewDoorNode, TEXT("KX"), FString::Printf(TEXT("%d"), static_cast<int32>(CurrentDoorDir)));

		FString WJParam = FString::Printf(TEXT("%f"), WidthFactor * PlaneWidth * 10.0f);
		SetDataParam(*NewDoorNode, PARAM_WJ_STR, WJParam);

		if (GenerateDoorData.OpenDoorNum > 1)
		{
			if (Index == 0)
			{
				SetDataParam(*NewDoorNode, PARAM_YBFG, FString::FromInt(static_cast<int32>(EMaskingMode::EOuterOver)));
			}
			else if (Index == (GenerateDoorData.OpenDoorNum - 1))
			{
				SetDataParam(*NewDoorNode, PARAM_ZBFG, FString::FromInt(static_cast<int32>(EMaskingMode::EOuterOver)));
			}
			else
			{
				SetDataParam(*NewDoorNode, PARAM_YBFG, FString::FromInt(static_cast<int32>(EMaskingMode::EOuterOver)));
				SetDataParam(*NewDoorNode, PARAM_ZBFG, FString::FromInt(static_cast<int32>(EMaskingMode::EOuterOver)));
			}
		}

		DependencyInfo.DoorPlane.Points[0] = DependencyInfo.DoorContainerPlane.Points[1] + WidthDir * ((Index + 1.0f) / GenerateDoorData.OpenDoorNum * PlaneWidth);
		DependencyInfo.DoorPlane.Points[1] = DependencyInfo.DoorContainerPlane.Points[1] + WidthDir * (Index * 1.0f / GenerateDoorData.OpenDoorNum * PlaneWidth);
		DependencyInfo.DoorPlane.Points[2] = DependencyInfo.DoorContainerPlane.Points[2] + WidthDir * (Index * 1.0f / GenerateDoorData.OpenDoorNum * PlaneWidth);
		DependencyInfo.DoorPlane.Points[3] = DependencyInfo.DoorContainerPlane.Points[2] + WidthDir * ((Index + 1.0f) / GenerateDoorData.OpenDoorNum * PlaneWidth);

		DependencyInfo.DoorPlane.Center = (DependencyInfo.DoorPlane.Points[0] + DependencyInfo.DoorPlane.Points[2]) * 0.5f;
		DependencyInfo.DoorPlane.Normal = DependencyInfo.DoorContainerPlane.Normal;

		FTransform FinalTransform = FTransform(DoorRotation) * FTransform(FQuat::Identity, DependencyInfo.DoorPlane.Points[2] * 10.0f);

		NewDoorNode->ComponentRotation = FinalTransform.GetRotation().Rotator();
		NewDoorNode->ComponentLocation = FinalTransform.GetLocation();

		UDSModelDependencySubsystem::GetInstance()->AddDoorDependencyInfo(NewDoorNode->UUID, DependencyInfo);

		SelectModel->MarkNodeSource(NewDoorNode->UUID, !bDoorResourceFromStyle);
	}

	//revoke
	TSharedPtr<FDSCustomComponentRevokeData> NewRevokeData = MakeShared<FDSCustomComponentRevokeData>();
	NewRevokeData->SetComponentData(ContainerNode, true);
	NewRevokeData->SetComponentCommandType(EDSRevokeComponentType::E_Component_Add);
	//index from 0, new node is current num
	FString NewNodePath = FString::Printf(TEXT("%02d"), SelectModel->GetModelInfoRef().ComponentTreeData->ChildComponent.Num());
	NewRevokeData->SetNodePath(NewNodePath);

	FDSRevokePushData PushData(EDSPushDataType::E_Custom, FDSModelExecuteType::ExecuteUpdateSelf, false);
	PushData.SetData(FDSCustomPushData(FDSModelExecuteType::ExecuteUpdateSelf, SelectModel, NewRevokeData));
	UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(
		SelectModel,
		FDSModelExecuteType::ExecuteUpdateSelf, PushData, UDSMVCSubsystem::GetInstance()->GetRevokeMark(), TEXT(""));

	TMap<FString, FParameterData> RootCupboardParams;
	SelectModel->GetSelfComponentOverriderParametersRef(RootCupboardParams);

	FGeometryDatas::CalculateCurMultiComponentData(UDSMVCSubsystem::GetInstance()->GetGlobalParamsMap(), RootCupboardParams, *ContainerNode);

	SelectModel->GetModelInfoRef().ComponentTreeData->ChildComponent.Add(ContainerNode);
	SelectModel->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
}

void UCustomGenerateDoorWidget::GetGenerateDoorData()
{
	GenerateDoorData.OpenDoorNum = FCString::Atoi(*OpenDoorNum_TextBlock->GetText().ToString());
	if (HB_KX->GetVisibility() != ESlateVisibility::Collapsed)
	{
		if (OpenLeft_CheckBox->GetCheckedState() == ECheckBoxState::Checked)
		{
			GenerateDoorData.OpenDoorDir = EOpenDoorDir::EOpenLeft;
		}
		if (OpenRight_CheckBox->GetCheckedState() == ECheckBoxState::Checked)
		{
			GenerateDoorData.OpenDoorDir = EOpenDoorDir::EOpenRight;
		}
	}

	GenerateDoorData.DoorMaskingType = static_cast<EMaskingType>(MaskingType_ComboBox->GetSelectedIndex());

	GenerateDoorData.MaskingModeUp = static_cast<EMaskingMode>(MaskingUp_ComboBox->GetSelectedIndex());
	GenerateDoorData.MaskingModeDown = static_cast<EMaskingMode>(MaskingDown_ComboBox->GetSelectedIndex());
	GenerateDoorData.MaskingModeLeft = static_cast<EMaskingMode>(MaskingLeft_ComboBox->GetSelectedIndex());
	GenerateDoorData.MaskingModeRight = static_cast<EMaskingMode>(MaskingRight_ComboBox->GetSelectedIndex());

	GenerateDoorData.Indentation = FCString::Atoi(*Indentation_ETB->GetText().ToString());
	GenerateDoorData.DoorCrack = FCString::Atoi(*DoorCrack_ETB->GetText().ToString());

	GenerateDoorData.MaskingModeRight = static_cast<EMaskingMode>(MaskingRight_ComboBox->GetSelectedIndex());

	if (DoorMatRot_ComboBox->GetSelectedIndex() == 0)
	{
		GenerateDoorData.DoorMatRot = TEXT("0");
	}
	else if (DoorMatRot_ComboBox->GetSelectedIndex() == 1)
	{
		GenerateDoorData.DoorMatRot = TEXT("90");
	}
	else if (DoorMatRot_ComboBox->GetSelectedIndex() == 2)
	{
		GenerateDoorData.DoorMatRot = TEXT("180");
	}
	else if (DoorMatRot_ComboBox->GetSelectedIndex() == 3)
	{
		GenerateDoorData.DoorMatRot = TEXT("270");
	}

	for (auto& It : Cast<UDSCupboardModel>(SpaceData.Dependencies[0])->GetModelInfo().ComponentTreeData->ChildComponent[0]->ComponentParameters)
	{
		if (It.Data.name.Contains(TEXT("H1")))
		{
			GenerateDoorData.SCBJT = It.Data.value;
		}
	}

	for (auto& It : Cast<UDSCupboardModel>(SpaceData.Dependencies[1])->GetModelInfo().ComponentTreeData->ChildComponent[0]->ComponentParameters)
	{
		if (It.Data.name.Contains(TEXT("H1")))
		{
			GenerateDoorData.XCBJT = It.Data.value;
		}
	}
	for (auto& It : Cast<UDSCupboardModel>(SpaceData.Dependencies[2])->GetModelInfo().ComponentTreeData->ChildComponent[0]->ComponentParameters)
	{
		if (It.Data.name.Contains(TEXT("H1")))
		{
			GenerateDoorData.ZCBJT = It.Data.value;
		}
	}
	for (auto& It : Cast<UDSCupboardModel>(SpaceData.Dependencies[3])->GetModelInfo().ComponentTreeData->ChildComponent[0]->ComponentParameters)
	{
		if (It.Data.name.Contains(TEXT("H1")))
		{
			GenerateDoorData.YCBJT = It.Data.value;
		}
	}
	auto WJ = FVector::Distance(SpaceData.PlanePoints[0], SpaceData.PlanePoints[1]) * 10 + FCString::Atod(*GenerateDoorData.ZCBJT) + FCString::Atod(*GenerateDoorData.YCBJT);
	auto HJ = FVector::Distance(SpaceData.PlanePoints[1], SpaceData.PlanePoints[2]) * 10 + FCString::Atod(*GenerateDoorData.SCBJT) + FCString::Atod(*GenerateDoorData.XCBJT);

	GenerateDoorData.WJ = FString::SanitizeFloat(WJ);
	GenerateDoorData.HJ = FString::SanitizeFloat(HJ);

	GenerateCustomDoor();
}

bool UCustomGenerateDoorWidget::OnIsEnable()
{
	return bHasSelectDoorType;
}

void UCustomGenerateDoorWidget::OnMaskingTypeComboBoxChanged(FString SelectedItem, ESelectInfo::Type SelectionType)
{
	if (MaskingType_ComboBox->GetSelectedIndex() == 0)
	{
		for (auto& It : AllMaskingComboBoxs)
		{
			It->ClearOptions();
			It->AddOption(TEXT("全盖"));
			It->AddOption(TEXT("半盖"));
			It->AddOption(TEXT("不盖"));
			It->SetSelectedIndex(0);
		}
	}
	else
	{
		for (auto& It : AllMaskingComboBoxs)
		{
			It->ClearOptions();
			It->AddOption(TEXT("内嵌"));
			It->SetSelectedIndex(0);
		}
	}
}

void UCustomGenerateDoorWidget::CreateReplacementDialog()
{
	if (!ReplacementDialogClass)
	{
		return;
	}

	ReplacementDialog = CreateWidget<UReplacementDialog>(this, ReplacementDialogClass);
}

void UCustomGenerateDoorWidget::ShowReplacementDialog(const FString& GlType, const FParameterData& InParams, const FRefToLocalFileData& LocalFileData)
{
	if (SelectModel == nullptr || !UDSUISubsystem::IsInitialized())
	{
		return;
	}

	UWindowLayoutWidget* WindowLayoutWidget = UDSUISubsystem::GetInstance()->GetWindowLayoutWidget();
	if (WindowLayoutWidget == nullptr)
	{
		return;
	}

	FString FoldId = SelectModel->GetModelInfoRef().ComponentTreeData->ComponentID.GetFormattedValue();

	ReplacementDialog->ReplaceItemClickedDelegate.BindUFunction(this, FName(TEXT("OnReplaceItemClicked")));
	ReplacementDialog->SetEmployer(nullptr, GlType, FoldId, 0, InParams, LocalFileData);
	WindowLayoutWidget->SetDialogueBoxContent(ReplacementDialog);
}

void UCustomGenerateDoorWidget::OnReplaceItemClicked(FDSResourceInfo ResourceInfo)
{
	if (ResourceInfo.Type == EDSResourceType::Custom)
	{
		UDSResourceSubsystem::GetInstance()->AddResourceCache(ResourceInfo);

		FRefToLocalFileData StyleFileData;
		FString StyleFilePath = FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("Resources"), FString::Printf(TEXT("%s.dat"), *ResourceInfo.FolderId));
		if (!FPaths::FileExists(StyleFilePath) || !FProtobufOperatorFunctionLibrary::LoadRelationFromFile(StyleFilePath, StyleFileData))
		{
			return;
		}

		DoorFolderId = StyleFileData.FolderDBData.folder_id; //第一时间替换门样式的ID
		CurrentStyleWidget->BoardName = FText::FromString(StyleFileData.FolderDBData.folder_name);
		CurrentStyleWidget->BoardCode = FText::FromString(StyleFileData.FolderDBData.folder_code);
		CurrentStyleWidget->ImageURL = StyleFileData.FolderDBData.thumbnail_path;
		CurrentStyleWidget->DownloadVisualFromUrl(StyleFileData.FolderDBData.thumbnail_path);
		CurrentStyleWidget->LocalFileData = StyleFileData;

		bDoorResourceFromStyle = ReplacementDialog->IsReplacedFromStyle();
	}
	else
	{
		UWorld* World = GetWorld();
		if (World == nullptr)
		{
			return;
		}

		UEasyNetworkFileSubsystem* EasyNetworkFileSubsystem = World->GetGameInstance()->GetSubsystem<UEasyNetworkFileSubsystem>();
		if (EasyNetworkFileSubsystem == nullptr)
		{
			return;
		}

		FDSResourceFile FileInfo = ResourceInfo.GetResourceFile(EDSResourceQuality::Low);
		if (FileInfo.FilePath.IsEmpty())
		{
			return;
		}

		UBaseNetworkFileTask* NewTask = EasyNetworkFileSubsystem->CreateTask(ENetworkFileOperation::ENFO_Download);
		if (FileInfo.FilePath.StartsWith(TEXT("http://")) || FileInfo.FilePath.StartsWith(TEXT("https://")))
		{
			NewTask->SetUrl(ResourceInfo.Type == EDSResourceType::Material ? FileInfo.FilePath : FileInfo.FilePath);
		}
		else
		{
			NewTask->SetUrl(FileInfo.FilePath);
		}

		NewTask->SetFilePath(FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("Resources"), FileInfo.ComputeLocalFileName()));
		NewTask->SetChecksum(FileInfo.MD5);

		UCustomRelativeResourcePayload* Payload = NewObject<UCustomRelativeResourcePayload>(NewTask);
		Payload->ResourceInfo = ResourceInfo;
		NewTask->SetPayload(Payload);

		NewTask->OnTaskCompleteEvent().AddDynamic(this, &ThisClass::OnReplaceItemDownloadResourceTaskComplete);
		NewTask->StartProcess();
	}
}

void UCustomGenerateDoorWidget::OnReplaceItemDownloadResourceTaskComplete(const FString& TaskId, bool bSucceed)
{
	UWorld* World = GetWorld();
	if (World == nullptr)
	{
		return;
	}

	UEasyNetworkFileSubsystem* EasyNetworkFileSubsystem = World->GetGameInstance()->GetSubsystem<UEasyNetworkFileSubsystem>();
	if (!UDSResourceSubsystem::IsInitialized() || EasyNetworkFileSubsystem == nullptr)
	{
		return;
	}

	UBaseNetworkFileTask* Task = EasyNetworkFileSubsystem->FindTask(TaskId);
	UCustomRelativeResourcePayload* Payload = Cast<UCustomRelativeResourcePayload>(Task->GetPayload());
	if (Payload == nullptr)
	{
		return;
	}

	if (!bSucceed)
	{
		UE_LOG(LogTemp, Warning, TEXT("Download relative resource '%s' failure."), *Payload->ResourceInfo.Id);
		return;
	}

	UDSResourceSubsystem::GetInstance()->AddResourceCache(Payload->ResourceInfo);

	if (Payload->ResourceInfo.Type == EDSResourceType::Material)
	{
		FString MatFilePath = FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("Resources"), Payload->ResourceInfo.GetResourceFile(EDSResourceQuality::Low).ComputeLocalFileName());
		if (!FPaths::FileExists(MatFilePath))
		{
			return;
		}
		DoorMaterialFolderId = Payload->ResourceInfo.FolderId; //第一时间替换门材质的ID
		CurrentStyleWidget->BoardName = FText::FromString(Payload->ResourceInfo.Name);
		CurrentStyleWidget->BoardCode = FText::FromString(Payload->ResourceInfo.FolderCode);
		CurrentStyleWidget->ImageURL = Payload->ResourceInfo.ProductImg;
		CurrentStyleWidget->DownloadVisualFromUrl(Payload->ResourceInfo.ProductImg);
	}
}

void UCustomGenerateDoorWidget::OnStyleDetailsClick(UDoorStyleWidget* StyleWidgetSelf, const FString& GlType, const FParameterData& InParams, const FRefToLocalFileData& LocalFileData)
{
	CurrentStyleWidget = StyleWidgetSelf;
	ShowReplacementDialog(GlType, InParams, LocalFileData);
}

void UCustomGenerateDoorWidget::UpdateStyleDetails(UDSCupboardModel* InCupboardFurniture)
{
	//先根据柜体FoldId关联去查，没有再用风格默认配置
	if (InCupboardFurniture)
	{
		auto StyleData = InCupboardFurniture->GetModelInfo().ApplyStyle_Whole;
		for (auto OptionData : StyleData.ApplySelectOptionData)
		{
			for (auto param : OptionData.option_params)
			{
				if (param.Data.name == TEXT("MX"))
				{
					DoorFolderId = param.Data.value;
					DoorBoardStyle->SetVisibility(ESlateVisibility::Visible);
					DoorBoardStyle->BoardName = FText::FromString(OptionData.option_description);
					DoorBoardStyle->BoardCode = FText::FromString(OptionData.option_code);
					DoorBoardStyle->ImageURL = OptionData.option_thumbnail_url;
					DoorBoardStyle->DownloadVisualFromUrl(OptionData.option_thumbnail_url);
					DoorBoardStyle->StyleType = TEXT("GLMB");
					DoorBoardStyle->Params = param;
				}
				if (param.Data.name == TEXT("MV"))
				{
					DoorMaterialFolderId = param.Data.value;
					DoorBoardMat->SetVisibility(ESlateVisibility::Visible);
					DoorBoardMat->BoardName = FText::FromString(OptionData.option_description);
					DoorBoardMat->BoardCode = FText::FromString(OptionData.option_code);
					DoorBoardMat->ImageURL = OptionData.option_thumbnail_url;
					DoorBoardMat->DownloadVisualFromUrl(OptionData.option_thumbnail_url);
					DoorBoardMat->StyleType = TEXT("GLCZ");
					DoorBoardMat->Params = param;
				}
			}
		}
	}
}

void UCustomGenerateDoorWidget::SetDataProperty(FMultiComponentDataItem& InMulti, const FDSCustomDoorProperty& InProp)
{
	FString DoorMaskingType = FString::FromInt(static_cast<int32>(InProp.DoorMaskingType));
	SetDataParam(InMulti, PARAM_MYGLX_STR, DoorMaskingType);

	FString MaskingModeUp = FString::FromInt(static_cast<int32>(InProp.MaskingModeUp));
	SetDataParam(InMulti, PARAM_SBFG, MaskingModeUp);

	FString MaskingModeDown = FString::FromInt(static_cast<int32>(InProp.MaskingModeDown));
	SetDataParam(InMulti, PARAM_XBFG, MaskingModeDown);

	FString MaskingModeLeft = FString::FromInt(static_cast<int32>(InProp.MaskingModeLeft));
	SetDataParam(InMulti, PARAM_ZBFG, MaskingModeLeft);

	FString MaskingModeRight = FString::FromInt(static_cast<int32>(InProp.MaskingModeRight));
	SetDataParam(InMulti, PARAM_YBFG, MaskingModeRight);

	FString SCBJT = InProp.SCBJT;
	SetDataParam(InMulti, PARAM_SCBJT_STR, SCBJT);

	FString XCBJT = InProp.XCBJT;
	SetDataParam(InMulti, PARAM_XCBJT_STR, XCBJT);

	FString ZCBJT = InProp.ZCBJT;
	SetDataParam(InMulti, PARAM_ZCBJT_STR, ZCBJT);

	FString YCBJT = InProp.YCBJT;
	SetDataParam(InMulti, PARAM_YCBJT_STR, YCBJT);

	FString HJ = InProp.HJ;
	SetDataParam(InMulti, PARAM_HJ_STR, HJ);

	FString DoorType = FString::FromInt(static_cast<int32>(InProp.OpenDoorType));
	SetDataParam(InMulti, PARAM_LXMB_STR, DoorType);

	FString DoorDir = FString::FromInt(static_cast<int32>(InProp.OpenDoorDir));
	SetDataParam(InMulti, PARAM_KX_STR, DoorDir);

	FString DoorRot = InProp.DoorMatRot;
	SetDataParam(InMulti, PARAM_DKJD_STR, DoorRot);
}

void UCustomGenerateDoorWidget::SetDataParam(FMultiComponentDataItem& InMulti, FString ParamName, FString ParamValue)
{
	auto& Params = InMulti.ComponentParameters;
	for (auto& Iter : Params)
	{
		if (Iter.Data.name.Equals(ParamName))
		{
			Iter.Data.expression = ParamValue;
			Iter.Data.value = ParamValue;
			break;
		}
	}
}
