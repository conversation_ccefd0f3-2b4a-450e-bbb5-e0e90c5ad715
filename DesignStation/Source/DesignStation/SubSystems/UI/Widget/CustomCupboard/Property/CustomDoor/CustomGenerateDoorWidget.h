// Fill out your copyright notice in the Description page of Project Settings.

#pragma once
#include "CoreMinimal.h"
#include "CheckBox.h"
#include "ComboBoxString.h"
#include "DoorStyleWidget.h"
#include "DoorTypeButtonWidget.h"
#include "EditableTextBox.h"
#include "HorizontalBox.h"
#include "IHttpRequest.h"
#include "Subsystems/MVC/Model/Custom/DSCupboardModel.h"
#include "SubSystems/MVC/Model/Group/DSMultiModel.h"
#include "SubSystems/UI/Widget/CustomCupboard/CustomPageFloatingToolBar/CraftMenuContent/Structures/CraftStructures.h"
#include "SubSystems/UI/Widget/CustomCupboard/ReplacementDialog/ReplacementDialog.h"
#include "CustomGenerateDoorWidget.generated.h"

UENUM()
enum class EOpenDoorDir : uint8
{
	EOpenLeft = 0,
	EO<PERSON>Right,
	EOpenUp,
	EOpenDown,
};

UENUM()
enum class EOpenDoorType : uint8
{
	EOpen = 41,
	<PERSON><PERSON><PERSON>Up,
	EFlipDown,
	EDrawer,
	Slide,
};

UENUM()
enum class EMaskingType : uint8
{
	EOuterOver = 0, EEmbedded
};

UENUM()
enum class EMaskingMode : uint8
{
	EOuterOver = 0,
	ECoverHalf,
	ENoCover,
	EEmbedded
};

UENUM()
enum class EDoorMatRotate : uint8
{
	ERotZero = 0,
	ERotNinety,
	ERotOneHundredAndEighty,
	ERotTwoHundredSeventy,
};

USTRUCT(BlueprintType)
struct DESIGNSTATION_API FDSCustomDoorProperty
{
	GENERATED_BODY()

	FDSCustomDoorProperty()
		: OpenDoorType(EOpenDoorType::EOpen)
		  , OpenDoorNum(1)
		  , OpenDoorDir(EOpenDoorDir::EOpenLeft)
		  , DoorMaskingType(EMaskingType::EOuterOver)
		  , MaskingModeUp(EMaskingMode::EOuterOver)
		  , MaskingModeDown(EMaskingMode::EOuterOver)
		  , MaskingModeLeft(EMaskingMode::EOuterOver)
		  , MaskingModeRight(EMaskingMode::EOuterOver)
		  , Indentation(0)
		  , DoorCrack(0)
		  , DoorMatRot("0") {}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DSCustomDoorProperty")
	EOpenDoorType OpenDoorType; //门板类型

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DSCustomDoorProperty")
	int32 OpenDoorNum;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DSCustomDoorProperty")
	EOpenDoorDir OpenDoorDir;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DSCustomDoorProperty")
	EMaskingType DoorMaskingType;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DSCustomDoorProperty")
	EMaskingMode MaskingModeUp;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DSCustomDoorProperty")
	EMaskingMode MaskingModeDown;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DSCustomDoorProperty")
	EMaskingMode MaskingModeLeft;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DSCustomDoorProperty")
	EMaskingMode MaskingModeRight;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DSCustomDoorProperty")
	int32 Indentation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DSCustomDoorProperty")
	int32 DoorCrack;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DSCustomDoorProperty")
	FString SCBJT;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DSCustomDoorProperty")
	FString XCBJT;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DSCustomDoorProperty")
	FString ZCBJT;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DSCustomDoorProperty")
	FString YCBJT;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DSCustomDoorProperty")
	FString WJ;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DSCustomDoorProperty")
	FString HJ;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DSCustomDoorProperty")
	FString MBPXH;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DSCustomDoorProperty")
	FString MHFDX;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "DSCustomDoorProperty")
	FString DoorMatRot;
};

UCLASS()
class DESIGNSTATION_API UCustomGenerateDoorWidget : public UUserWidget
{
	GENERATED_BODY()

public:
	UCustomGenerateDoorWidget(const FObjectInitializer& Initializer);

	virtual void NativeOnInitialized() override;

	static UCustomGenerateDoorWidget* Create();

	void UpdateProperty(UDSBaseModel* InModel);

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "CustomProperty | Classes")
	TSubclassOf<UReplacementDialog> ReplacementDialogClass;

	UFUNCTION()
	void SetSpaceData(const FDSCraftDoorInfo& InSpaceData);

protected:
	void ConvertInnerPointToContainerPoint(const FVector& WidthStart, const FVector& HeightStart, const FVector& OperatePoint, double BoardWidth, double BoardHeight, const FTransform& RootTransform,
	                                       FVector& OutPoint);

private:
	UFUNCTION()
	void OnTypeButtonsClick(const int32& Id);

	UFUNCTION()
	void GenerateCustomDoor();

	UFUNCTION()
	void GetGenerateDoorData();

	UFUNCTION()
	bool OnIsEnable();

	UFUNCTION()
	void OnMaskingTypeComboBoxChanged(FString SelectedItem, ESelectInfo::Type SelectionType);

	UFUNCTION()
	void CreateReplacementDialog();

	UFUNCTION()
	virtual void ShowReplacementDialog(const FString& GlType, const FParameterData& InParams, const FRefToLocalFileData& LocalFileData);

	UFUNCTION()
	void OnReplaceItemClicked(FDSResourceInfo ResourceInfo);

	UFUNCTION()
	void OnReplaceItemDownloadResourceTaskComplete(const FString& TaskId, bool bSucceed);

	UFUNCTION()
	void OnStyleDetailsClick(UDoorStyleWidget* StyleWidgetSelf, const FString& StyleType, const FParameterData& InParams, const FRefToLocalFileData& LocalFileData);

	UFUNCTION()
	void UpdateStyleDetails(UDSCupboardModel* InCupboardFurniture);

	void SetDataParam(FMultiComponentDataItem& InMulti, FString ParamName, FString ParamValue);

	void SetDataProperty(FMultiComponentDataItem& InMulti, const FDSCustomDoorProperty& InProp);

	void AdjustDoorCoverParameters();

	UFUNCTION()
	void QueryRelativeDoorResource();

	UFUNCTION()
	void OnQueryRelativeDoorResourceComplete(const TArray<FDSCategoryInfo>& Categories);

	UPROPERTY()
	FDSCraftDoorInfo SpaceData;

	FHttpRequestPtr HttpRequestHandle;

	int32 NeedsDownloadResourceCount;

	int32 DownloadedResourceCount;

	TArray<FString> ProcessingTaskIds;

	TMap<FString, bool> RelativeMaterials;

protected:
	UPROPERTY()
	UDSCupboardModel* SelectModel;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UComboBoxString* MaskingType_ComboBox;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UComboBoxString* MaskingUp_ComboBox;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UComboBoxString* MaskingDown_ComboBox;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UComboBoxString* MaskingLeft_ComboBox;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UComboBoxString* MaskingRight_ComboBox;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UDoorTypeButton* OpenLeftDoor;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UDoorTypeButton* OpenRightDoor;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UDoorTypeButton* OpenUpDoor;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UDoorTypeButton* OpenDownDoor;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UDoorTypeButton* OpenMulDoor;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UTextBlock* OpenDoorNum_TextBlock;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UCheckBox* OpenLeft_CheckBox;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UCheckBox* OpenRight_CheckBox;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UEditableTextBox* Indentation_ETB;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UEditableTextBox* DoorCrack_ETB;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UComboBoxString* DoorMatRot_ComboBox;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UButton* GenerateButton;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UHorizontalBox* DoorNum_HB;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UHorizontalBox* HB_KX;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UDoorStyleWidget* DoorBoardStyle;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UDoorStyleWidget* DoorBoardMat;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UDoorStyleWidget* DoorCoreStyle;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UDoorStyleWidget* DoorCoreMat;

	UPROPERTY()
	TArray<UDoorTypeButton*> AllTypeButtons;

	UPROPERTY()
	TArray<UDoorStyleWidget*> AllDoorStyleButtons;

	UPROPERTY()
	TArray<UComboBoxString*> AllMaskingComboBoxs;

	UPROPERTY()
	FDSCustomDoorProperty GenerateDoorData;

	UPROPERTY()
	bool bHasSelectDoorType;

	UPROPERTY()
	FString DoorContainerFolderId;

	UPROPERTY()
	FString DoorFolderId;

	UPROPERTY()
	FString DoorMaterialFolderId;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UReplacementDialog* ReplacementDialog;

	UPROPERTY()
	UDoorStyleWidget* CurrentStyleWidget;

	TSharedPtr<FMultiComponentDataItem> OldContainerNode;

	UPROPERTY()
	UDSMultiModel* BoardsModel;

	UPROPERTY()
	bool bDoorResourceFromStyle;
};
