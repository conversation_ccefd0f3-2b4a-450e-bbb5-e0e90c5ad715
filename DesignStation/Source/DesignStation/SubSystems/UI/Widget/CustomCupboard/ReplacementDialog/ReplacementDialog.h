// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UserWidget.h"
#include "Interfaces/IHttpRequest.h"
#include "LocalCache/Parameter/ParameterDataDefine.h"
#include "SubSystems/MVC/CatalogSupport/Core/RefToFileData.h"
#include "Subsystems/UI/Widget/Common/CategoryTree/Structures/CategoryInfo.h"
#include "Subsystems/UI/Widget/Common/ResourceItem/Structures/ResourceInfo.h"
#include "ReplacementDialog.generated.h"

struct FQueryResourceListParams;

struct FRefToLocalFileData;

class UTextBlock;

class UButton;

class UWrapBox;

class UImage;

class UCategoryTree;

class UWidgetSwitcher;

class UProgressBar;

class USearchBox;

class UReplacementItem;

class UDSCupboardModel;

struct FMultiComponentDataItem;

DECLARE_DELEGATE_OneParam(FReplaceItemClickedDelegate, const FDSResourceInfo&);

/**
 * 
 */
UCLASS()
class DESIGNSTATION_API UReplacementDialog : public UUserWidget
{
	GENERATED_BODY()

	DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnReplaceMaterialDelegate, const FParameterData&, ParameterData, const FString&, ParameterUUID, const FString&, MaterialFolderId);

public:
	UReplacementDialog(const FObjectInitializer& Initializer);

	virtual void NativeOnInitialized() override;

	void SetEmployer(UDSCupboardModel* InEmployer, const FString& GlType, const FString& FolderId, const int32& Type, const FParameterData& InParam, const FRefToLocalFileData& LocalFileData,
	                 bool bInShouldRecord = false);

	UFUNCTION(BlueprintCallable)
	void QueryResourceList(const FQueryResourceListParams& InParams);

	UFUNCTION(BlueprintPure)
	FQueryResourceListParams CollectQueryResourceListParams() const;

	UFUNCTION(BlueprintPure)
	bool IsReplacedFromStyle() const;

protected:
	void QueryResourceListFromStyle();

	//Query of the Parent Category ID for the Style  
	UFUNCTION()
	void QueryParentCategoryTreeFolderId();

	//the call back of QueryParentCategoryTreeFolderId() 
	void OnQueryParentCategoryTreeFolderIdComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully);

	//Query related items based on the parent's folder ID
	void QueryRelativeListByFolderId(const FString& GlType, const FString& FolderId, const int32& Type);

	//the call back of QueryRelativeListByFolderId() 
	void OnQueryRelativeListByFolderIdComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully);

	void RefreshResourceList(const TArray<FDSResourceInfo>& ItemList);

	UFUNCTION()
	void OnQueryRelativeCategoryTreeComplete(const TArray<FDSCategoryInfo>& InCategories);

	UFUNCTION()
	void OnQueryResourceListByFolderIdsComplete(const TArray<FDSResourceInfo>& ItemList);

	UFUNCTION()
	void OnQueryResourceListComplete(const FIntPoint& InPageInfo, const TArray<FDSResourceInfo>& ItemList);

	void OnCategoryTreeSelectionChanged(TSharedPtr<FDSCategoryInfo> InCategory, ESelectInfo::Type InType);

	UFUNCTION()
	void OnReplacementItemClicked(int32 ButtonId);

	UFUNCTION()
	void OnSearchBoxTextCommitted(const FText& InText, ETextCommit::Type InType);

	UFUNCTION()
	void OnApplyButtonClicked();

	UFUNCTION()
	void OnCloseDownloadButtonClicked();

	void OnRequestResourceFileHeadComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully);

	void OnDownloadResourceFileProgress(FHttpRequestPtr Request, uint64 BytesSent, uint64 BytesReceived);

	void OnDownloadResourceFileCompleted(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully);

	void ReplaceEmployerComponent();

	FSimpleDelegate QueryRelativeListByFolderIdNoResult;

public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "ReplacementDialog | Classes")
	TSubclassOf<UReplacementItem> ResourceItemClass;

	UPROPERTY(BlueprintAssignable, Category = "ReplacementDialog | Event")
	FOnReplaceMaterialDelegate OnReplaceMaterial;

	FReplaceItemClickedDelegate ReplaceItemClickedDelegate;

protected:
	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UWidgetSwitcher* DownloadSwitcher;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UCategoryTree* CategoryTree;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UButton* ApplyButton;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	USearchBox* SearchBox;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UWrapBox* ResourceContainer;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UButton* CloseDownloadButton;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UImage* DownloadVisualImage;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UProgressBar* DownloadProgressBar;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UTextBlock* DownloadStatusTextBlock;

	UPROPERTY(BlueprintReadOnly)
	UReplacementItem* SelectedItem;

	UPROPERTY(BlueprintReadOnly)
	FParameterData MaterialParameter;

	UPROPERTY(BlueprintReadOnly)
	FString ParameterUUID;

	UPROPERTY(BlueprintReadOnly)
	bool bReplaceMaterial;

	UPROPERTY(BlueprintReadOnly)
	bool bUseStyleResourceList;

	UPROPERTY(BlueprintReadOnly)
	UDSCupboardModel* Employer;

	UPROPERTY(BlueprintReadOnly)
	TArray<FDSResourceInfo> ResourceItemInfos;

	UPROPERTY(BlueprintReadOnly)
	FString ResourceFilePath;

	UPROPERTY(BlueprintReadOnly)
	int32 ResourceFileBytes;

	FHttpRequestPtr HttpRequest;

	UPROPERTY(BlueprintReadOnly)
	FString StyleType;

	UPROPERTY(BlueprintReadOnly)
	FString CurGlType;

	UPROPERTY(BlueprintReadOnly)
	FParameterData CurParameterData;

	UPROPERTY(BlueprintReadOnly)
	FRefToLocalFileData CurRefToLocalFileData;

	UPROPERTY(BlueprintReadOnly)
	bool bShouldRecordReplacement;
};
