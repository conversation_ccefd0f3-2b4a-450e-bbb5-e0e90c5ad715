// Fill out your copyright notice in the Description page of Project Settings.

#include "ReplacementDialog.h"

#include "FileHelper.h"
#include "Http.h"
#include "ReplacementItem.h"
#include "Components/Button.h"
#include "Components/Image.h"
#include "Components/ProgressBar.h"
#include "Components/TextBlock.h"
#include "Components/WidgetSwitcher.h"
#include "Components/WrapBox.h"
#include "Engine/GameInstance.h"
#include "Subsystems/DSNetworkSubsystem.h"
#include "SubSystems/NetworkConfig.h"
#include "Subsystems/File/Core/ProtoDataConvert/ProtobufOperatorFunctionLibrary.h"
#include "Subsystems/MVC/DSMVCSubsystem.h"
#include "Subsystems/MVC/Model/Custom/DSCupboardModel.h"
#include "Subsystems/MVC/Model/Custom/Library/DSCupboardLibrary.h"
#include "Subsystems/Resource/DSResourceSubsystem.h"
#include "Subsystems/UI/Widget/Common/CategoryTree/CategoryTree.h"
#include "Subsystems/UI/Widget/Common/ResourceItem/Structures/QueryResourceListParams.h"
#include "Subsystems/UI/Widget/Components/SearchBox/SearchBox.h"
#include "SubSystems/Undo/DSRevokeSubsystem.h"

DECLARE_LOG_CATEGORY_CLASS(LogReplacementDialog, Log, All);

UReplacementDialog::UReplacementDialog(const FObjectInitializer& Initializer)
	: Super(Initializer),
	  DownloadSwitcher(nullptr),
	  CategoryTree(nullptr),
	  ApplyButton(nullptr),
	  SearchBox(nullptr),
	  ResourceContainer(nullptr),
	  CloseDownloadButton(nullptr),
	  DownloadVisualImage(nullptr),
	  DownloadProgressBar(nullptr),
	  DownloadStatusTextBlock(nullptr),
	  SelectedItem(nullptr),
	  Employer(nullptr),
	  ResourceFileBytes(0)
{
	bReplaceMaterial = false;
	bUseStyleResourceList = false;
	bShouldRecordReplacement = false;
}

void UReplacementDialog::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	SearchBox->OnSearchTextCommitted.AddDynamic(this, &ThisClass::OnSearchBoxTextCommitted);
	CategoryTree->OnSelectionChangedDelegate().BindUObject(this, &ThisClass::OnCategoryTreeSelectionChanged);
	ApplyButton->OnClicked.AddDynamic(this, &ThisClass::OnApplyButtonClicked);

	CloseDownloadButton->OnClicked.AddDynamic(this, &ThisClass::OnCloseDownloadButtonClicked);
}

void UReplacementDialog::SetEmployer(UDSCupboardModel* InEmployer, const FString& GlType, const FString& FolderId, const int32& Type, const FParameterData& InParam,
                                     const FRefToLocalFileData& LocalFileData, bool bInShouldRecord)
{
	DownloadSwitcher->SetActiveWidgetIndex(0);
	ResourceContainer->ClearChildren();

	CurGlType = GlType;
	CurParameterData = InParam;
	CurRefToLocalFileData = LocalFileData;

	Employer = InEmployer;
	bShouldRecordReplacement = false;
	if (bInShouldRecord)
	{
		UDSCupboardModel* RootModel = Employer->GetRootCupboardModel();
		if (UDSCupboardLibrary::CanCupboardApplyStyle(RootModel) && UDSCupboardLibrary::CanCupboardApplyStyle(Employer)) {}
	}

	if (GlType == TEXT("GLCZ"))
	{
		bReplaceMaterial = true;
		MaterialParameter = InParam;
	}
	else
	{
		bReplaceMaterial = false;
	}

	bUseStyleResourceList = false;

	QueryRelativeListByFolderIdNoResult.BindUFunction(this, FName(TEXT("QueryParentCategoryTreeFolderId"))); //用风格查类目树
	QueryRelativeListByFolderId(GlType, FolderId, Type); //用父级查关联类目树，FolderId是父级的ID
}

void UReplacementDialog::QueryResourceList(const FQueryResourceListParams& InParams)
{
	SelectedItem = nullptr;
	ResourceContainer->ClearChildren();

	if (!UDSNetworkSubsystem::IsInitialized())
	{
		return;
	}

	FOnQueryResourceListCompletedDelegate CallBack;
	CallBack.BindDynamic(this, &ThisClass::OnQueryResourceListComplete);
	UDSNetworkSubsystem::GetInstance()->SendQueryResourceListRequest(InParams, CallBack);
}

FQueryResourceListParams UReplacementDialog::CollectQueryResourceListParams() const
{
	FQueryResourceListParams Params;

	if (!UDSMVCSubsystem::IsInitialized())
	{
		return Params;
	}

	Params.SearchText = SearchBox->GetText().ToString();
	Params.PageSize = 1000;

	TSharedPtr<FDSCategoryInfo> SelectedCategory = CategoryTree->GetSelectedCategory();
	if (!SelectedCategory)
	{
		return Params;
	}

	Params.Type = SelectedCategory->Type;
	Params.CategoryId = SelectedCategory->CategoryId;

	return Params;
}

bool UReplacementDialog::IsReplacedFromStyle() const
{
	return bUseStyleResourceList;
}

void UReplacementDialog::QueryResourceListFromStyle()
{
	const FApplyStyleData& AppliedStyle = Employer->GetModelInfoRef().ApplyStyle_Whole;
	if (!AppliedStyle.IsValid())
	{
		return;
	}

	TMap<FString, EDSResourceType> ResourceFolderIds;
	for (const FRefToOptionData& OptionData : AppliedStyle.ApplySelectOptionData)
	{
		int32 ParamPos = INDEX_NONE;

		if (bReplaceMaterial)
		{
			ParamPos = OptionData.option_params.IndexOfByPredicate(
				[&](const FParameterData& InParam)
				{
					return InParam.Data.param_id == MaterialParameter.Data.param_id;
				});
		}
		else
		{
			// Collect resource folder id for replace model. 
		}

		if (ParamPos == INDEX_NONE)
		{
			continue;
		}

		ResourceFolderIds.Add(OptionData.option_params[ParamPos].Data.value, EDSResourceType::Material);
	}

	FOnQueryResourceListByFolderIdsCompletedDelegate Callback;
	Callback.BindDynamic(this, &ThisClass::OnQueryResourceListByFolderIdsComplete);
	UDSNetworkSubsystem::GetInstance()->SendQueryResourceListByFolderIdsRequest(ResourceFolderIds, Callback);
}

void UReplacementDialog::QueryParentCategoryTreeFolderId()
{
	FString FolderId; //风格内自身ID
	if (CurRefToLocalFileData.IsValid())
	{
		FolderId = CurRefToLocalFileData.FolderDBData.folder_id;
	}
	else if (CurParameterData.IsValid())
	{
		FolderId = CurParameterData.Data.value;
	}

	FString QueryParamsStr;
	TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter = TJsonWriterFactory<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>::Create(&QueryParamsStr);
	JsonWriter->WriteObjectStart();
	{
		JsonWriter->WriteValue(TEXT("folderId"), FolderId);
		JsonWriter->WriteValue(TEXT("type"), CurGlType == TEXT("GLCZ") ? 2 : 0); //"0定制 1成品模型 2材质"
	}
	JsonWriter->WriteObjectEnd();
	JsonWriter->Close();

	const FString URL = FString::Printf(TEXT("%s/bk-design/api/store/getCateBkId"), SERVER_URL);

	UDSNetworkSubsystem* NetworkSubsystem = GetWorld()->GetGameInstance()->GetSubsystem<UDSNetworkSubsystem>();
	if (NetworkSubsystem == nullptr)
	{
		return;
	}
	FHttpRequestPtr HttpRequestHandle = NetworkSubsystem->CreatePostRequest(URL, QueryParamsStr);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnQueryParentCategoryTreeFolderIdComplete);
	HttpRequestHandle->ProcessRequest();
}

void UReplacementDialog::OnQueryParentCategoryTreeFolderIdComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully)
{
	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		UE_LOG(LogTemp, Error, TEXT("Query folder resource informations failure."));
		return;
	}

	TSharedPtr<FJsonObject> ResponseObj;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response->GetContentAsString());

	if (!FJsonSerializer::Deserialize(Reader, ResponseObj))
	{
		UE_LOG(LogTemp, Error, TEXT("Parse response json failure."));
		return;
	}

	if (!ResponseObj->GetBoolField(TEXT("success")))
	{
		UE_LOG(LogTemp, Error, TEXT("%s : The success field is false, msg is '%s'."), __FUNCTIONW__, *ResponseObj->GetStringField(TEXT("msg")));
		return;
	}

	if (!ResponseObj->HasTypedField<EJson::Array>(TEXT("resp")))
	{
		UE_LOG(LogTemp, Error, TEXT("%s : Not found 'resp' field."), __FUNCTIONW__);
		return;
	}

	TArray<TSharedPtr<FDSCategoryInfo>> CategoryTree;

	const TArray<TSharedPtr<FJsonValue>>& RespValues = ResponseObj->GetArrayField(TEXT("resp"));
	for (const TSharedPtr<FJsonValue>& RespValue : RespValues)
	{
		const TSharedPtr<FJsonObject>& RespObj = RespValue->AsObject();
		if (!RespObj)
		{
			continue;
		}

		TSharedPtr<FDSCategoryInfo>& NewCategory = CategoryTree.AddDefaulted_GetRef();
		NewCategory = MakeShared<FDSCategoryInfo>();
		FDSCategoryInfo::DeserializeFromJson(NewCategory, RespObj);
	}

	TArray<FDSCategoryInfo> CategoriesForDelegate;
	for (const TSharedPtr<FDSCategoryInfo>& CategoryPtr : CategoryTree)
	{
		if (CategoryPtr.IsValid())
		{
			CategoriesForDelegate.Add(*CategoryPtr);
		}
	}

	OnQueryRelativeCategoryTreeComplete(CategoriesForDelegate);
}

void UReplacementDialog::QueryRelativeListByFolderId(const FString& GlType, const FString& FolderId, const int32& Type)
{
	TArray<FString> Code;
	FString QueryParamsStr;

	Code.Add(GlType);

	TSharedRef<TJsonWriter<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>> JsonWriter = TJsonWriterFactory<TCHAR, TCondensedJsonPrintPolicy<TCHAR>>::Create(&QueryParamsStr);
	JsonWriter->WriteObjectStart();
	{
		JsonWriter->WriteValue(TEXT("bkId"), TEXT(""));
		JsonWriter->WriteValue(TEXT("folderId"), FolderId);
		JsonWriter->WriteValue(TEXT("id"), TEXT(""));
		JsonWriter->WriteValue(TEXT("type"), Type);
		JsonWriter->WriteArrayStart(TEXT("code"));
		{
			for (auto& String : Code)
			{
				JsonWriter->WriteValue(String);
			}
		}
		JsonWriter->WriteArrayEnd();
	}
	JsonWriter->WriteObjectEnd();
	JsonWriter->Close();

	const FString URL = FString::Printf(TEXT("%s/bk-design/api/store/getByIdType"), SERVER_URL);

	UDSNetworkSubsystem* NetworkSubsystem = GetWorld()->GetGameInstance()->GetSubsystem<UDSNetworkSubsystem>();
	if (NetworkSubsystem == nullptr)
	{
		return;
	}
	FHttpRequestPtr HttpRequestHandle = NetworkSubsystem->CreatePostRequest(URL, QueryParamsStr);
	HttpRequestHandle->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnQueryRelativeListByFolderIdComplete);
	HttpRequestHandle->ProcessRequest();
}

void UReplacementDialog::OnQueryRelativeListByFolderIdComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully)
{
	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		UE_LOG(LogTemp, Error, TEXT("Query folder resource informations failure."));
		return;
	}

	TSharedPtr<FJsonObject> ResponseObj;
	TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(Response->GetContentAsString());

	if (!FJsonSerializer::Deserialize(Reader, ResponseObj))
	{
		UE_LOG(LogTemp, Error, TEXT("Parse response json failure."));
		return;
	}

	if (!ResponseObj->GetBoolField(TEXT("success")))
	{
		QueryRelativeListByFolderIdNoResult.Execute(); //查不到就用风格继续查
		UE_LOG(LogTemp, Error, TEXT("%s : The success field is false, msg is '%s'."), __FUNCTIONW__, *ResponseObj->GetStringField(TEXT("msg")));
		return;
	}

	if (!ResponseObj->HasTypedField<EJson::Array>(TEXT("resp")))
	{
		UE_LOG(LogTemp, Error, TEXT("%s : Not found 'resp' field."), __FUNCTIONW__);
		return;
	}

	TArray<TSharedPtr<FDSCategoryInfo>> CategoryTree;

	const TArray<TSharedPtr<FJsonValue>>& RespValues = ResponseObj->GetArrayField(TEXT("resp"));
	for (const TSharedPtr<FJsonValue>& RespValue : RespValues)
	{
		const TSharedPtr<FJsonObject>& RespObj = RespValue->AsObject();
		if (!RespObj)
		{
			continue;
		}

		TSharedPtr<FDSCategoryInfo>& NewCategory = CategoryTree.AddDefaulted_GetRef();
		NewCategory = MakeShared<FDSCategoryInfo>();
		FDSCategoryInfo::DeserializeFromJson(NewCategory, RespObj);
	}

	TArray<FDSCategoryInfo> CategoriesForDelegate;
	for (const TSharedPtr<FDSCategoryInfo>& CategoryPtr : CategoryTree)
	{
		if (CategoryPtr.IsValid())
		{
			CategoriesForDelegate.Add(*CategoryPtr);
		}
	}

	OnQueryRelativeCategoryTreeComplete(CategoriesForDelegate);
}

void UReplacementDialog::RefreshResourceList(const TArray<FDSResourceInfo>& ItemList)
{
	if (!ResourceItemClass)
	{
		return;
	}

	ResourceItemInfos = ItemList;

	FString IdInUse;

	bool bHasDefaultSelected = false;
	if (!IdInUse.IsEmpty())
	{
		// Find if result has anyone should be selected.
		int32 SelectedInfoPos = ResourceItemInfos.IndexOfByPredicate(
			[&](const FDSResourceInfo& InInfo)
			{
				return InInfo.FolderId == IdInUse;
			});

		// Move the selected one to front.
		if (SelectedInfoPos != INDEX_NONE)
		{
			FDSResourceInfo TempInfo = ResourceItemInfos[SelectedInfoPos];
			ResourceItemInfos.RemoveAt(SelectedInfoPos);
			ResourceItemInfos.Insert(TempInfo, 0);

			bHasDefaultSelected = true;
		}
	}

	//会触发几次接口，前面的清理，不会在回调中清理
	ResourceContainer->ClearChildren();
	for (int32 Index = 0; Index < ResourceItemInfos.Num(); ++Index)
	{
		FString ProductImage = ResourceItemInfos[Index].ProductImg;
		UReplacementItem* NewItem = CreateWidget<UReplacementItem>(this, ResourceItemClass);
		NewItem->ButtonId = Index;
		NewItem->ButtonName = FText::FromString(ResourceItemInfos[Index].Name);
		NewItem->ResourceInfo = ResourceItemInfos[Index];
		NewItem->DownloadVisualBrushFromUrl(ProductImage);
		NewItem->OnClicked.AddDynamic(this, &ThisClass::OnReplacementItemClicked);

		if (bHasDefaultSelected && Index == 0)
		{
			NewItem->SetActivate(true);
			SelectedItem = NewItem;
		}

		ResourceContainer->AddChildToWrapBox(NewItem);
	}
}

void UReplacementDialog::OnQueryRelativeCategoryTreeComplete(const TArray<FDSCategoryInfo>& InCategories)
{
	TArray<TSharedPtr<FDSCategoryInfo>> OutSharedPtrCategories;
	for (const FDSCategoryInfo& Category : InCategories)
	{
		FDSCategoryInfo* NewCategory = new FDSCategoryInfo(Category);
		TSharedPtr<FDSCategoryInfo> CategoryPtr = MakeShareable(NewCategory);
		OutSharedPtrCategories.Add(CategoryPtr);
	}

	CategoryTree->SetCategoryTree(OutSharedPtrCategories);

	if (InCategories.IsEmpty())
	{
		bUseStyleResourceList = true;
		QueryRelativeListByFolderIdNoResult.Execute(); //查不到就用风格继续查
	}
	else
	{
		CategoryTree->SetCategorySelected(OutSharedPtrCategories[0]);
	}
}

void UReplacementDialog::OnQueryResourceListByFolderIdsComplete(const TArray<FDSResourceInfo>& ItemList)
{
	RefreshResourceList(ItemList);
}

void UReplacementDialog::OnQueryResourceListComplete(const FIntPoint& InPageInfo, const TArray<FDSResourceInfo>& ItemList)
{
	RefreshResourceList(ItemList);
}

void UReplacementDialog::OnCategoryTreeSelectionChanged(TSharedPtr<FDSCategoryInfo> InCategory, ESelectInfo::Type InType)
{
	QueryResourceList(CollectQueryResourceListParams());
}

void UReplacementDialog::OnReplacementItemClicked(int32 ButtonId)
{
	TArray<UWidget*> AllChildren = ResourceContainer->GetAllChildren();
	for (UWidget* Child : AllChildren)
	{
		UReplacementItem* Item = Cast<UReplacementItem>(Child);
		if (Item == nullptr || Item->ButtonId != ButtonId)
		{
			continue;
		}
		SelectedItem = Item;
	}
}

void UReplacementDialog::OnSearchBoxTextCommitted(const FText& InText, ETextCommit::Type InType)
{
	if (!bUseStyleResourceList)
	{
		QueryResourceList(CollectQueryResourceListParams());
	}
}

void UReplacementDialog::OnApplyButtonClicked()
{
	if (SelectedItem == nullptr)
	{
		return;
	}

	if (!ResourceItemInfos.IsValidIndex(SelectedItem->ButtonId))
	{
		return;
	}

	if (bReplaceMaterial && (ResourceItemInfos[SelectedItem->ButtonId].Type != EDSResourceType::Material))
	{
		// TODO: Need to display a toast to tell user the resource type invalid.
		return;
	}

	if (UDSResourceSubsystem::IsInitialized())
	{
		UDSResourceSubsystem::GetInstance()->AddResourceCache(ResourceItemInfos[SelectedItem->ButtonId]);
	}

	FDSResourceFile ResourceFileInfo = ResourceItemInfos[SelectedItem->ButtonId].GetResourceFile(EDSResourceQuality::Low);

	DownloadStatusTextBlock->SetText(FText::FromString(TEXT("正在校验本地文件...")));
	DownloadSwitcher->SetActiveWidgetIndex(1);

	ResourceFilePath = FPaths::Combine(FPaths::ProjectSavedDir(), TEXT("Resources"), ResourceFileInfo.ComputeLocalFileName()/* + FPaths::GetExtension(ResourceFileInfo.FilePath, true)*/);
	if (IFileManager::Get().FileExists(*ResourceFilePath))
	{
		TArray<uint8> ResourceContent;
		if (FFileHelper::LoadFileToArray(ResourceContent, *ResourceFilePath))
		{
			if (ResourceFileInfo.MD5 == FMD5::HashBytes(ResourceContent.GetData(), ResourceContent.Num()))
			{
				ReplaceEmployerComponent();
				DownloadProgressBar->SetPercent(1.0f);
				DownloadStatusTextBlock->SetText(FText::FromString(TEXT("替换完成...")));
				if (ReplaceItemClickedDelegate.IsBound())
				{
					ReplaceItemClickedDelegate.Execute(SelectedItem->ResourceInfo);
				}
				OnCloseDownloadButtonClicked();
				return;
			}
		}
	}

	DownloadStatusTextBlock->SetText(FText::FromString(TEXT("正在下载...")));

	HttpRequest = FHttpModule::Get().CreateRequest();
	if (ResourceItemInfos[SelectedItem->ButtonId].GetResourceFile(EDSResourceQuality::Low).FilePath.StartsWith(TEXT("http://")) ||
		ResourceItemInfos[SelectedItem->ButtonId].GetResourceFile(EDSResourceQuality::Low).FilePath.StartsWith(TEXT("https://")))
	{
		HttpRequest->SetURL(ResourceItemInfos[SelectedItem->ButtonId].GetResourceFile(EDSResourceQuality::Low).FilePath);
	}
	else
	{
		HttpRequest->SetURL(ResourceItemInfos[SelectedItem->ButtonId].GetResourceFile(EDSResourceQuality::Low).FilePath);
	}
	HttpRequest->SetVerb(TEXT("HEAD"));
	HttpRequest->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnRequestResourceFileHeadComplete);
	HttpRequest->ProcessRequest();
}

void UReplacementDialog::OnCloseDownloadButtonClicked()
{
	if (HttpRequest && HttpRequest->GetStatus() != EHttpRequestStatus::Processing)
	{
		HttpRequest->OnProcessRequestComplete().Unbind();
		HttpRequest->CancelRequest();
	}

	DownloadSwitcher->SetActiveWidgetIndex(0);
	RemoveFromParent();
}

void UReplacementDialog::OnRequestResourceFileHeadComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully)
{
	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		UE_LOG(LogReplacementDialog, Warning, TEXT("%s - Request size of resource file failure."), __FUNCTIONW__);
		return;
	}

	//ResourceFileBytes = FCString::Atoi(*Request->GetHeader(TEXT("Content-Length")));
	ResourceFileBytes = Response->GetContentLength();

	HttpRequest = FHttpModule::Get().CreateRequest();

	HttpRequest->SetURL(Request->GetURL());
	HttpRequest->SetVerb(TEXT("GET"));
	HttpRequest->OnRequestProgress64().BindUObject(this, &ThisClass::OnDownloadResourceFileProgress);
	HttpRequest->OnProcessRequestComplete().BindUObject(this, &ThisClass::OnDownloadResourceFileCompleted);

	HttpRequest->ProcessRequest();
}

void UReplacementDialog::OnDownloadResourceFileProgress(FHttpRequestPtr Request, uint64 BytesSent, uint64 BytesReceived)
{
	float DownloadPercent = BytesReceived / ResourceFileBytes * 0.9;

	DownloadProgressBar->SetPercent(DownloadPercent);
}

void UReplacementDialog::OnDownloadResourceFileCompleted(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bConnectedSuccessfully)
{
	if (!bConnectedSuccessfully || !EHttpResponseCodes::IsOk(Response->GetResponseCode()))
	{
		UE_LOG(LogReplacementDialog, Warning, TEXT("%s - Request resource file failure."), __FUNCTIONW__);
		return;
	}

	FDSResourceFile ResourceFileInfo = ResourceItemInfos[SelectedItem->ButtonId].GetResourceFile(EDSResourceQuality::Low);

	DownloadStatusTextBlock->SetText(FText::FromString(TEXT("正在校验资源MD5...")));

	FString ResourceFileMD5 = ResourceFileInfo.MD5;
	FString ResourceContentMD5 = FMD5::HashBytes(Response->GetContent().GetData(), Response->GetContent().Num());

	if (ResourceFileMD5 != ResourceContentMD5)
	{
		DownloadStatusTextBlock->SetText(FText::FromString(TEXT("资源文件MD5校验不通过！")));
		UE_LOG(LogReplacementDialog, Warning, TEXT("%s - Verify md5 of the downloaded resource file with resource information failure."), __FUNCTIONW__);
		return;
	}

	if (!FFileHelper::SaveArrayToFile(Response->GetContent(), *ResourceFilePath))
	{
		DownloadStatusTextBlock->SetText(FText::FromString(TEXT("保存文件失败...")));
		UE_LOG(LogReplacementDialog, Warning, TEXT("%s - Save the resource file data on disk failure."), __FUNCTIONW__);
		return;
	}

	DownloadProgressBar->SetPercent(0.95);
	DownloadStatusTextBlock->SetText(FText::FromString(TEXT("正在进行替换...")));

	ReplaceEmployerComponent();

	DownloadProgressBar->SetPercent(1.0f);
	DownloadStatusTextBlock->SetText(FText::FromString(TEXT("替换完成...")));

	if (ReplaceItemClickedDelegate.IsBound())
	{
		ReplaceItemClickedDelegate.Execute(SelectedItem->ResourceInfo);
	}
	OnCloseDownloadButtonClicked();
}

void UReplacementDialog::ReplaceEmployerComponent()
{
	if (Employer == nullptr)
	{
		//Employer is nullptr, means the dialog is only select resource, not replace component.
		return;
	}

	switch (ResourceItemInfos[SelectedItem->ButtonId].Type)
	{
	case EDSResourceType::Material:
		{
			if (!bReplaceMaterial)
			{
				UE_LOG(LogReplacementDialog, Error, TEXT("%s - Material resource used for model type component."), __FUNCTIONW__);
				return;
			}

			//revoke
			TSharedPtr<FDSCustomComponentRevokeData> RevokeComponentData = MakeShared<FDSCustomComponentRevokeData>();
			RevokeComponentData->SetComponentData(Employer->GetModelInfoRef().ComponentTreeData, true);
			RevokeComponentData->SetComponentCommandType(EDSRevokeComponentType::E_Component_MatReplace);
			RevokeComponentData->SetNodePath(TEXT(""));

			FDSRevokePushData PushData(EDSPushDataType::E_Custom, FDSModelExecuteType::ExecuteUpdateSelf, false);
			PushData.SetData(FDSCustomPushData(FDSModelExecuteType::ExecuteUpdateSelf, Employer, RevokeComponentData));
			UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(
				Employer,
				FDSModelExecuteType::ExecuteUpdateSelf, PushData, UDSMVCSubsystem::GetInstance()->GetRevokeMark(), TEXT(""));

			FParameterData* Param_HandlePos = Employer->GetModelInfoRef().ComponentTreeData->ComponentParameters.FindByPredicate([this](const FParameterData& InParam)
			{
				return CurParameterData.Data.name == InParam.Data.name;
			});

			bool bAddAction = false;
			if (Param_HandlePos == nullptr)
			{
				bAddAction = true;
				int32 NewItemPos = Employer->GetModelInfoRef().ComponentTreeData->ComponentParameters.Add(CurParameterData);
				Param_HandlePos = &Employer->GetModelInfoRef().ComponentTreeData->ComponentParameters[NewItemPos];
			}

			if (bShouldRecordReplacement)
			{
				UDSCupboardModel* RootModel = Employer->GetRootCupboardModel();
				RootModel->PushNodeRecord(Employer->GetModelInfo().ComponentTreeData->UUID, *Param_HandlePos, bAddAction);
			}

			Param_HandlePos->Data.value = ResourceItemInfos[SelectedItem->ButtonId].FolderId;
			Param_HandlePos->Data.expression = ResourceItemInfos[SelectedItem->ButtonId].FolderId;

			Employer->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
		}
		break;
	case EDSResourceType::Model:
		{
			if (!UDSResourceSubsystem::IsInitialized())
			{
				UE_LOG(LogReplacementDialog, Error, TEXT("%s - Not find valid resource subsystem."), __FUNCTIONW__);
				return;
			}

			//revoke
			TSharedPtr<FDSCustomComponentRevokeData> NewRevokeData = MakeShared<FDSCustomComponentRevokeData>();
			NewRevokeData->SetComponentData(Employer->GetComponentTreeDataRef(), true);
			NewRevokeData->SetComponentCommandType(EDSRevokeComponentType::E_Component_ModelReplace);
			NewRevokeData->SetNodePath(TEXT(""));

			FDSRevokePushData PushData(EDSPushDataType::E_Custom, FDSModelExecuteType::ExecuteUpdateSelf, false);
			PushData.SetData(FDSCustomPushData(FDSModelExecuteType::ExecuteUpdateSelf, Employer, NewRevokeData));
			UDSRevokeSubsystem::GetInstance()->InsertCommandDataUnion(
				Employer,
				FDSModelExecuteType::ExecuteUpdateSelf, PushData, UDSMVCSubsystem::GetInstance()->GetRevokeMark(), TEXT(""));

			Employer->GetComponentTreeDataRef()->ChildComponent.Empty();
			Employer->GetComponentTreeDataRef()->SingleComponentData.ComponentItems.Reserve(1);
			Employer->GetComponentTreeDataRef()->ComponentType = ECompType::ImportModel;

			FSingleComponentItem ReplacementSection;
			ReplacementSection.ComponentSource = ESingleComponentSource::EImportPAK;
			ReplacementSection.PakRefPath = UDSResourceSubsystem::GetInstance()->ComputeModelSoftPathByPakPath(ResourceFilePath, UDSResourceSubsystem::GetDefaultModelAssetFileName());
			ReplacementSection.PakRelativeFilePath = ResourceFilePath;
			FPaths::MakePathRelativeTo(ReplacementSection.PakRelativeFilePath, *FPaths::ProjectSavedDir());

			Employer->GetComponentTreeDataRef()->SingleComponentData.ComponentItems[0] = ReplacementSection;

			UDSCupboardModel* RootModel = Employer->GetRootCupboardModel();
			RootModel->MarkNodeSource(Employer->GetModelInfo().ComponentTreeData->UUID, !bUseStyleResourceList);

			Employer->OnExecuteAction(FDSModelExecuteType::ExecuteUpdateSelf);
		}
		break;
	case EDSResourceType::Custom:
		{
			FRefToLocalFileData ResourceFileData;
			if (!FProtobufOperatorFunctionLibrary::LoadRelationFromFile(ResourceFilePath, ResourceFileData))
			{
				UE_LOG(LogReplacementDialog, Error, TEXT("%s - Failed to load relation from file '%s'."), __FUNCTIONW__, *ResourceFilePath);
				return;
			}

			UDSCupboardModel* RootModel = Employer->GetRootCupboardModel();
			RootModel->MarkNodeSource(Employer->GetModelInfo().ComponentTreeData->UUID, !bUseStyleResourceList);

			Employer->ReplaceComponent(ResourceFileData);
		}
		break;
	default:
		break;
	}
}
