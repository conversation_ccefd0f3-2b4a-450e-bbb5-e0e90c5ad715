// Fill out your copyright notice in the Description page of Project Settings.

#include "GeometryDatas.h"
#include "LocalCache/Libraries/GeometryRelativeLibrary.h"
#include "LocalCache/Parameter/ParameterProcLibrary.h"
#include "LocalCache/Component/ParameterPropertyData.h"
#include "LocalCache/Libraries/GeomtryMathmaticLibrary.h"
#include "Runtime/Core/Public/Misc/Paths.h"
#include "BasicClasses/DesignStationController.h"
#include "LocalCache/Parameter/ParameterRefrenceParser.h"
#include "LocalCache/Component/ComponentDBOperatorLibrary.h"
#include "LocalCache/FolderData/FolderDataLibrary.h"
#include "SubSystems/File/Core/ProtoDataConvert/ProtobufOperatorFunctionLibrary.h"
#include "SubSystems/NetworkData/FolderInformationData.h"


#ifdef WITH_EDITOR
#pragma optimize("", off)
#endif

extern const FString PARAM_FDZ_STR;
extern const FString PARAM_GDFD_STR;
extern const FString PLATE_FLAG_STR;

extern const FString PARAM_KMLB_STR;

TArray<FParameterData> FGeometryDatas::ConvertParamsMapToArray(const TMap<FString, FParameterData>& ParamsMap)
{
	TArray<FParameterData> OutRes;
	ParamsMap.GenerateValueArray(OutRes);
	return OutRes;
}

TMap<FString, FParameterData> FGeometryDatas::ConvertParamsArrayToMap(const TArray<FParameterData>& ParamsArray)
{
	TMap<FString, FParameterData> OutRes;
	for (auto& PA : ParamsArray)
	{
		OutRes.Add(PA.Data.name, PA);
	}
	return OutRes;
}

void FGeometryDatas::FormatLocation(const EPlanPolygonBelongs& InPlanType, FVector& InOutLocation)
{
	switch (InPlanType)
	{
	case EPlanPolygonBelongs::EXY_Plan: InOutLocation.Z = 0.0f;
		break;
	case EPlanPolygonBelongs::EXZ_Plan: InOutLocation.Y = 0.0f;
		break;
	case EPlanPolygonBelongs::EYZ_Plan: InOutLocation.X = 0.0f;
		break;
	case EPlanPolygonBelongs::EUnknown: break;
	default: checkNoEntry();
		break;
	}
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, FExpressionValuePair& InOutParameter)
{
	if (InOutParameter.Expression.IsEmpty())
	{
		InOutParameter.Value = TEXT("");
		return true;
	}

	FString ErrorMessage;
	FString FormatExpress;
	bool Res = FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, InParameters, InOutParameter.Expression, InOutParameter.Value, FormatExpress);
	if (Res)
	{
		InOutParameter.Value = UParameterPropertyData::FormatParameterValue(InOutParameter.Value).ToString();
	}
	return Res;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, FLocationProperty& InOutLocation)
{
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutLocation.LocationX))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutLocation.LocationY))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutLocation.LocationZ))
	{
		return false;
	}
	return true;
}


bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, FRotationProperty& InOutRotation)
{
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutRotation.Roll))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutRotation.Pitch))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutRotation.Yaw))
	{
		return false;
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, FScaleProperty& InOutScale)
{
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutScale.X))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutScale.Y))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutScale.Z))
	{
		return false;
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, FGeomtryPointProperty& InOutPoint)
{
	bool ResX = CalculateParameterValue(InGlobalParameter, InParameters, InOutPoint.LocationX);
	bool ResY = CalculateParameterValue(InGlobalParameter, InParameters, InOutPoint.LocationY);
	bool ResZ = CalculateParameterValue(InGlobalParameter, InParameters, InOutPoint.LocationZ);
	return ResX && ResY && ResZ;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, FGeomtryLineProperty& InOutLine)
{
	bool Res1 = CalculateParameterValue(InGlobalParameter, InParameters, InOutLine.RadiusOrHeightData);
	bool Res2 = CalculateParameterValue(InGlobalParameter, InParameters, InOutLine.InterpPointCountData);
	return Res1 && Res2;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, FGeomtryRectanglePlanProperty& InOutRectangle)
{
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutRectangle.StartLocationX))
	{
		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue rectanngle start location_x %s"), *InOutRectangle.StartLocationX.ToString());
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutRectangle.StartLocationY))
	{
		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue rectanngle start location_y %s"), *InOutRectangle.StartLocationY.ToString());
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutRectangle.StartLocationZ))
	{
		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue rectanngle start location_z %s"), *InOutRectangle.StartLocationZ.ToString());
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutRectangle.EndLocationX))
	{
		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue rectanngle end location_x %s"), *InOutRectangle.EndLocationX.ToString());
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutRectangle.EndLocationY))
	{
		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue rectanngle end location_y %s"), *InOutRectangle.EndLocationY.ToString());
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutRectangle.EndLocationZ))
	{
		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue rectanngle end location_z %s"), *InOutRectangle.EndLocationZ.ToString());
		return false;
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, FGeomtryEllipsePlanProperty& InOutEllipse)
{
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutEllipse.CenterLocationX))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutEllipse.CenterLocationY))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutEllipse.CenterLocationZ))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutEllipse.ShortRadiusData))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutEllipse.LongRadiusData))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutEllipse.InterpPointCountData))
	{
		return false;
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, FGeomtryCubeProperty& InOutCube)
{
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutCube.StartLocationX))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutCube.StartLocationY))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutCube.StartLocationZ))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutCube.EndLocationX))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutCube.EndLocationY))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutCube.EndLocationZ))
	{
		return false;
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, FSectionDrawOperation& InOutDrawData)
{
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutDrawData.DrawOffsetX))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutDrawData.DrawOffsetY))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutDrawData.DrawOffsetZ))
	{
		return false;
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, FSectionShiftingOperation& InOutShiftData)
{
	for (int32 i = 0; i < InOutShiftData.ShiftValue.Num(); ++i)
	{
		if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutShiftData.ShiftValue[i]))
		{
			return false;
		}
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, FSectionZoomOperation& InOutZoomData)
{
	for (int32 i = 0; i < InOutZoomData.ZoomValue.Num(); ++i)
	{
		if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutZoomData.ZoomValue[i]))
		{
			return false;
		}
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, FSectionLoftOperation& InOutLoftData)
{
	for (int32 i = 0; i < InOutLoftData.Points.Num(); ++i)
	{
		if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutLoftData.Points[i]))
		{
			return false;
		}
	}
	for (int32 i = 0; i < InOutLoftData.Lines.Num(); ++i)
	{
		if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutLoftData.Lines[i]))
		{
			return false;
		}
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, FSectionCutOutOperation& InOutCutoutData)
{
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutCutoutData.CutOutValue))
	{
		return false;
	}
	for (int32 i = 0; i < InOutCutoutData.Points.Num(); ++i)
	{
		if (CalculateParameterValue(InGlobalParameter, InParameters, InOutCutoutData.Points[i]))
		{
			return false;
		}
	}
	for (int32 i = 0; i < InOutCutoutData.Lines.Num(); ++i)
	{
		if (CalculateParameterValue(InGlobalParameter, InParameters, InOutCutoutData.Lines[i]))
		{
			return false;
		}
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutCutoutData.Rectangle))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutCutoutData.Ellipse))
	{
		return false;
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, FSectionOperation& InOutOperationData)
{
	for (int32 i = 0; i < InOutOperationData.DrawOperations.Num(); ++i)
	{
		if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutOperationData.DrawOperations[i]))
		{
			return false;
		}
	}
	for (int32 i = 0; i < InOutOperationData.ShiftOperations.Num(); ++i)
	{
		if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutOperationData.ShiftOperations[i]))
		{
			return false;
		}
	}
	for (int32 i = 0; i < InOutOperationData.ZoomOperations.Num(); ++i)
	{
		if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutOperationData.ZoomOperations[i]))
		{
			return false;
		}
	}
	for (int32 i = 0; i < InOutOperationData.CutoutOperations.Num(); ++i)
	{
		if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutOperationData.CutoutOperations[i]))
		{
			return false;
		}
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutOperationData.LoftingOperation))
	{
		return false;
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, FCrossSectionData& InOutCrossSectionData)
{
	for (int32 i = 0; i < InOutCrossSectionData.Points.Num(); ++i)
	{
		if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutCrossSectionData.Points[i]))
		{
			return false;
		}
	}
	for (int32 i = 0; i < InOutCrossSectionData.Lines.Num(); ++i)
	{
		if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutCrossSectionData.Lines[i]))
		{
			return false;
		}
		if (ELineType::ELineSegment != InOutCrossSectionData.Lines[i].LineType)
		{
			FVector PreLocation = InOutCrossSectionData.Points[(i - 1 + InOutCrossSectionData.Points.Num()) % InOutCrossSectionData.Points.Num()].PointLocation();
			InOutCrossSectionData.Lines[i].StartLocation = InOutCrossSectionData.Points[i].PointLocation(PreLocation);
			FVector NextLocation = InOutCrossSectionData.Points[(i + 1) % InOutCrossSectionData.Points.Num()].PointLocation(InOutCrossSectionData.Lines[i].StartLocation);
			InOutCrossSectionData.Lines[i].EndLocation = NextLocation;
		}
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutCrossSectionData.Rectangle))
	{
		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue section rectanngle"));
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutCrossSectionData.Ellipse))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutCrossSectionData.Cube))
	{
		return false;
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, FSingleComponentItem& InOutSingleComponentData)
{
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutSingleComponentData.OperatorSection))
	{
		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue single operation section"));
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutSingleComponentData.ComponentMaterial))
	{
		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue single component material %s"), *InOutSingleComponentData.ComponentMaterial.ToString());
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutSingleComponentData.VisibleParam))
	{
		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue single visible %s"), *InOutSingleComponentData.VisibleParam.ToString());
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutSingleComponentData.SectionOperation))
	{
		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue single section operation"));
		return false;
	}
	for (auto& Iter : InOutSingleComponentData.ImportMesh)
	{
		if (false == CalculateParameterValue(InGlobalParameter, InParameters, Iter.MaterialId))
		{
			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue single material id %s"), *Iter.MaterialId.ToString());
			return false;
		}
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutSingleComponentData.SingleComponentLocation))
	{
		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue single location"));
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutSingleComponentData.SingleComponentRotation))
	{
		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue single rotation "));
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutSingleComponentData.SingleComponentScale))
	{
		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue single scale "));
		return false;
	}
	return true;
}

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters,
                                             FSingleComponentProperty& InOutSingleComponentData)
{
	for (int32 i = 0; i < InOutSingleComponentData.ComponentItems.Num(); ++i)
	{
		if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutSingleComponentData.ComponentItems[i]))
		{
			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue single child i=%d"), i);
			return false;
		}
	}
	return true;
}

#define PARAM_DEPTH TEXT("D")
#define PARAM_WIDTH TEXT("W")
#define PARAM_HEIGHT TEXT("H")
#define PARAM_HEIGHT1 TEXT("H1")
#define PARAM_HEIGHT2 TEXT("H2")
#define PARAM_WIDTH1 TEXT("W1")
#define PARAM_WIDTH2 TEXT("W2")
//#define PARAM_KMLB TEXT("KMLB")
#define PARAM_LSAZ TEXT("LSAZ")

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters,
                                             FMultiComponentDataItem& InOutMultiComponentData)
{
	TMap<FString, FParameterData> ComponentParametersMap;
	FParameterProcLibrary::CombineParameters(ComponentParametersMap, InOutMultiComponentData.ComponentParameters);
	for (auto& Parameter : InOutMultiComponentData.StyleParameters)
	{
		if (ComponentParametersMap.Contains(Parameter.Data.name))
		{
			ComponentParametersMap[Parameter.Data.name] = Parameter;
		}
		else
		{
			ComponentParametersMap.Add(Parameter.Data.name, Parameter);
		}
	}
	TMap<FString, FParameterData> OverrideParameters;
	{
		//解析本级变量的引用关系，依据引用关系重新计算各变量的值
		TArray<FParameterData> PeerParameters;
		ComponentParametersMap.GenerateValueArray(PeerParameters);
		FParameterRefrenceParser Parser;
		bool Res = Parser.SortParameterByRefrence(PeerParameters);
		auto SortedParameters = Res ? Parser.GetSortedParameters() : Parser.GetOriginalParameters();
		for (int32 i = 0; i < SortedParameters.Num(); ++i)
		{
			auto& EditParameter = SortedParameters[i];
			ComponentParametersMap.Remove(EditParameter.Data.name);
			TMap<FString, FParameterData> OverrideParameters1;
			FParameterProcLibrary::CombineParameters(InParameters, ComponentParametersMap, OverrideParameters1);
			FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, OverrideParameters1, EditParameter);
			ComponentParametersMap.Add(EditParameter.Data.name, EditParameter);
			const int32 Index = InOutMultiComponentData.ComponentParameters.IndexOfByPredicate([EditParameter](const FParameterData& InOther)
			{
				return InOther.Data.name.Equals(EditParameter.Data.name);
			});
			if (INDEX_NONE != Index)
			{
				InOutMultiComponentData.ComponentParameters[Index] = EditParameter;
			}
		}
		FParameterProcLibrary::CombineParameters(InParameters, ComponentParametersMap, OverrideParameters);
	}

	{
		//计算ID引用是否发生改变
		const int32 PreComponentID = FCString::Atof(*InOutMultiComponentData.ComponentID.Value);
		if (false == CalculateParameterValue(InGlobalParameter, OverrideParameters, InOutMultiComponentData.ComponentID))
		{
			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 2"));
			return false;
		}
		const int32 NewComponentID = FCString::Atof(*InOutMultiComponentData.ComponentID.Value);
		InOutMultiComponentData.bComponentIDChanged = InOutMultiComponentData.bComponentIDChanged || PreComponentID != NewComponentID;
		if (InOutMultiComponentData.bComponentIDChanged)
		{
			InOutMultiComponentData.ComponentType = ECompType::None;
			InOutMultiComponentData.ChildComponent.Empty();
			InOutMultiComponentData.SingleComponentPath.Empty();
			InOutMultiComponentData.SingleComponentData.Empty();
		}
	}
	if (false == CalculateParameterValue(InGlobalParameter, OverrideParameters, InOutMultiComponentData.ComponentVisibility))
	{
		return false;
	}
	FExpressionValuePair CodePair(InOutMultiComponentData.CodeExp);
	if (false == CalculateParameterValue(InGlobalParameter, OverrideParameters, CodePair))
	{
		return false;
	}
	InOutMultiComponentData.Code = CodePair.Value;
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutMultiComponentData.ComponentLocation))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutMultiComponentData.ComponentRotation))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, InParameters, InOutMultiComponentData.ComponentScale))
	{
		return false;
	}
	if (false == CalculateParameterValue(InGlobalParameter, OverrideParameters, InOutMultiComponentData.SingleComponentData))
	{
		return false;
	}
	for (int32 i = 0; i < InOutMultiComponentData.ChildComponent.Num(); ++i)
	{
		if (false == CalculateParameterValue(InGlobalParameter, OverrideParameters, *InOutMultiComponentData.ChildComponent[i]))
		{
			return false;
		}
	}
	return true;
}

void FGeometryDatas::CalculateParameterValue(
	const TMap<FString, FParameterData>& InGlobalParameter,
	TMap<FString, FParameterData> InParameters,
	TMap<FString, FParameterData>& ComponentParametersMap
)
{
	TArray<FParameterData> WithStyleParameters;
	ComponentParametersMap.GenerateValueArray(WithStyleParameters);
	ComponentParametersMap.Empty();
	FParameterRefrenceParser Parser;
	bool Res = Parser.SortParameterByRefrence(WithStyleParameters);
	auto SortedParameters = Res ? Parser.GetSortedParameters() : Parser.GetOriginalParameters();
	FParameterProcLibrary::CombineParameters(ComponentParametersMap, SortedParameters);
	for (int32 i = 0; i < SortedParameters.Num(); ++i)
	{
		auto& EditParameter = SortedParameters[i];
		ComponentParametersMap.Remove(EditParameter.Data.name);
		InParameters.Remove(EditParameter.Data.name);
		FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, InParameters, EditParameter);
		ComponentParametersMap.Add(EditParameter.Data.name, EditParameter);
		InParameters.Add(EditParameter.Data.name, EditParameter);
	}
}

void FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TArray<FDecorateStyleModel>& InStyleParameters,
                                             TMap<FString, FParameterData>& ComponentParametersMap)
{
	TMap<FString, FParameterData> StyleParameters;
	FDecorateStyleModel::ParseDecorateStyleParameters(InStyleParameters, StyleParameters);
	CalculateParameterValue(InGlobalParameter, StyleParameters, ComponentParametersMap);
}

bool FGeometryDatas::CalculateParameterExpressionByLevel(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParentParameters,
                                                         FParameterData& InOutParameter)
{
	bool Res = true;
	TMap<FString, FParameterData> ParentParameters = InParentParameters;
	FString FormatExpresstion = FString();
	Res = FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, InParentParameters, InOutParameter.Data.expression, InOutParameter.Data.value, FormatExpresstion);
	if (Res)
	{
		InOutParameter.Data.expression = FormatExpresstion;
	}
	/*if (ParentParameters.Contains(InOutParameter.Data.name))
		ParentParameters[InOutParameter.Data.name] = InOutParameter;
	else
		ParentParameters.Add(InOutParameter.Data.name, InOutParameter);*/

	if (!InOutParameter.Data.max_expression.IsEmpty())
	{
		Res = Res && FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, ParentParameters, InOutParameter.Data.max_expression, InOutParameter.Data.max_value, FormatExpresstion);
		if (Res)
		{
			InOutParameter.Data.max_expression = FormatExpresstion;
		}
	}
	if (!InOutParameter.Data.min_expression.IsEmpty())
	{
		Res = Res && FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, ParentParameters, InOutParameter.Data.min_expression, InOutParameter.Data.min_value, FormatExpresstion);
		if (Res)
		{
			InOutParameter.Data.min_expression = FormatExpresstion;
		}
	}
	if (!InOutParameter.Data.visibility_exp.IsEmpty())
	{
		Res = Res && FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, ParentParameters, InOutParameter.Data.visibility_exp, InOutParameter.Data.visibility, FormatExpresstion);
		if (Res)
		{
			InOutParameter.Data.visibility_exp = FormatExpresstion;
		}
	}
	if (!InOutParameter.Data.editable_exp.IsEmpty())
	{
		Res = Res && FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, ParentParameters, InOutParameter.Data.editable_exp, InOutParameter.Data.editable, FormatExpresstion);
		if (Res)
		{
			InOutParameter.Data.editable_exp = FormatExpresstion;
		}
	}
	for (int32 i = 0; Res && i < InOutParameter.EnumData.Num(); ++i)
	{
		if (!InOutParameter.EnumData[i].expression.IsEmpty())
		{
			Res = FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, ParentParameters, InOutParameter.EnumData[i].expression, InOutParameter.EnumData[i].value, FormatExpresstion);
			if (Res)
			{
				InOutParameter.EnumData[i].expression = FormatExpresstion;
			}
		}
		if (!InOutParameter.EnumData[i].visibility_exp.IsEmpty())
		{
			Res = Res && FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, ParentParameters, InOutParameter.EnumData[i].visibility_exp, InOutParameter.EnumData[i].visibility,
			                                                                 FormatExpresstion);
			if (Res)
			{
				InOutParameter.EnumData[i].visibility_exp = FormatExpresstion;
			}
		}
	}
	Res = Res && FParameterProcLibrary::CheckParameterValue(InOutParameter);
	return Res;
}

bool FGeometryDatas::CalculateParameterValueExpressionOnly(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParentParameters,
                                                           FParameterData& InOutParameter)
{
	bool Res = true;
	FString FormatExpresstion = TEXT("");
	if (!InOutParameter.Data.expression.IsEmpty())
	{
		Res = FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, InParentParameters, InOutParameter.Data.expression, InOutParameter.Data.value, FormatExpresstion);
	}
	if (Res)
	{
		InOutParameter.Data.expression = FormatExpresstion;
	}
	return Res;
}

bool FGeometryDatas::CalculateParameterExtremumExpressionOnly(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParentParameters,
                                                              FParameterData& InOutParameter)
{
	bool Res = true;
	TMap<FString, FParameterData> ParentParameters = InParentParameters;
	//if (ParentParameters.Contains(InOutParameter.Data.name))
	//	ParentParameters[InOutParameter.Data.name] = InOutParameter;
	//else
	//	ParentParameters.Add(InOutParameter.Data.name, InOutParameter);
	FString FormatExpresstion = TEXT("");
	if (!InOutParameter.Data.max_expression.IsEmpty())
	{
		Res = Res && FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, ParentParameters, InOutParameter.Data.max_expression, InOutParameter.Data.max_value, FormatExpresstion);
		if (Res)
		{
			InOutParameter.Data.max_expression = FormatExpresstion;
		}
	}
	if (!InOutParameter.Data.min_expression.IsEmpty())
	{
		Res = Res && FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, ParentParameters, InOutParameter.Data.min_expression, InOutParameter.Data.min_value, FormatExpresstion);
		if (Res)
		{
			InOutParameter.Data.min_expression = FormatExpresstion;
		}
	}

	//计算过极值后再进行值的校验
	Res = Res && FParameterProcLibrary::CheckParameterValue(InOutParameter);

	return Res;
}

bool FGeometryDatas::CalculateParameterOtherExpressionOnly(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParentParameters,
                                                           FParameterData& InOutParameter)
{
	bool Res = true;
	TMap<FString, FParameterData> ParentParameters = InParentParameters;
	FString FormatExpresstion = TEXT("");
	if (!InOutParameter.Data.visibility_exp.IsEmpty())
	{
		Res = Res && FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, ParentParameters, InOutParameter.Data.visibility_exp, InOutParameter.Data.visibility, FormatExpresstion);
		if (Res)
		{
			InOutParameter.Data.visibility_exp = FormatExpresstion;
		}
	}
	if (!InOutParameter.Data.editable_exp.IsEmpty())
	{
		Res = Res && FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, ParentParameters, InOutParameter.Data.editable_exp, InOutParameter.Data.editable, FormatExpresstion);
		if (Res)
		{
			InOutParameter.Data.editable_exp = FormatExpresstion;
		}
	}
	for (int32 i = 0; Res && i < InOutParameter.EnumData.Num(); ++i)
	{
		if (!InOutParameter.EnumData[i].expression.IsEmpty())
		{
			Res = FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, ParentParameters, InOutParameter.EnumData[i].expression, InOutParameter.EnumData[i].value, FormatExpresstion);
			if (Res)
			{
				InOutParameter.EnumData[i].expression = FormatExpresstion;
			}
		}
		if (!InOutParameter.EnumData[i].visibility_exp.IsEmpty())
		{
			Res = Res && FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, ParentParameters, InOutParameter.EnumData[i].visibility_exp, InOutParameter.EnumData[i].visibility,
			                                                                 FormatExpresstion);
			if (Res)
			{
				InOutParameter.EnumData[i].visibility_exp = FormatExpresstion;
			}
		}
	}
	return Res;
}

bool FGeometryDatas::CalculateParameterValue_LevelSort(
	const TMap<FString, FParameterData>& InGlobalParameter,
	TMap<FString, FParameterData> InParentParameters,
	TMap<FString, FParameterData>& ComponentParametersMap
)
{
	//循环引用计算
	TMap<FString, FParameterData> OriginComponentParametersMap = ComponentParametersMap;
	bool FinalRes = true; //用于返回计算结果是否错误
	//仅值引用时其它可以直接计算，仅其他引用时值可以直接计算，两者都有时先拆分后重复值与其他的过程
	//本级参数
	TArray<FParameterData> ComponentParameters = TArray<FParameterData>();
	ComponentParametersMap.GenerateValueArray(ComponentParameters);
	UE_LOG(LogTemp, Verbose, TEXT("UParameterRelativeLibrary::CalculateParameterValue_LevelSort in ComponentParameters Num @ %d "), ComponentParameters.Num());
	ComponentParametersMap.Empty();

	//字符串的处理
	TArray<FParameterData> BackUp = TArray<FParameterData>();
	for (auto& iter : ComponentParameters)
	{
		if (iter.Data.expression.Len() > 2 && iter.Data.expression[0] == '\"' && iter.Data.expression[iter.Data.expression.Len() - 1] == '\"')
		{
			FString temp = iter.Data.expression;
			temp.RemoveAt(0);
			temp.RemoveAt(temp.Len() - 1);
			iter.Data.value = temp;
			BackUp.Add(iter);
			iter.Data.expression = FString("0.0"); //后续当作0.0计算
			iter.Data.value = FString("0.0");
		}
	}

	//原始父级参数保存用于计算值循环项
	TMap<FString, FParameterData> ParentParameters = InParentParameters;

	FParameterRefrenceParser Parser;
	TArray<FParameterData> SortedParameters = TArray<FParameterData>();
	//解析当前参数需要的所有引用
	TMap<FString, TArray<FString>> AllRefMap = TMap<FString, TArray<FString>>();
	Parser.ParseRefrenceMap(ComponentParameters, SortedParameters, AllRefMap);

	//新的同级参数排序可以直接进行计算
	TArray<FParameterData> SameLevelSortedParameters = TArray<FParameterData>();
	Parser.SortSameLevelParmeter(AllRefMap, ComponentParameters, SameLevelSortedParameters);

	//解析参数引用分类
	TArray<FParameterData> NoneRefrenceParmeter = TArray<FParameterData>(); //GetNoneRefrenceParmeter
	TMap<FString, TArray<FString>> ValueOnlyRefrenceMap = TMap<FString, TArray<FString>>(); //GetValueExpWithRef
	TMap<FString, TArray<FString>> OtherOnlyRefrenceMap = TMap<FString, TArray<FString>>(); //GetOtherExpWithRef
	TMap<FString, TArray<FString>> BothRefrenceMap = TMap<FString, TArray<FString>>(); //GeBothtValueExpWithRef this is a mark but with value's ref
	TMap<FString, TArray<FString>> BothOtherRefrenceMap = TMap<FString, TArray<FString>>(); //GeBothtOtherExpWithRef 
	bool Res = Parser.SortParameterByParamRefrence(ComponentParameters, NoneRefrenceParmeter, ValueOnlyRefrenceMap, OtherOnlyRefrenceMap, BothRefrenceMap, BothOtherRefrenceMap);

	//解析当前参数其它中需要的引用
	TMap<FString, TArray<FString>> AllOtherRefrenceMap = TMap<FString, TArray<FString>>();
	FParameterProcLibrary::CombineParameters(AllOtherRefrenceMap, OtherOnlyRefrenceMap);
	FParameterProcLibrary::CombineParameters(AllOtherRefrenceMap, BothOtherRefrenceMap);

	if (!Res)
	{
		//纯常数或纯上级的计算不需任何特殊处理
		auto ParametersList = Parser.GetOriginalParameters();
		TMap<FString, FParameterData> TempParentParameters = ParentParameters;
		for (int32 i = 0; i < ParametersList.Num(); ++i)
		{
			//FinalRes = FinalRes && UParameterRelativeLibrary::CalculateParameterExpression(InGlobalParameter, ParentParameters, ParametersList[i]);
			FinalRes = FinalRes && CalculateParameterValueExpressionOnly(InGlobalParameter, TempParentParameters, ParametersList[i]);
			FinalRes = FinalRes && CalculateParameterExtremumExpressionOnly(InGlobalParameter, TempParentParameters, ParametersList[i]);
			if (TempParentParameters.Contains(ParametersList[i].Data.name))
			{
				TempParentParameters[ParametersList[i].Data.name] = ParametersList[i];
			}
			else
			{
				TempParentParameters.Add(ParametersList[i].Data.name, ParametersList[i]);
			}
		}
		for (int32 i = 0; i < ParametersList.Num(); ++i)
		{
			FinalRes = FinalRes && CalculateParameterOtherExpressionOnly(InGlobalParameter, TempParentParameters, ParametersList[i]);
		}
		FParameterProcLibrary::CombineParameters(ComponentParametersMap, ParametersList);

		//复原字符串表达式
		for (const auto& iter : BackUp)
		{
			FString CurName = iter.Data.name;
			if (ComponentParametersMap.Contains(CurName))
			{
				ComponentParametersMap[CurName] = iter;
			}
			else
			{
				ComponentParametersMap.Add(CurName, iter);
			}
		}

		return FinalRes;
	}
	//无引用
	TMap<FString, FParameterData> CurParentParameters = ParentParameters;
	if (NoneRefrenceParmeter.Num() > 0)
	{
		for (int32 i = 0; i < NoneRefrenceParmeter.Num(); ++i) //常数
		{
			FinalRes = FinalRes && CalculateParameterExpressionByLevel(InGlobalParameter, CurParentParameters, NoneRefrenceParmeter[i]);
			if (CurParentParameters.Contains(NoneRefrenceParmeter[i].Data.name))
			{
				CurParentParameters[NoneRefrenceParmeter[i].Data.name] = NoneRefrenceParmeter[i];
			}
			else
			{
				CurParentParameters.Add(NoneRefrenceParmeter[i].Data.name, NoneRefrenceParmeter[i]);
			}
		}
		FParameterProcLibrary::CombineParameters(ComponentParametersMap, NoneRefrenceParmeter);
	}

	//仅值存在引用关系
	TArray<FParameterData> ValueSameLevel = TArray<FParameterData>(); //值引用中的同级后置
	TArray<FParameterData> ValueTempSameLevelCirBoth = TArray<FParameterData>(); //值引用中的同级后置
	if (ValueOnlyRefrenceMap.Num() > 0)
	{
		TMap<FString, TArray<FString>> TempRefMap = ValueOnlyRefrenceMap; //同时考虑所有的值引用，区分同级和同级环
		if (BothRefrenceMap.Num() > 0)
		{
			TempRefMap.Append(BothRefrenceMap);
		}
		TArray<FParameterData> TempSameLevel = TArray<FParameterData>();
		TArray<FParameterData> TempSameLevelCir = TArray<FParameterData>();
		Parser.DetermineRefEitherCircleOrNot(ComponentParameters, TempRefMap, TempSameLevel, TempSameLevelCir);
		for (auto& iter : TempSameLevel)
		{
			if (BothRefrenceMap.Contains(iter.Data.name))
			{
				continue;
			}
			ValueSameLevel.AddUnique(iter); //值引用同级保存
		}

		for (int32 i = 0; i < TempSameLevelCir.Num(); ++i) //值同级循环先行计算
		{
			if (BothRefrenceMap.Contains(TempSameLevelCir[i].Data.name))
			{
				//快进参数计算
				TArray<FParameterData> DoneParmeter = TArray<FParameterData>();
				const FString& CurDataName = TempSameLevelCir[i].Data.name;

				if (AllRefMap.Contains(CurDataName))
				{
					bool GotAllRefDoneFlag = true;
					for (auto& RefIter : AllRefMap[CurDataName])
					{
						if (!CurParentParameters.Contains(RefIter))
						{
							GotAllRefDoneFlag = false;
							break;
						}
					}
					if (GotAllRefDoneFlag)
					{
						FinalRes = FinalRes && CalculateParameterValueExpressionOnly(InGlobalParameter, ParentParameters, TempSameLevelCir[i]);
						FinalRes = FinalRes && CalculateParameterExtremumExpressionOnly(InGlobalParameter, CurParentParameters, TempSameLevelCir[i]);
						//UParameterRelativeLibrary::CalculateParameterExpressionByLevel(InGlobalParameter, CurParentParameters, TempSameLevelCir[i]);
						ValueTempSameLevelCirBoth.Add(TempSameLevelCir[i]);
						DoneParmeter.AddUnique(TempSameLevelCir[i]);
						if (CurParentParameters.Contains(CurDataName))
						{
							CurParentParameters[CurDataName] = TempSameLevelCir[i];
						}
						else
						{
							CurParentParameters.Add(CurDataName, TempSameLevelCir[i]);
						}
						if (ComponentParametersMap.Contains(CurDataName))
						{
							ComponentParametersMap[CurDataName] = TempSameLevelCir[i];
						}
						else
						{
							ComponentParametersMap.Add(CurDataName, TempSameLevelCir[i]);
						}
						continue;
					}
				}
				else
				{
					//不可能发生
					UE_LOG(LogTemp, Error, TEXT("UParameterRelativeLibrary::ValueOnlyRefrenceMap Has An Error "));
				}
				//快进参数计算结束

				//处理都有时值的循环，因为直接使用值，所以影响小，后续再对新父类的值校准
				FinalRes = FinalRes && CalculateParameterValueExpressionOnly(InGlobalParameter, ParentParameters, TempSameLevelCir[i]);

				//考虑同时使用同级的值暂时计算其它
				//同级检索//复制起点
				TMap<FString, FParameterData> TempParentParameters = CurParentParameters; //每个将计算的参数都有专属父类PP
				if (ValueOnlyRefrenceMap.Contains(CurDataName))
				{
					for (const auto& RefIter : ValueOnlyRefrenceMap[CurDataName])
					{
						int32 CurRefIndex = ComponentParameters.IndexOfByPredicate(
							[RefIter](const FParameterData& p)-> bool { return p.Data.name.Equals(RefIter, ESearchCase::IgnoreCase); }
						);
						if (CurRefIndex == INDEX_NONE)
						{
							continue;
						}
						//引用值已计算且存在
						if (TempParentParameters.Contains(RefIter) && TempParentParameters[RefIter].Data.id.Equals(ComponentParameters[CurRefIndex].Data.id, ESearchCase::IgnoreCase))
						{
							continue;
						}
						//UE_LOG(LogTemp, Error, TEXT("UParameterRelativeLibrary::ValueSameLevel if（%d && %d） "), TempParentParameters.Contains(RefIter), TempParentParameters[RefIter].Data.id.Equals(ComponentParameters[CurRefIndex].Data.id, ESearchCase::IgnoreCase));
						//优先插入编辑的值用于计算
						/*if (ChangedParameter.IsValid() && ChangedParameter.Data.name.Equals(CurDataName, ESearchCase::IgnoreCase))
							{
								if (TempParentParameters.Contains(RefIter))
									TempParentParameters[RefIter] = ChangedParameter;
								else
									TempParentParameters.Add(CurDataName, ChangedParameter);
								continue;
							}*/
						//没有编辑值插入同级值
						if (TempParentParameters.Contains(RefIter))
						{
							TempParentParameters[RefIter] = ComponentParameters[CurRefIndex];
						}
						else
						{
							TempParentParameters.Add(CurDataName, ComponentParameters[CurRefIndex]); //插入同级的值用于计算
						}
					}
				} //复制结束###
				FinalRes = FinalRes && CalculateParameterExtremumExpressionOnly(InGlobalParameter, TempParentParameters, TempSameLevelCir[i]);

				ValueTempSameLevelCirBoth.Add(TempSameLevelCir[i]);
				if (CurParentParameters.Contains(TempSameLevelCir[i].Data.name))
				{
					CurParentParameters[TempSameLevelCir[i].Data.name] = TempSameLevelCir[i];
				}
				else
				{
					CurParentParameters.Add(TempSameLevelCir[i].Data.name, TempSameLevelCir[i]);
				}
			} /*continue;*/
			else
			{
				//仅值循环，其他不存在引用
				FinalRes = FinalRes && CalculateParameterExpressionByLevel(InGlobalParameter, ParentParameters, TempSameLevelCir[i]);
				if (CurParentParameters.Contains(TempSameLevelCir[i].Data.name))
				{
					CurParentParameters[TempSameLevelCir[i].Data.name] = TempSameLevelCir[i];
				}
				else
				{
					CurParentParameters.Add(TempSameLevelCir[i].Data.name, TempSameLevelCir[i]);
				}
			}

			//else//都有时存在循环也使用上级计算
			//{
			//	
			//}
		}
		FParameterProcLibrary::CombineParameters(ComponentParametersMap, TempSameLevelCir);
	}

	//仅其它表达式存在引用关系
	TArray<FParameterData> OtherSameLevel = TArray<FParameterData>(); //其它同级后置
	TArray<FParameterData> OtherSameLevelCir = TArray<FParameterData>(); //其它同级后置
	if (OtherOnlyRefrenceMap.Num() > 0)
	{
		TMap<FString, TArray<FString>> TempRefMap = OtherOnlyRefrenceMap;
		if (BothOtherRefrenceMap.Num() > 0)
		{
			TempRefMap.Append(BothOtherRefrenceMap);
		}
		TArray<FParameterData> TempSameLevel = TArray<FParameterData>();
		TArray<FParameterData> TempSameLevelCir = TArray<FParameterData>();
		//函数继续使用因可转换Map至ParameterData
		Parser.DetermineRefEitherCircleOrNot(ComponentParameters, TempRefMap, TempSameLevel, TempSameLevelCir);
		for (auto& iter : TempSameLevel)
		{
			if (BothOtherRefrenceMap.Contains(iter.Data.name))
			{
				continue;
			}
			OtherSameLevel.AddUnique(iter);
		}
		//其它引用循环不再计算统一使用同级参数特殊处理
		for (auto& iter : TempSameLevelCir)
		{
			if (BothOtherRefrenceMap.Contains(iter.Data.name))
			{
				continue;
			}
			OtherSameLevelCir.AddUnique(iter);
		}
	}

	//值与其它均有引用关系时
	TArray<FParameterData> BothValueSameLevel = TArray<FParameterData>(); //都有时值的同级后置
	TArray<FParameterData> BothValueSameLevelCir = TArray<FParameterData>(); //都有时值的同级循环后置
	TArray<FParameterData> BothOtherSameLevel = TArray<FParameterData>(); //其它的同级后置
	//都有先算值引用循环
	if (BothRefrenceMap.Num() > 0)
	{
		//解析所有参数值中的表达式引用
		TMap<FString, TArray<FString>> ValueRefrenceMap = TMap<FString, TArray<FString>>();
		TArray<FParameterData> ValueNoneRefParameters = TArray<FParameterData>();
		Parser.ParseValueExpRefrenceMap(ComponentParameters, ValueRefrenceMap, ValueNoneRefParameters);
		//解析所有参数其它中的表达式引用
		TMap<FString, TArray<FString>> OtherRefrenceMap = TMap<FString, TArray<FString>>();
		TArray<FParameterData> OtherNoneRefParameters = TArray<FParameterData>();
		Parser.ParseOtherExpRefrenceMap(ComponentParameters, OtherRefrenceMap, OtherNoneRefParameters);

		//处理都有时的值
		//TMap<FString, FParameterData> SyncData = TMap<FString, FParameterData>();
		TArray<FParameterData> ValueTempSameLevel = TArray<FParameterData>();
		TArray<FParameterData> ValueTempSameLevelCir = TArray<FParameterData>();
		Parser.DetermineRefEitherCircleOrNot(ComponentParameters, ValueRefrenceMap, ValueTempSameLevel, ValueTempSameLevelCir);
		//处理不包含在双引的值
		for (auto& iter : ValueTempSameLevel)
		{
			if (!BothRefrenceMap.Contains(iter.Data.name))
			{
				continue;
			}
			BothValueSameLevel.AddUnique(iter);
		}

		for (auto& iter : ValueTempSameLevelCir)
		{
			if (!BothRefrenceMap.Contains(iter.Data.name))
			{
				continue;
			}
			FinalRes = FinalRes && CalculateParameterValueExpressionOnly(InGlobalParameter, ParentParameters, iter);
			//SyncData.Add(iter.Data.name, iter);
			BothValueSameLevelCir.AddUnique(iter);
			//iter.Data.expression = iter.Data.value;
			//UParameterRelativeLibrary::CombineParameters(CurParentParameters, ValueTempSameLevelCir);
		}
		//处理都有时的其它
		TArray<FParameterData> OtherTempSameLevel = TArray<FParameterData>();
		TArray<FParameterData> OtherTempSameLevelCir = TArray<FParameterData>();
		Parser.DetermineRefEitherCircleOrNot(ComponentParameters, OtherRefrenceMap, OtherTempSameLevel, OtherTempSameLevelCir);
		//其他的所有计算均使用同级计算
		for (auto& iter : OtherTempSameLevel)
		{
			if (!BothRefrenceMap.Contains(iter.Data.name))
			{
				continue;
			}
			BothOtherSameLevel.AddUnique(iter);
		}
		for (auto& iter : OtherTempSameLevelCir)
		{
			if (!BothRefrenceMap.Contains(iter.Data.name))
			{
				continue;
			}
			BothOtherSameLevel.AddUnique(iter);
		}
	}
	//	同级循环提前算
	for (auto& OtherSameLevelCirIter : OtherSameLevelCir)
	{
		FString CurDataName = OtherSameLevelCirIter.Data.name;
		//同级检索//复制起点
		TMap<FString, FParameterData> TempParentParameters = CurParentParameters; //每个将计算的参数都有专属父类PP
		if (OtherOnlyRefrenceMap.Contains(CurDataName))
		{
			for (const auto& RefIter : OtherOnlyRefrenceMap[CurDataName])
			{
				int32 CurRefIndex = ComponentParameters.IndexOfByPredicate(
					[RefIter](const FParameterData& p)-> bool { return p.Data.name.Equals(RefIter, ESearchCase::IgnoreCase); }
				);
				if (CurRefIndex == INDEX_NONE)
				{
					continue;
				}
				//引用值已计算且存在
				if (TempParentParameters.Contains(RefIter) && TempParentParameters[RefIter].Data.id.Equals(ComponentParameters[CurRefIndex].Data.id, ESearchCase::IgnoreCase))
				{
					continue;
				}
				UE_LOG(LogTemp, Log, TEXT("UParameterRelativeLibrary:: %s "), *CurDataName);
				//UE_LOG(LogTemp, Error, TEXT("UParameterRelativeLibrary::OtherSameLevel if（%d && %d） "), TempParentParameters.Contains(RefIter), TempParentParameters[RefIter].Data.id.Equals(ComponentParameters[CurRefIndex].Data.id, ESearchCase::IgnoreCase));
				//没有计算过的同级参数先计算表达式然后插入
				auto RefP = ComponentParameters[CurRefIndex];
				if (CurParentParameters.Contains(RefIter) && CurParentParameters[RefIter].Data.id.Equals(RefP.Data.id, ESearchCase::IgnoreCase))
				{
					continue;
				}

				FinalRes = FinalRes && CalculateParameterValueExpressionOnly(InGlobalParameter, CurParentParameters, RefP);


				if (TempParentParameters.Contains(RefIter))
				{
					TempParentParameters[RefIter] = RefP;
				}
				else
				{
					TempParentParameters.Add(CurDataName, RefP); //插入同级的值用于计算
				}
			}
		} //复制结束###
		FinalRes = FinalRes && CalculateParameterExpressionByLevel(InGlobalParameter, TempParentParameters, OtherSameLevelCirIter);
		if (CurParentParameters.Contains(CurDataName))
		{
			CurParentParameters[CurDataName] = OtherSameLevelCirIter;
		}
		else
		{
			CurParentParameters.Add(CurDataName, OtherSameLevelCirIter);
		}
		if (ComponentParametersMap.Contains(CurDataName))
		{
			ComponentParametersMap[CurDataName] = OtherSameLevelCirIter;
		}
		else
		{
			ComponentParametersMap.Add(CurDataName, OtherSameLevelCirIter);
		}
	}
	//所有同级计算的处理 顺序为 值同级、其它同级(包括循环处理)、都有值循环、都有值同级、都有其它同级(包括循环处理)
	// *需要加入除循环常数以外的所有参数进行排序后进行同级计算*

	for (auto& SameLevelIter : SameLevelSortedParameters)
	{
		FString CurName = SameLevelIter.Data.name;
		int32 ValueTempSameLevelCirIndex = ValueTempSameLevelCirBoth.IndexOfByPredicate(
			[CurName](const FParameterData& p)-> bool { return p.Data.name.Equals(CurName, ESearchCase::IgnoreCase); }
		);
		int32 BothValueSameLevelCirIndex = BothValueSameLevelCir.IndexOfByPredicate(
			[CurName](const FParameterData& p)-> bool { return p.Data.name.Equals(CurName, ESearchCase::IgnoreCase); }
		);

		if (ValueTempSameLevelCirIndex != INDEX_NONE)
		{
			SameLevelIter = ValueTempSameLevelCirBoth[ValueTempSameLevelCirIndex];
			FinalRes = FinalRes && CalculateParameterExtremumExpressionOnly(InGlobalParameter, CurParentParameters, SameLevelIter);
		}
		else if (BothValueSameLevelCirIndex != INDEX_NONE)
		{
			SameLevelIter = BothValueSameLevelCir[BothValueSameLevelCirIndex];
			FinalRes = FinalRes && CalculateParameterExtremumExpressionOnly(InGlobalParameter, CurParentParameters, SameLevelIter);
		}
		else
		{
			FinalRes = FinalRes && CalculateParameterExpressionByLevel(InGlobalParameter, CurParentParameters, SameLevelIter);
		}

		if (CurParentParameters.Contains(CurName))
		{
			CurParentParameters[CurName] = SameLevelIter;
		}
		else
		{
			CurParentParameters.Add(CurName, SameLevelIter);
		}
		if (ComponentParametersMap.Contains(CurName))
		{
			ComponentParametersMap[CurName] = SameLevelIter;
		}
		else
		{
			ComponentParametersMap.Add(CurName, SameLevelIter);
		}
	}
	for (auto& ValueSameLevelIter : ValueSameLevel)
	{
		FString CurName = ValueSameLevelIter.Data.name;
		if (ComponentParametersMap.Contains(CurName))
		{
			continue;
		}
		FinalRes = FinalRes && CalculateParameterExpressionByLevel(InGlobalParameter, CurParentParameters, ValueSameLevelIter);
		if (CurParentParameters.Contains(CurName))
		{
			CurParentParameters[CurName] = ValueSameLevelIter;
		}
		else
		{
			CurParentParameters.Add(CurName, ValueSameLevelIter);
		}
		if (ComponentParametersMap.Contains(CurName))
		{
			ComponentParametersMap[CurName] = ValueSameLevelIter;
		}
		else
		{
			ComponentParametersMap.Add(CurName, ValueSameLevelIter);
		}
	}
	for (auto& OtherSameLevelIter : OtherSameLevel)
	{
		FString CurName = OtherSameLevelIter.Data.name;
		if (ComponentParametersMap.Contains(CurName))
		{
			continue;
		}

		//同级检索//复制起点
		const FString& CurDataName = OtherSameLevelIter.Data.name;
		TMap<FString, FParameterData> TempParentParameters = CurParentParameters; //每个将计算的参数都有专属父类PP
		if (OtherOnlyRefrenceMap.Contains(CurDataName))
		{
			for (const auto& RefIter : OtherOnlyRefrenceMap[CurDataName])
			{
				int32 CurRefIndex = ComponentParameters.IndexOfByPredicate(
					[RefIter](const FParameterData& p)-> bool { return p.Data.name.Equals(RefIter, ESearchCase::IgnoreCase); }
				);
				if (CurRefIndex == INDEX_NONE)
				{
					continue;
				}
				//引用值已计算且存在
				if (TempParentParameters.Contains(RefIter) && TempParentParameters[RefIter].Data.id.Equals(ComponentParameters[CurRefIndex].Data.id, ESearchCase::IgnoreCase))
				{
					continue;
				}
				UE_LOG(LogTemp, Log, TEXT("UParameterRelativeLibrary:: %s "), *CurDataName);
				//UE_LOG(LogTemp, Error, TEXT("UParameterRelativeLibrary::OtherSameLevel if（%d && %d） "), TempParentParameters.Contains(RefIter), TempParentParameters[RefIter].Data.id.Equals(ComponentParameters[CurRefIndex].Data.id, ESearchCase::IgnoreCase));
				//没有编辑值插入同级值
				if (TempParentParameters.Contains(RefIter))
				{
					TempParentParameters[RefIter] = ComponentParameters[CurRefIndex];
				}
				else
				{
					TempParentParameters.Add(CurDataName, ComponentParameters[CurRefIndex]); //插入同级的值用于计算
				}
			}
		} //复制结束###

		FinalRes = FinalRes && CalculateParameterExpressionByLevel(InGlobalParameter, TempParentParameters, OtherSameLevelIter);
		if (CurParentParameters.Contains(CurName))
		{
			CurParentParameters[CurName] = OtherSameLevelIter;
		}
		else
		{
			CurParentParameters.Add(CurName, OtherSameLevelIter);
		}
		if (ComponentParametersMap.Contains(CurName))
		{
			ComponentParametersMap[CurName] = OtherSameLevelIter;
		}
		else
		{
			ComponentParametersMap.Add(CurName, OtherSameLevelIter);
		}
	}
	for (auto& BothIter : BothOtherSameLevel)
	{
		FString CurName = BothIter.Data.name;
		if (ComponentParametersMap.Contains(CurName))
		{
			continue;
		}
		//同级检索//复制起点
		const FString& CurDataName = BothIter.Data.name;
		TMap<FString, FParameterData> TempParentParameters = CurParentParameters; //每个将计算的参数都有专属父类PP
		if (OtherOnlyRefrenceMap.Contains(CurDataName))
		{
			for (const auto& RefIter : OtherOnlyRefrenceMap[CurDataName])
			{
				int32 CurRefIndex = ComponentParameters.IndexOfByPredicate(
					[RefIter](const FParameterData& p)-> bool { return p.Data.name.Equals(RefIter, ESearchCase::IgnoreCase); }
				);
				if (CurRefIndex == INDEX_NONE)
				{
					continue;
				}
				//引用值已计算且存在
				if (TempParentParameters.Contains(RefIter) && TempParentParameters[RefIter].Data.id.Equals(ComponentParameters[CurRefIndex].Data.id, ESearchCase::IgnoreCase))
				{
					continue;
				}
				UE_LOG(LogTemp, Error, TEXT("UParameterRelativeLibrary:: %s "), *CurDataName);
				//UE_LOG(LogTemp, Error, TEXT("UParameterRelativeLibrary::OtherSameLevel if（%d && %d） "), TempParentParameters.Contains(RefIter), TempParentParameters[RefIter].Data.id.Equals(ComponentParameters[CurRefIndex].Data.id, ESearchCase::IgnoreCase));
				//没有编辑值插入同级值
				if (TempParentParameters.Contains(RefIter))
				{
					TempParentParameters[RefIter] = ComponentParameters[CurRefIndex];
				}
				else
				{
					TempParentParameters.Add(CurDataName, ComponentParameters[CurRefIndex]); //插入同级的值用于计算
				}
			}
		} //复制结束###
		int32 BothValueSameLevelCirIndex = BothValueSameLevelCir.IndexOfByPredicate(
			[CurName](const FParameterData& p)-> bool { return p.Data.name.Equals(CurName, ESearchCase::IgnoreCase); }
		);
		if (BothValueSameLevelCirIndex != INDEX_NONE)
		{
			BothIter = BothValueSameLevelCir[BothValueSameLevelCirIndex];
			FinalRes = FinalRes && CalculateParameterExtremumExpressionOnly(InGlobalParameter, CurParentParameters, BothIter);
		}
		else
		{
			FinalRes = FinalRes && CalculateParameterExpressionByLevel(InGlobalParameter, CurParentParameters, BothIter);
		}

		if (CurParentParameters.Contains(CurName))
		{
			CurParentParameters[CurName] = BothIter;
		}
		else
		{
			CurParentParameters.Add(CurName, BothIter);
		}
		if (ComponentParametersMap.Contains(CurName))
		{
			ComponentParametersMap[CurName] = BothIter;
		}
		else
		{
			ComponentParametersMap.Add(CurName, BothIter);
		}
	}
	//所有值计算过后，使用平级参数计算可见性可编辑性枚举可见性
	UE_LOG(LogTemp, Verbose, TEXT("UParameterRelativeLibrary::CalculateParameterValue_LevelSort Use to Cal Other Num @ %d "), CurParentParameters.Num());
	for (auto& IterAllOther : ComponentParametersMap)
	{
		FinalRes = FinalRes && CalculateParameterOtherExpressionOnly(InGlobalParameter, CurParentParameters, IterAllOther.Value);
	}

	//计算过程结束后对所有结果保留1位小数
	/*for (auto& ResIter : ComponentParametersMap)
	{
		if (!ResIter.Value.Data.value.IsEmpty())
		{
			float TempFloat = FCString::Atof(*ResIter.Value.Data.value);
			ResIter.Value.Data.value = FString::SanitizeFloat(TempFloat);
		}
		if (!ResIter.Value.Data.max_value.IsEmpty())
		{
			float TempFloat = FCString::Atof(*ResIter.Value.Data.max_value);
			ResIter.Value.Data.max_value = FString::SanitizeFloat(TempFloat);
		}
		if (!ResIter.Value.Data.min_value.IsEmpty())
		{
			float TempFloat = FCString::Atof(*ResIter.Value.Data.min_value);
			ResIter.Value.Data.min_value = FString::SanitizeFloat(TempFloat);
		}
		if (!ResIter.Value.Data.editable.IsEmpty())
		{
			float TempFloat = FCString::Atof(*ResIter.Value.Data.editable);
			ResIter.Value.Data.editable = FString::SanitizeFloat(TempFloat);
		}
		if (!ResIter.Value.Data.visibility.IsEmpty())
		{
			float TempFloat = FCString::Atof(*ResIter.Value.Data.visibility);
			ResIter.Value.Data.visibility = FString::SanitizeFloat(TempFloat);
		}
	}*/

	//复原字符串表达式
	for (const auto& iter : BackUp)
	{
		FString CurName = iter.Data.name;
		if (ComponentParametersMap.Contains(CurName))
		{
			ComponentParametersMap[CurName] = iter;
		}
		else
		{
			ComponentParametersMap.Add(CurName, iter);
		}
	}

	UE_LOG(LogTemp, Verbose, TEXT("UParameterRelativeLibrary::CalculateParameterValue_LevelSort Out ComponentParameters Num @ %d "), ComponentParametersMap.Num());
	if (!FinalRes)
	{
		ComponentParametersMap = OriginComponentParametersMap;
	}
	else
	{
		for (const auto& iter : ComponentParametersMap)
		{
			//保持顺序
			if (OriginComponentParametersMap.Contains(iter.Key))
			{
				OriginComponentParametersMap[iter.Key] = iter.Value;
			}
			else
			{
				UE_LOG(LogTemp, Error, TEXT("UParameterRelativeLibrary::CalculateParameterValue_LevelSort XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"));
				UE_LOG(LogTemp, Error, TEXT("UParameterRelativeLibrary::CalculateParameterValue_LevelSort XERROR This Means We Lost Parmeter XXXXXXXXXXXX"));
				UE_LOG(LogTemp, Error, TEXT("UParameterRelativeLibrary::CalculateParameterValue_LevelSort XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"));
			}
		}
		ComponentParametersMap = OriginComponentParametersMap;
	}
	return FinalRes;
	/*TArray<FParameterData> WithStyleParameters;
	ComponentParametersMap.GenerateValueArray(WithStyleParameters);
	ComponentParametersMap.Empty();
	FParameterRefrenceParser Parser;
	bool Res = Parser.SortParameterByParamRefrence(WithStyleParameters);
	auto SortedParameters = Res ? Parser.GetSortedParameters() : Parser.GetOriginalParameters();
	FParameterProcLibrary::CombineParameters(ComponentParametersMap, SortedParameters);
	for (int32 i = 0; i < SortedParameters.Num(); ++i)
	{
		auto& EditParameter = SortedParameters[i];
		ComponentParametersMap.Remove(EditParameter.Data.name);
		InParameters.Remove(EditParameter.Data.name);
		FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, InParameters, EditParameter);
		ComponentParametersMap.Add(EditParameter.Data.name, EditParameter);
		InParameters.Add(EditParameter.Data.name, EditParameter);
	}*/
}

void FGeometryDatas::CalculateSameLevelParameter(const TArray<FString>& ParamNames, const TMap<FString, FParameterData>& GlobalParameters, TMap<FString, FParameterData>& OverrideParameters,
                                                 TMap<FString, FParameterData>& MultiComponentParameters)
{
	for (auto& ParamIter : ParamNames)
	{
		if (OverrideParameters.Contains(ParamIter) || MultiComponentParameters.Contains(ParamIter))
		{
			FString FunctionExpression = OverrideParameters.Contains(ParamIter) ? OverrideParameters[ParamIter].Data.expression : MultiComponentParameters[ParamIter].Data.expression;
			FExpressionValuePair ParamPair(FunctionExpression);
			CalculateParameterValue(GlobalParameters, OverrideParameters, ParamPair);

			if (OverrideParameters.Contains(ParamIter))
			{
				OverrideParameters[ParamIter].Data.value = ParamPair.Value;
			}
			if (MultiComponentParameters.Contains(ParamIter))
			{
				MultiComponentParameters[ParamIter].Data.value = ParamPair.Value;
			}
		}
	}
}

void FGeometryDatas::CalculateSameLevelParameter(const TMap<FString, FParameterData>& GlobalParameters, TMap<FString, FParameterData>& ToEditParameters)
{
	TMap<FString, FParameterData> OldEditParameters = ToEditParameters;
	TArray<FParameterData> WithStyleParameters;
	for (auto& Param : OldEditParameters)
	{
		if (NeedCalculate(Param.Value))
		{
			WithStyleParameters.Add(Param.Value);
			ToEditParameters.Remove(Param.Key);
		}
	}

	FParameterRefrenceParser Parser;
	bool Res = Parser.SortParameterByRefrence(WithStyleParameters);
	auto SortedParameters = Res ? Parser.GetSortedParameters() : Parser.GetOriginalParameters();
	FParameterProcLibrary::CombineParameters(ToEditParameters, SortedParameters);
	for (int32 i = 0; i < SortedParameters.Num(); ++i)
	{
		auto& EditParameter = SortedParameters[i];
		ToEditParameters.Remove(EditParameter.Data.name);
		FParameterProcLibrary::CalculateParameterExpression(GlobalParameters, ToEditParameters, EditParameter);
		ToEditParameters.Add(EditParameter.Data.name, EditParameter);
	}
}

//
//bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, FMultiComponentDataItem& InOutMultiComponentData, int32 Level)
//{
//	{//Fix bug DES-1980
//		TMap<FString, FParameterData> OverrideParameters = InParameters;
//		for (int32 i = 0; i < InOutMultiComponentData.ComponentParameters.Num(); ++i)
//		{
//			FParameterData& Parameter = InOutMultiComponentData.ComponentParameters[i];
//			if (!(Parameter.Data.name.Equals(PARAM_DEPTH) || Parameter.Data.name.Equals(PARAM_WIDTH) || Parameter.Data.name.Equals(PARAM_HEIGHT) || Parameter.Data.name.Equals(PARAM_HEIGHT1) || Parameter.Data.name.Equals(PARAM_KMLB_STR)))
//			{//对于部件的宽高深，需要单独计算
//				FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, InParameters, Parameter);
//				OverrideParameters.Contains(Parameter.Data.name) ? OverrideParameters[Parameter.Data.name] = Parameter : OverrideParameters.Add(Parameter.Data.name, Parameter);
//			}
//		}
//		for (int32 i = 0; i < InOutMultiComponentData.ComponentParameters.Num(); ++i)
//		{
//			FParameterData& Parameter = InOutMultiComponentData.ComponentParameters[i];
//			if (Parameter.Data.name.Equals(PARAM_HEIGHT1) || Parameter.Data.name.Equals(PARAM_KMLB_STR) || Parameter.Data.name.Equals(PARAM_GDFD_STR))
//			{//H1变量需要使用单独的计算逻辑，H1会使用同级的H，而其他变量不会使用同级的变量; 同级变量的计算
//				TMap<FString, FParameterData> MultiParameter = InParameters;
//				auto CleanCompParameters = InOutMultiComponentData.ComponentParameters;
//				CleanCompParameters.RemoveAt(i);
//				FParameterProcLibrary::CombineParameters(MultiParameter, CleanCompParameters);
//				FParameterProcLibrary::CalculateParameterExpression(ADesignStationController::Get()->GetGlobalParameters(), MultiParameter, Parameter);
//			}
//			else if (Parameter.Data.name.Equals(PARAM_DEPTH) || Parameter.Data.name.Equals(PARAM_WIDTH) || Parameter.Data.name.Equals(PARAM_HEIGHT))
//			{
//				FParameterProcLibrary::CalculateParameterExpression(ADesignStationController::Get()->GetGlobalParameters(), OverrideParameters, Parameter);
//			}
//		}
//	}
//	TMap<FString, FParameterData> MultiParameter = InParameters;
//	{
//		if (InOutMultiComponentData.ComponentParameters.Num() > 0) FParameterProcLibrary::CombineParameters(MultiParameter, InOutMultiComponentData.ComponentParameters);
//		if (InOutMultiComponentData.StyleParameters.Num() > 0) FParameterProcLibrary::CombineParameters(MultiParameter, InOutMultiComponentData.StyleParameters);//当前节点有风格参数使用本节点的风格参数
//	}
//	for (int32 i = 0; i < InOutMultiComponentData.StyleParameters.Num(); ++i)
//	{
//		if (false == FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, InParameters, InOutMultiComponentData.StyleParameters[i]))
//		{
//			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 1 i=%d"), i);
//			return false;
//		}
//	}
//	{//不可见的时候不计算其子部件用于优化数据
//		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.ComponentVisibility))
//		{
//			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 3"));
//			return false;
//		}
//		if (!InOutMultiComponentData.IsVisiable()) return true;
//	}
//	const int32 PreComponentID = FCString::Atof(*InOutMultiComponentData.ComponentID.Value);
//	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.ComponentID))
//	{
//		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 2"));
//		return false;
//	}
//	const int32 NewComponentID = FCString::Atof(*InOutMultiComponentData.ComponentID.Value);
//	InOutMultiComponentData.bComponentIDChanged = InOutMultiComponentData.bComponentIDChanged || PreComponentID != NewComponentID;
//	if (InOutMultiComponentData.bComponentIDChanged)
//	{//如果当前节点的ID改变则不再计算子节点的变量
//		InOutMultiComponentData.ComponentType = ECompType::None;
//		InOutMultiComponentData.ChildComponent.Empty();
//		InOutMultiComponentData.SingleComponentPath.Empty();
//		InOutMultiComponentData.SingleComponentData.Empty();
//		return true;
//	}
//	for (int32 i = 0; (500029 == NewComponentID) && (i < InOutMultiComponentData.ComponentParameters.Num()); ++i)
//	{//门的高度需要单独计算500029是单门部件，单门部件中的H1控制门的高度
//		FParameterData& Parameter = InOutMultiComponentData.ComponentParameters[i];
//		if (Parameter.Data.name.Equals(PARAM_HEIGHT1))
//		{//对于部件的宽高深，需要单独计算
//			FParameterProcLibrary::CalculateParameterExpression(ADesignStationController::Get()->GetGlobalParameters(), MultiParameter, Parameter);
//		}
//	}
//	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.ComponentLocation))
//	{
//		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 4"));
//		return false;
//	}
//	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.ComponentRotation))
//	{
//		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 5"));
//		return false;
//	}
//	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.ComponentScale))
//	{
//		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 6"));
//		return false;
//	}
//	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.SingleComponentData))
//	{
//		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 7"));
//		return false;
//	}
//	for (int32 i = 0; i < InOutMultiComponentData.ChildComponent.Num(); ++i)
//	{
//		if (Level > 0) InOutMultiComponentData.ChildComponent[i].StyleParameters = InOutMultiComponentData.StyleParameters;
//		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.ChildComponent[i], Level + 1))
//		{
//			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 8"));
//			return false;
//		}
//	}
//	{
//		FExpressionValuePair CodePair(InOutMultiComponentData.CodeExp);
//		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, CodePair))
//		{
//			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 9"));
//			return false;
//		}
//		InOutMultiComponentData.Code = CodePair.Value;
//	}
//	return true;
//}
//
//bool FGeometryDatas::CalculateAdjustmentParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, const TArray<FParameterData>& InStyleParameter, FMultiComponentDataItem& InOutMultiComponentData)
//{
//	TMap<FString, FParameterData> OverrideParameters = InParameters;
//	{
//		FParameterProcLibrary::CombineParameters(OverrideParameters, InStyleParameter);
//		const int32 IgnoreIndex = InOutMultiComponentData.ComponentParameters.IndexOfByPredicate([](const FParameterData& ParamIter)->bool {return ParamIter.Data.name.Equals(PARAM_FDZ_STR); });
//		if (IgnoreIndex != INDEX_NONE)
//			InOutMultiComponentData.ComponentParameters.RemoveAt(IgnoreIndex);
//		for (int32 i = 0; i < InOutMultiComponentData.ComponentParameters.Num(); ++i)
//		{
//			FParameterData& Parameter = InOutMultiComponentData.ComponentParameters[i];
//			if (!(Parameter.Data.name.Equals(PARAM_DEPTH) || Parameter.Data.name.Equals(PARAM_WIDTH) || Parameter.Data.name.Equals(PARAM_HEIGHT) || Parameter.Data.name.Equals(PARAM_HEIGHT1) || Parameter.Data.name.Equals(PARAM_KMLB_STR)))
//			{//对于部件的宽高深，需要单独计算
//				FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, OverrideParameters, Parameter);
//				OverrideParameters.Contains(Parameter.Data.name) ? OverrideParameters[Parameter.Data.name] = Parameter : OverrideParameters.Add(Parameter.Data.name, Parameter);
//			}
//		}
//		for (int32 i = 0; i < InOutMultiComponentData.ComponentParameters.Num(); ++i)
//		{
//			FParameterData& Parameter = InOutMultiComponentData.ComponentParameters[i];
//			if (Parameter.Data.name.Equals(PARAM_HEIGHT1) || Parameter.Data.name.Equals(PARAM_KMLB_STR))
//			{//H1变量需要使用单独的计算逻辑，H1会使用同级的H，而其他变量不会使用同级的变量; 同级变量的计算
//				TMap<FString, FParameterData> MultiParameter = OverrideParameters;
//				auto CleanCompParameters = InOutMultiComponentData.ComponentParameters;
//				CleanCompParameters.RemoveAt(i);
//				FParameterProcLibrary::CombineParameters(MultiParameter, CleanCompParameters);
//				FParameterProcLibrary::CalculateParameterExpression(ADesignStationController::Get()->GetGlobalParameters(), MultiParameter, Parameter);
//			}
//			else if (Parameter.Data.name.Equals(PARAM_DEPTH) || Parameter.Data.name.Equals(PARAM_WIDTH) || Parameter.Data.name.Equals(PARAM_HEIGHT))
//			{
//				FParameterProcLibrary::CalculateParameterExpression(ADesignStationController::Get()->GetGlobalParameters(), OverrideParameters, Parameter);
//			}
//		}
//	}
//	TMap<FString, FParameterData> MultiParameter = OverrideParameters;
//	{
//		if (InOutMultiComponentData.ComponentParameters.Num() > 0) FParameterProcLibrary::CombineParameters(MultiParameter, InOutMultiComponentData.ComponentParameters);
//		for (int32 i = 0; i < InOutMultiComponentData.StyleParameters.Num(); ++i)
//		{
//			if (false == FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, InParameters, InOutMultiComponentData.StyleParameters[i]))
//			{
//				UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 1 i=%d"), i);
//				return false;
//			}
//		}
//		if (InOutMultiComponentData.StyleParameters.Num() > 0) FParameterProcLibrary::CombineParameters(MultiParameter, InOutMultiComponentData.StyleParameters);//当前节点有风格参数使用本节点的风格参数
//	}
//	{//不可见的时候不计算其子部件用于优化数据
//		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.ComponentVisibility))
//		{
//			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 3"));
//			return false;
//		}
//		if (!InOutMultiComponentData.IsVisiable()) return true;
//	}
//	const int32 PreComponentID = FCString::Atof(*InOutMultiComponentData.ComponentID.Value);
//	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.ComponentID))
//	{
//		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 2"));
//		return false;
//	}
//	const int32 NewComponentID = FCString::Atof(*InOutMultiComponentData.ComponentID.Value);
//	InOutMultiComponentData.bComponentIDChanged = InOutMultiComponentData.bComponentIDChanged || PreComponentID != NewComponentID;
//	if (InOutMultiComponentData.bComponentIDChanged)
//	{//如果当前节点的ID改变则不再计算子节点的变量
//		InOutMultiComponentData.ComponentType = ECompType::None;
//		InOutMultiComponentData.ChildComponent.Empty();
//		InOutMultiComponentData.SingleComponentPath.Empty();
//		InOutMultiComponentData.SingleComponentData.Empty();
//		return true;
//	}
//	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.ComponentLocation))
//	{
//		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 4"));
//		return false;
//	}
//	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.ComponentRotation))
//	{
//		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 5"));
//		return false;
//	}
//	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.ComponentScale))
//	{
//		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 6"));
//		return false;
//	}
//	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.SingleComponentData))
//	{
//		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 7"));
//		return false;
//	}
//	for (int32 i = 0; i < InOutMultiComponentData.ChildComponent.Num(); ++i)
//	{
//		const auto& StyleParameters = InOutMultiComponentData.StyleParameters.Num() > 0 ? InOutMultiComponentData.StyleParameters : InStyleParameter;
//		if (false == FGeometryDatas::CalculateAdjustmentParameterValue(InGlobalParameter, MultiParameter, StyleParameters, InOutMultiComponentData.ChildComponent[i]))
//		{
//			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 8"));
//			return false;
//		}
//	}
//	{
//		FExpressionValuePair CodePair(InOutMultiComponentData.CodeExp);
//		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, CodePair))
//		{
//			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 9"));
//			return false;
//		}
//		InOutMultiComponentData.Code = CodePair.Value;
//	}
//	return true;
//}

bool FGeometryDatas::ParameterMayRefrenceConcurrentLevel(const FString& InParameterName)
{
	return InParameterName.Equals(PARAM_DEPTH) || InParameterName.Equals(PARAM_WIDTH) || InParameterName.Equals(PARAM_WIDTH2) || InParameterName.Equals(PARAM_WIDTH1) || InParameterName.
		Equals(PARAM_HEIGHT) || InParameterName.Equals(PARAM_HEIGHT1) || InParameterName.Equals(PARAM_HEIGHT2) || InParameterName.Equals(PARAM_KMLB_STR) || InParameterName.Equals(PARAM_LSAZ);
}

//bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, const TArray<FParameterData>& InStyleParameter, FMultiComponentDataItem& InOutMultiComponentData, int32 Level)
//{
//	{//Fix bug DES-1980
//		TMap<FString, FParameterData> OverrideParameters = InParameters;
//		for (int32 i = 0; i < InOutMultiComponentData.ComponentParameters.Num(); ++i)
//		{
//			FParameterData& Parameter = InOutMultiComponentData.ComponentParameters[i];
//			if (!(FGeometryDatas::ParameterMayRefrenceConcurrentLevel(Parameter.Data.name)))
//			{//对于部件的宽高深，需要单独计算
//				FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, InParameters, Parameter);
//				OverrideParameters.Contains(Parameter.Data.name) ? OverrideParameters[Parameter.Data.name] = Parameter : OverrideParameters.Add(Parameter.Data.name, Parameter);
//				if (Parameter.Data.name.Equals(TEXT("UY")) || Parameter.Data.name.Equals(TEXT("DY")) || Parameter.Data.name.Equals(TEXT("LY")) || Parameter.Data.name.Equals(TEXT("RY")))
//				{
//					TMap<FString, FParameterData> OverrideParameter;
//					FParameterProcLibrary::CombineParameters(TMap<FString, FParameterData>(), InOutMultiComponentData.ComponentParameters, OverrideParameter);
//					FExpressionValuePair Vis(Parameter.Data.visibility_exp);
//					FGeometryDatas::CalculateParameterValue(ADesignStationController::Get()->GetGlobalParameters(), OverrideParameter, Vis);
//					Parameter.Data.visibility = Vis.Value;
//				}
//			}
//		}
//		for (int32 i = 0; i < InOutMultiComponentData.ComponentParameters.Num(); ++i)
//		{
//			FParameterData& Parameter = InOutMultiComponentData.ComponentParameters[i];
//			if (Parameter.Data.name.Equals(PARAM_WIDTH2) || Parameter.Data.name.Equals(PARAM_WIDTH1) || Parameter.Data.name.Equals(PARAM_HEIGHT2) || Parameter.Data.name.Equals(PARAM_HEIGHT1) || Parameter.Data.name.Equals(PARAM_KMLB_STR) || Parameter.Data.name.Equals(PARAM_LSAZ))
//			{//H1变量需要使用单独的计算逻辑，H1会使用同级的H，而其他变量不会使用同级的变量
//				TMap<FString, FParameterData> MultiParameter = InParameters;
//				auto CleanCompParameters = InOutMultiComponentData.ComponentParameters;
//				CleanCompParameters.RemoveAt(i);
//				FParameterProcLibrary::CombineParameters(MultiParameter, CleanCompParameters);
//				FParameterProcLibrary::CalculateParameterExpression(ADesignStationController::Get()->GetGlobalParameters(), MultiParameter, Parameter);
//			}
//			else if (Parameter.Data.name.Equals(PARAM_DEPTH) || Parameter.Data.name.Equals(PARAM_WIDTH) || Parameter.Data.name.Equals(PARAM_HEIGHT))
//			{
//				FParameterProcLibrary::CalculateParameterExpression(ADesignStationController::Get()->GetGlobalParameters(), OverrideParameters, Parameter);
//			}
//		}
//	}
//	TMap<FString, FParameterData> MultiParameter = InParameters;
//	{
//		if (InOutMultiComponentData.ComponentParameters.Num() > 0) FParameterProcLibrary::CombineParameters(MultiParameter, InOutMultiComponentData.ComponentParameters);
//		if (InOutMultiComponentData.StyleParameters.Num() > 0)
//			FParameterProcLibrary::CombineParameters(MultiParameter, InOutMultiComponentData.StyleParameters);//当前节点有风格参数使用本节点的风格参数
//		else if (InStyleParameter.Num() > 0)
//			FParameterProcLibrary::CombineParameters(MultiParameter, InStyleParameter);//当前节点没有风格参数使用上一级的风格参数
//	}
//	for (int32 i = 0; i < InOutMultiComponentData.StyleParameters.Num(); ++i)
//	{
//		if (false == FParameterProcLibrary::CalculateParameterExpression(InGlobalParameter, InParameters, InOutMultiComponentData.StyleParameters[i]))
//		{
//			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 1 i=%d"), i);
//			return false;
//		}
//	}
//	{//不可见的时候不计算其子部件用于优化数据
//		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.ComponentVisibility))
//		{
//			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 3"));
//			return false;
//		}
//		if (!InOutMultiComponentData.IsVisiable()) return true;
//	}
//	const int32 PreComponentID = FCString::Atof(*InOutMultiComponentData.ComponentID.Value);
//	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.ComponentID))
//	{
//		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 2"));
//		return false;
//	}
//	const int32 NewComponentID = FCString::Atof(*InOutMultiComponentData.ComponentID.Value);
//	InOutMultiComponentData.bComponentIDChanged = InOutMultiComponentData.bComponentIDChanged || PreComponentID != NewComponentID;
//	if (InOutMultiComponentData.bComponentIDChanged)
//	{//如果当前节点的ID改变则不再计算子节点的变量
//		InOutMultiComponentData.ComponentType = ECompType::None;
//		InOutMultiComponentData.ChildComponent.Empty();
//		InOutMultiComponentData.SingleComponentPath.Empty();
//		InOutMultiComponentData.SingleComponentData.Empty();
//		return true;
//	}
//	for (int32 i = 0; (500029 == NewComponentID) && (i < InOutMultiComponentData.ComponentParameters.Num()); ++i)
//	{//门的高度需要单独计算500029是单门部件，单门部件中的H1控制门的高度
//		FParameterData& Parameter = InOutMultiComponentData.ComponentParameters[i];
//		if (Parameter.Data.name.Equals(PARAM_HEIGHT1))
//		{//对于部件的宽高深，需要单独计算
//			FParameterProcLibrary::CalculateParameterExpression(ADesignStationController::Get()->GetGlobalParameters(), MultiParameter, Parameter);
//		}
//	}
//	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.ComponentLocation))
//	{
//		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 4"));
//		return false;
//	}
//	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.ComponentRotation))
//	{
//		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 5"));
//		return false;
//	}
//	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.ComponentScale))
//	{
//		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 6"));
//		return false;
//	}
//	if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.SingleComponentData))
//	{
//		UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 7"));
//		return false;
//	}
//	for (int32 i = 0; i < InOutMultiComponentData.ChildComponent.Num(); ++i)
//	{
//		//if (Level > 0) InOutMultiComponentData.ChildComponent[i].StyleParameters = InOutMultiComponentData.StyleParameters;
//		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, InOutMultiComponentData.StyleParameters.Num() <= 0 ? InStyleParameter : InOutMultiComponentData.StyleParameters, InOutMultiComponentData.ChildComponent[i], Level + 1))
//		{
//			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 8"));
//			return false;
//		}
//	}
//	{
//		FExpressionValuePair CodePair(InOutMultiComponentData.CodeExp);
//		if (false == FGeometryDatas::CalculateParameterValue(InGlobalParameter, MultiParameter, CodePair))
//		{
//			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateParameterValue 9"));
//			return false;
//		}
//		InOutMultiComponentData.Code = CodePair.Value;
//	}
//	return true;
//}


#undef PARAM_DEPTH
#undef PARAM_WIDTH
#undef PARAM_WIDTH1
#undef PARAM_WIDTH2
#undef PARAM_HEIGHT
#undef PARAM_HEIGHT1
#undef PARAM_HEIGHT2
//#undef PARAM_KMLB
#undef PARAM_LSAZ

bool FGeometryDatas::CalculateParameterValue(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters, FMultiComponentData& InOutMultiComponentData)
{
	for (int32 i = 0; i < InOutMultiComponentData.ComponentItems.Num(); ++i)
	{
		UE_LOG(LogTemp, Log, TEXT("FGeometryDatas::CalculateParameterValue i=%d"), i);
		if (false == CalculateParameterValue(InGlobalParameter, InParameters, *InOutMultiComponentData.ComponentItems[i]))
		{
			return false;
		}
	}
	return true;
}

bool FGeometryDatas::CalculateCurMultiComponentData(const TMap<FString, FParameterData>& InGlobalParameter, const TMap<FString, FParameterData>& InParameters,
                                                    FMultiComponentDataItem& InOutMultiComponentData)
{
	{
		auto ComponentParameters = InOutMultiComponentData.ComponentParameters;
		TMap<FString, FParameterData> PeerParameters;
		FParameterProcLibrary::CombineParameters(PeerParameters, ComponentParameters);
		CalculateParameterValue_LevelSort(InGlobalParameter, InParameters, PeerParameters);
		for (const auto& iter : PeerParameters)
		{
			auto& EditParameter = iter.Value;
			const int32 Index = InOutMultiComponentData.ComponentParameters.IndexOfByPredicate([EditParameter](const FParameterData& InOther)
			{
				return InOther.Data.name.Equals(EditParameter.Data.name);
			});
			if (INDEX_NONE != Index)
			{
				InOutMultiComponentData.ComponentParameters[Index] = EditParameter;
			}
		}
	}
	TMap<FString, FParameterData> OverrideParameters;
	FParameterProcLibrary::CombineParameters(InParameters, InOutMultiComponentData.ComponentParameters, OverrideParameters);

	CalculateParameterValue(InGlobalParameter, OverrideParameters, InOutMultiComponentData.ComponentID);

	CalculateParameterValue(InGlobalParameter, OverrideParameters, InOutMultiComponentData.ComponentVisibility);

	if (InOutMultiComponentData.CodeExp.Contains(TEXT("\"")))
	{
		FExpressionValuePair CodePair(InOutMultiComponentData.CodeExp);
		CalculateParameterValue(InGlobalParameter, OverrideParameters, CodePair);
		InOutMultiComponentData.Code = CodePair.Value;
	}
	else
	{
		InOutMultiComponentData.Code = InOutMultiComponentData.CodeExp;
	}

	CalculateParameterValue(InGlobalParameter, InParameters, InOutMultiComponentData.ComponentLocation);

	CalculateParameterValue(InGlobalParameter, InParameters, InOutMultiComponentData.ComponentRotation);

	CalculateParameterValue(InGlobalParameter, InParameters, InOutMultiComponentData.ComponentScale);
	//FGeometryDatas::CalculateParameterValue(InGlobalParameter, OverrideParameters, InOutMultiComponentData.SingleComponentData);
	return true;
}

bool FGeometryDatas::GenerateMesh(FSingleComponentItem& InComponentData, FPMCSection& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramework)
{
	FCrossSectionGenerator crossSectionGenerator;
	crossSectionGenerator.setBaseCrossSection(InComponentData.OperatorSection);
	return crossSectionGenerator.generateMesh(InComponentData.SectionOperation, OutMeshInfo, OutFramework);

	//return InComponentData.OperatorSection.TransformSection(InComponentData.SectionOperation, OutMeshInfo, OutFramework);
}

bool FGeometryDatas::GenerateMesh(FSingleComponentItem& InComponentData, TArray<FShowSingleComponentActorPropertyItem>& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramework,
                                  const FString& InDMValue, bool InForceVisible)
{
	if (!InForceVisible && FMath::IsNearlyZero(FCString::Atof(*InComponentData.VisibleParam.Value)))
	{
		return true;
	}
	if (ESingleComponentSource::ECustom == InComponentData.ComponentSource)
	{
		FPMCSection MeshInfo;
		TArray<TPair<FVector, FVector>> Framework;
		if (false == GenerateMesh(InComponentData, MeshInfo, Framework))
		{
			return false;
		}
		FShowSingleComponentActorPropertyItem MeshMaterial;
		MeshMaterial.MaterialFolderID = FCString::Atoi64(*InComponentData.ComponentMaterial.Value);
		MeshInfo.SectionName = FName(*FString::FromInt(MeshMaterial.MaterialFolderID));
		MeshMaterial.MeshInfo = MoveTemp(MeshInfo);
		OutMeshInfo.Push(MeshMaterial);
		OutFramework.Append(Framework);
	}
	else if (ESingleComponentSource::EImportFBX == InComponentData.ComponentSource)
	{
		for (auto& MeshIter : InComponentData.ImportMesh)
		{
			FShowSingleComponentActorPropertyItem MeshMaterial;
			MeshMaterial.MaterialFolderID = FCString::Atoi64(*MeshIter.MaterialId.Value);
			MeshMaterial.MeshInfo = MeshIter.SectionMesh;
			MeshMaterial.MeshInfo.SectionName = FName(*FString::FromInt(MeshMaterial.MaterialFolderID));
			OutMeshInfo.Push(MeshMaterial);
		}
	}
	else if (ESingleComponentSource::EImportPAK == InComponentData.ComponentSource)
	{
		FShowSingleComponentActorPropertyItem ImportMesh;
		ImportMesh.PakRefPath = InComponentData.PakRefPath;

		if (ImportMesh.PakRefPath.Contains(TEXT(".")))
		{
			FString L;
			FString R;
			FString S = TEXT(".");
			ImportMesh.PakRefPath.Split(S, &L, &R);
			ImportMesh.PakRefPath = R;
		}

		ImportMesh.PakCode = InDMValue;
		OutMeshInfo.Push(ImportMesh);
	}
	return true;
}

bool FGeometryDatas::GenerateMesh(FSingleComponentProperty& InComponentData, TArray<FPMCSection>& OutMeshInfo, TArray<TPair<FVector, FVector>>& OutFramework, const FString& InDMValue,
                                  bool InForceVisible)
{
	if (0 == InComponentData.ComponentItems.Num())
	{
		return true;
	}
	for (auto& Iter : InComponentData.ComponentItems)
	{
		if (!InForceVisible && FMath::IsNearlyZero(FCString::Atof(*Iter.VisibleParam.Value)))
		{
			continue;
		}
		if (ESingleComponentSource::ECustom == Iter.ComponentSource)
		{
			FPMCSection MeshInfo;
			TArray<TPair<FVector, FVector>> Framework;
			if (false == GenerateMesh(Iter, MeshInfo, Framework))
			{
				return false;
			}
			FPMCSection MeshWithUV;
			UGeomtryMathmaticLibrary::GenerateMeshUV(MeshInfo, MeshWithUV);
			OutMeshInfo.Push(MeshWithUV);
			OutFramework.Append(Framework);
		}
		else if (ESingleComponentSource::EImportFBX == Iter.ComponentSource)
		{
			for (auto& MeshIter : Iter.ImportMesh)
			{
				OutMeshInfo.Push(MeshIter.SectionMesh);
			}
		}
	}
	return true;
}

bool FGeometryDatas::GenerateMesh(FMultiComponentDataItem& InComponentData, FShowMultiComponentActorProperty& OutMesh, const FString& InDMValue)
{
	OutMesh.Visibility = InComponentData.IsVisiable();
	if (false == OutMesh.Visibility)
	{
		return true;
	}
	//Transform信息错误，需要修改数据结构。
	OutMesh.MeshTransform.SetLocation(InComponentData.ComponentLocation.GetLocation());
	OutMesh.MeshTransform.SetScale3D(InComponentData.ComponentScale.GetScale());
	OutMesh.MeshTransform.SetRotation(FQuat(InComponentData.ComponentRotation.GetRotation()));
	OutMesh.ComponentID = InComponentData.GetComponentID();
	OutMesh.ComponentCode = InComponentData.Code;
	OutMesh.ModelType = InComponentData.ModelType;
	OutMesh.UUID = InComponentData.UUID;
	FString DMValue = InDMValue;
	{
		int32 DMIndex = InComponentData.ComponentParameters.IndexOfByPredicate([](const FParameterData& InOther) { return InOther.Data.name.Equals(TEXT("DM")); });
		if (INDEX_NONE != DMIndex)
		{
			DMValue = InComponentData.ComponentParameters[DMIndex].Data.value;
		}
	}
	if (ECompType::MultiCom == InComponentData.ComponentType)
	{
		int32 Offset = OutMesh.MultiComponentDatas.AddDefaulted(InComponentData.ChildComponent.Num());
		for (auto& Iter : InComponentData.ChildComponent)
		{
			if (!Iter->IsVisiable())
			{
				++Offset;
				continue;
			}
			GenerateMesh(*Iter, OutMesh.MultiComponentDatas[Offset++], DMValue);
		}
		return true;
	}
	int32 Offset = OutMesh.SingleComponentDatas.AddDefaulted();
	if (InComponentData.SingleComponentData.IsValid() && !FMath::IsNearlyZero(FCString::Atof(*InComponentData.ComponentVisibility.Value)))
	{
		for (auto& SingleMeshIter : InComponentData.SingleComponentData.ComponentItems)
		{
			if (FMath::IsNearlyZero(FCString::Atof(*SingleMeshIter.VisibleParam.Value)))
			{
				continue;
			}

			int32 i = OutMesh.SingleComponentDatas[Offset].MeshInfo.AddDefaulted();
			TArray<TPair<FVector, FVector>> Framework;
			GenerateMesh(SingleMeshIter, OutMesh.SingleComponentDatas[Offset].MeshInfo[i].SingleMeshInfo, Framework, DMValue);
			OutMesh.SingleComponentDatas[Offset].MeshInfo[i].CompSource = SingleMeshIter.ComponentSource;
			OutMesh.SingleComponentDatas[Offset].MeshInfo[i].MeshTransform.SetLocation(SingleMeshIter.SingleComponentLocation.GetLocation());
			OutMesh.SingleComponentDatas[Offset].MeshInfo[i].MeshTransform.SetRotation(FQuat(SingleMeshIter.SingleComponentRotation.GetRotation()));
			OutMesh.SingleComponentDatas[Offset].MeshInfo[i].MeshTransform.SetScale3D(SingleMeshIter.SingleComponentScale.GetScale());
			OutMesh.SingleComponentDatas[Offset].OutlinePairs.Append(Framework);
		}
	}
	return true;
}

bool FGeometryDatas::LoadMultiComponent(FMultiComponentData& InOutMultiComponent)
{
	for (auto& Iter : InOutMultiComponent.ComponentItems)
	{
		if (false == LoadMultiComponentItem(*Iter))
		{
			return false;
		}
	}
	return true;
}

bool FGeometryDatas::LoadMultiComponent(FMultiComponentData& InOutMultiComponent, const int32& InIndex)
{
	if (InOutMultiComponent.ComponentItems.IsValidIndex(InIndex))
	{
		return LoadMultiComponentItem(*InOutMultiComponent.ComponentItems[InIndex]);
	}
	return true;
}

bool FGeometryDatas::LoadMultiComponentItem(FMultiComponentDataItem& InOutMultiComponent)
{
	if (InOutMultiComponent.ComponentID.Value.IsEmpty() || ECompType::None == InOutMultiComponent.ComponentType)
	{
		InOutMultiComponent.SingleComponentData.ComponentItems.Empty();
		return true;
	}
	if (ECompType::SingleCom == InOutMultiComponent.ComponentType)
	{
		if (InOutMultiComponent.SingleComponentPath.IsEmpty())
		{
			return true;
		}
		return LoadSingleComponentByFilePath(InOutMultiComponent.SingleComponentPath, InOutMultiComponent.SingleComponentData);
	}
	if (ECompType::MultiCom == InOutMultiComponent.ComponentType)
	{
		for (auto& Iter : InOutMultiComponent.ChildComponent)
		{
			bool Res = LoadMultiComponentItem(*Iter);
			if (!Res)
			{
				return false;
			}
		}
	}
	return true;
}

bool FGeometryDatas::LoadSingleComponentByFilePath(const FString& InFilePath, FSingleComponentProperty& OutSingleComponent)
{
	OutSingleComponent.ComponentItems.Empty();
	FString SingleComponentFilePath = FPaths::ProjectContentDir() + InFilePath;
	SingleComponentFilePath = FPaths::ConvertRelativePathToFull(SingleComponentFilePath);
	if (FPaths::FileExists(SingleComponentFilePath))
	{
		return FProtobufOperatorFunctionLibrary::LoadSingleComponentFromFile(SingleComponentFilePath, OutSingleComponent);
	}
	UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::LoadSingleComponentByFilePath file %s not exist"), *SingleComponentFilePath);
	return true;
}

bool FGeometryDatas::LoadMultiComponent(FSQLiteDatabase& Database, const FString& FileID, TArray<TSharedPtr<FMultiComponentDataItem>>& MultiComponent, TArray<FSingleComponentTableData>& RefSCs)
{
	if (!ADesignStationController::Get()->IsDBValid())
	{
		return false;
	}
	bool Res = FComponentDBOperatorLibrary::SelectMultiComponent(Database, FileID, MultiComponent);
	for (int32 i = 0; Res && (i < MultiComponent.Num()); ++i)
	{
		auto& CurrentComp = MultiComponent[i];
		const int64 CompID = CurrentComp->GetComponentID();
		FLocalFileData FileData;
		bool bFound = FFolderDataLibrary::SelectFolderData(Database, CompID, FileData);
		if (false == bFound)
		{
			continue;
		}
		CurrentComp->ComponentName = FileData.folder_name;
		CurrentComp->CodeExp = FileData.folder_code_exp;
		CurrentComp->Code = FileData.folder_code;
		CurrentComp->ModelType = FileData.model_type;
		if (5 == FileData.folder_type)
		{
			//单部件
			CurrentComp->ComponentType = ECompType::SingleCom;
			FSingleComponentTableData SC;
			FComponentDBOperatorLibrary::SelectSingleComponent(Database, FileData.id, SC);
			CurrentComp->SingleComponentPath = SC.data_path;
			RefSCs.AddUnique(SC);
		}
		else if (4 == FileData.folder_type)
		{
			//多部件
			CurrentComp->ComponentType = ECompType::MultiCom;
			Res = LoadMultiComponent(Database, FileData.id, CurrentComp->ChildComponent, RefSCs) && Res;
		}
	}
	return Res;
}

void FGeometryDatas::UnloadMultiComponent(FMultiComponentData& InOutMultiComponent)
{
	for (auto& Iter : InOutMultiComponent.ComponentItems)
	{
		UnloadMultiComponentItem(*Iter);
	}
}

void FGeometryDatas::UnloadMultiComponentItem(FMultiComponentDataItem& InOutMultiComponent)
{
	InOutMultiComponent.SingleComponentData.ComponentItems.Empty();
	for (auto& Iter : InOutMultiComponent.ChildComponent)
	{
		UnloadMultiComponentItem(*Iter);
	}
}


bool FGeometryDatas::CalculateAndLoadLocalCache(FSQLiteDatabase& DB, const TMap<FString, FParameterData>& GP, const TMap<FString, FParameterData>& PP, const FString& FileID,
                                                FMultiComponentData& InOutMultiComponentData)
{
	/*if (!ADesignStationController::Get()->IsDBValid())
		return false;*/
	bool Res = FComponentDBOperatorLibrary::SelectMultiComponent(DB, FileID, InOutMultiComponentData.ComponentItems);
	for (int32 i = 0; i < InOutMultiComponentData.ComponentItems.Num(); ++i)
	{
		InOutMultiComponentData.ComponentItems[i]->ComponentID.Value = TEXT("");
	}
	Res = CalculateAndLoadLocalCache(DB, GP, PP, InOutMultiComponentData) && Res;
	return Res;
}

bool FGeometryDatas::CalculateAndLoadLocalCache(FSQLiteDatabase& DB, const TMap<FString, FParameterData>& GP, const TMap<FString, FParameterData>& PP, FMultiComponentData& InOutMultiComponentData)
{
	/*if (!ADesignStationController::Get()->IsDBValid())
		return false;*/
	bool Res = true;
	for (int32 i = 0; i < InOutMultiComponentData.ComponentItems.Num(); ++i)
	{
		//UE_LOG(LogTemp, Log, TEXT("FGeometryDatas::CalculateAndLoadLocalCache i=%d"), i);
		Res = CalculateAndLoadLocalCache(DB, GP, PP, *InOutMultiComponentData.ComponentItems[i]) && Res;
	}
	return Res;
}

bool FGeometryDatas::CalculateAndLoadLocalCache(
	FSQLiteDatabase& DB,
	const TMap<FString, FParameterData>& GP,
	const TMap<FString, FParameterData>& PP,
	FMultiComponentDataItem& InOutMultiComponentData,
	const TMap<FString, int32> PathOperatorMap /*= TMap<FString, int32>()*/,
	const FString& UpLevelPath /*= FString(TEXT(""))*/,
	bool UpComponentChange /*= false*/,
	bool UpComponentVisibility /*= true*/,
	TMap<FString, FString> ValueSizeScope /*= TMap<FString, FString>()*/
)
{
	if (!InOutMultiComponentData.IsValidForGenerate())
	{
		return true;
	}

	TMap<FString, FString> RemainVSS = ValueSizeScope;
	if (ValueSizeScope.Num() > 0 && UpComponentVisibility)
	{
		TArray<FParameterData>& CurParameter = InOutMultiComponentData.ComponentParameters;

		for (auto& VSS : ValueSizeScope)
		{
			if (!VSS.Value.Contains(TEXT("/")))
			{
				continue;
			}
			FString ValueName = VSS.Key;
			int32 ValueIndex = CurParameter.IndexOfByPredicate(
				[ValueName](const FParameterData& PD)-> bool { return PD.Data.IsVisible() && PD.Data.name.Equals(ValueName, ESearchCase::IgnoreCase); }
			);
			if (ValueIndex != INDEX_NONE)
			{
				/*bool Min_Value_Empty = CurParameter[ValueIndex].Data.min_value.IsEmpty();
				bool Min_Expression_Empty = CurParameter[ValueIndex].Data.min_expression.IsEmpty();
				bool Max_Value_Empty = CurParameter[ValueIndex].Data.max_value.IsEmpty();
				bool Max_Expression_Empty = CurParameter[ValueIndex].Data.max_expression.IsEmpty();
				if (Min_Value_Empty && Min_Expression_Empty && Max_Value_Empty && Max_Expression_Empty)*/
				{
					FString MinValue, MaxValue;
					VSS.Value.Split(TEXT("/"), &MinValue, &MaxValue);

					CurParameter[ValueIndex].Data.max_value = MaxValue;
					CurParameter[ValueIndex].Data.max_expression = MaxValue;
					CurParameter[ValueIndex].Data.min_value = MinValue;
					CurParameter[ValueIndex].Data.min_expression = MinValue;
				}
				RemainVSS.Remove(VSS.Key);
			}
		}
	}

	TMap<FString, FParameterData> ComponentParametersMap;
	{
		//合并引用变量与风格变量
		FParameterProcLibrary::CombineParameters(ComponentParametersMap, InOutMultiComponentData.ComponentParameters);
		for (auto& Parameter : InOutMultiComponentData.StyleParameters)
		{
			if (ComponentParametersMap.Contains(Parameter.Data.name))
			{
				ComponentParametersMap[Parameter.Data.name] = Parameter;
			}
			else
			{
				ComponentParametersMap.Add(Parameter.Data.name, Parameter);
			}
		}
	}

	{
		//解析本级变量的引用关系，依据引用关系重新计算各变量的值
		TArray<FParameterData> WithStyleParameters;
		ComponentParametersMap.GenerateValueArray(WithStyleParameters);
		ComponentParametersMap.Empty();
		FParameterRefrenceParser Parser;
		bool Res = Parser.SortParameterByRefrence(WithStyleParameters);
		auto SortedParameters = Res ? Parser.GetSortedParameters() : Parser.GetOriginalParameters();
		FParameterProcLibrary::CombineParameters(ComponentParametersMap, SortedParameters);

		for (int32 i = 0; i < SortedParameters.Num(); ++i)
		{
			auto& EditParameter = SortedParameters[i];
			ComponentParametersMap.Remove(EditParameter.Data.name);
			TMap<FString, FParameterData> OverrideParameters;
			FParameterProcLibrary::CombineParameters(PP, ComponentParametersMap, OverrideParameters);

			//if (EditParameter.Data.name.Equals(PARAM_GDFD_STR))
			//{//特殊处理GDFD参数
			//	if (!EditParameter.Data.editable_exp.IsNumeric())
			//	{
			//		FParameterProcLibrary::CalculateExpression(GP, OverrideParameters, EditParameter.Data.editable_exp, EditParameter.Data.editable);
			//	}
			//	if (FCString::Atoi(*EditParameter.Data.editable) > 0)
			//	{
			//		ComponentParametersMap.Add(EditParameter.Data.name, EditParameter);
			//		continue;
			//	}
			//}

			FParameterProcLibrary::CalculateParameterExpression(GP, OverrideParameters, EditParameter);
			ComponentParametersMap.Add(EditParameter.Data.name, EditParameter);
			const int32 Index = InOutMultiComponentData.ComponentParameters.IndexOfByPredicate([EditParameter](const FParameterData& InOther)
			{
				return InOther.Data.name.Equals(EditParameter.Data.name);
			});
			if (INDEX_NONE != Index)
			{
				InOutMultiComponentData.ComponentParameters[Index] = EditParameter;
			}
		}
	}

	TMap<FString, FParameterData> OverrideParameters;
	FParameterProcLibrary::CombineParameters(PP, ComponentParametersMap, OverrideParameters);
	if (false == CalculateParameterValue(GP, OverrideParameters, InOutMultiComponentData.ComponentVisibility))
	{
		return false;
	}
	//if (false == InOutMultiComponentData.IsVisiable()) return true;//当前引用不显示则不计算，以加快计算速度。

	{
		//计算ID引用是否发生改变
		const int64 PreComponentID = FCString::Atoi64(*InOutMultiComponentData.ComponentID.Value);
		if (false == CalculateParameterValue(GP, OverrideParameters, InOutMultiComponentData.ComponentID))
		{
			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateAndLoadLocalCache 2"));
			return false;
		}
		const int64 NewComponentID = FCString::Atoi64(*InOutMultiComponentData.ComponentID.Value);

		bool CurComponentChange = (PreComponentID != NewComponentID);
		UpComponentChange = CurComponentChange || UpComponentChange;
		UpComponentVisibility = UpComponentVisibility && InOutMultiComponentData.IsVisiable();

		{
			InOutMultiComponentData.ComponentType = ECompType::None;

			FLocalFileData NewFile;
			FFolderDataLibrary::SelectFolderData(DB, NewComponentID, NewFile);
			InOutMultiComponentData.ComponentName = NewFile.folder_name;
			InOutMultiComponentData.CodeExp = NewFile.folder_code_exp;
			InOutMultiComponentData.Code = NewFile.folder_code;
			InOutMultiComponentData.ModelType = NewFile.model_type;
			EFolderType FileType = static_cast<EFolderType>(NewFile.folder_type);
			if (EFolderType::ESingleComponent == FileType)
			{
				InOutMultiComponentData.SingleComponentPath.Empty();
				InOutMultiComponentData.SingleComponentData.Empty();
				InOutMultiComponentData.ChildComponent.Empty();

				InOutMultiComponentData.ComponentType = ECompType::SingleCom;
				FSingleComponentTableData SingleComp;
				FComponentDBOperatorLibrary::SelectSingleComponent(DB, NewFile.id, SingleComp);
				InOutMultiComponentData.SingleComponentPath = SingleComp.data_path;
			}
			else if (EFolderType::EMultiComponents == FileType)
			{
				InOutMultiComponentData.ComponentType = ECompType::MultiCom;
				TArray<TSharedPtr<FMultiComponentDataItem>> NewChildren;
				FComponentDBOperatorLibrary::SelectMultiComponent(DB, NewFile.id, NewChildren);

				//操作数据还原
				TArray<int32> Delete_Index;
				if (PathOperatorMap.Num() > 0)
				{
					int32 Cur_Index = 0;
					for (const auto& PO : PathOperatorMap)
					{
						if (PO.Value == 2) //删除 EOperatorType
						{
							if (/*UpLevelPath.IsEmpty() && */PO.Key.Len() == 2) //第一层
							{
								bool IsSameLevel = IsPathSameLevel(PathOperatorMap, PO.Key);
								if (IsSameLevel)
								{
									Delete_Index.AddUnique(FCString::Atoi(*(PO.Key.Right(2))));
								}
								else if (PO.Key.Equals(UpLevelPath, ESearchCase::IgnoreCase))
								{
									Delete_Index.AddUnique(FCString::Atoi(*(PO.Key.Right(2))));
								}
							}
							else //其他层级
							{
								FString PO_Up_Level = PO.Key.Left(PO.Key.Len() - 2);
								if (PO_Up_Level.Equals(UpLevelPath, ESearchCase::IgnoreCase))
								{
									int32 Index = FCString::Atoi(*(PO.Key.Right(2)));
									Delete_Index.AddUnique(Index);
								}
							}
						}
						++Cur_Index;
					}
				}
				if (Delete_Index.Num() > 0)
				{
					Delete_Index.Sort([](const int32& A, const int32& B)-> bool { return A > B; });
					for (auto& Index : Delete_Index)
					{
						if (NewChildren.IsValidIndex(Index))
						{
							NewChildren[Index]->SetItemOnlyPlacehold();
						}
					}
				}

				//confirm placehold
				TArray<int32> PlaceHoldIndex;
				for (int32 i = 0; i < InOutMultiComponentData.ChildComponent.Num(); ++i)
				{
					if (!InOutMultiComponentData.ChildComponent[i]->IsValidForGenerate())
					{
						PlaceHoldIndex.Add(i);
					}
				}
				for (auto& PHI : PlaceHoldIndex)
				{
					if (NewChildren.IsValidIndex(PHI))
					{
						NewChildren[PHI]->SetItemOnlyPlacehold();
					}
				}

				if (/*InOutMultiComponentData.ChildComponent.Num() == 0 && */NewChildren.Num() != InOutMultiComponentData.ChildComponent.Num())
				{
					InOutMultiComponentData.ChildComponent = NewChildren;
				}
				else
				{
					for (int32 i = InOutMultiComponentData.ChildComponent.Num() - 1; i >= 0; --i)
					{
						if (!NewChildren.IsValidIndex(i))
						{
							InOutMultiComponentData.ChildComponent.RemoveAt(i);
							continue;
						}

						if (!UpComponentChange) //当此路径上的组件改变后，不再跟新参数
						{
							TArray<FParameterData> TempParams = InOutMultiComponentData.ChildComponent[i]->ComponentParameters;
							for (int32 j = 0; j < TempParams.Num(); ++j)
							{
								FString CurName = TempParams[j].Data.name;
								int32 Index = NewChildren[i]->ComponentParameters.IndexOfByPredicate(
									[CurName](const FParameterData& InData)-> bool { return CurName.Equals(InData.Data.name, ESearchCase::IgnoreCase); }
								);
								if (Index != INDEX_NONE)
								{
									NewChildren[i]->ComponentParameters[Index].Data.value = TempParams[j].Data.value;
									if (!CurName.Equals(PARAM_GDFD_STR))
									{
										NewChildren[i]->ComponentParameters[Index].Data.expression = TempParams[j].Data.expression;
									}
									else if (NewChildren[i]->ComponentParameters[Index].Data.expression.IsNumeric())
									{
										NewChildren[i]->ComponentParameters[Index].Data.expression = TempParams[j].Data.expression;
									}
								}
							}
						}

						InOutMultiComponentData.ChildComponent[i]->ComponentParameters = NewChildren[i]->ComponentParameters;
						InOutMultiComponentData.ChildComponent[i]->ComponentID = NewChildren[i]->ComponentID;
						InOutMultiComponentData.ChildComponent[i]->ComponentLocation = NewChildren[i]->ComponentLocation;
						InOutMultiComponentData.ChildComponent[i]->ComponentRotation = NewChildren[i]->ComponentRotation;
						InOutMultiComponentData.ChildComponent[i]->ComponentScale = NewChildren[i]->ComponentScale;
						InOutMultiComponentData.ChildComponent[i]->ComponentVisibility = NewChildren[i]->ComponentVisibility;
						InOutMultiComponentData.ChildComponent[i]->Description = NewChildren[i]->Description;
					}
				}
			}
		}
	}
	{
		FExpressionValuePair CodePair(InOutMultiComponentData.CodeExp);
		CalculateParameterValue(GP, OverrideParameters, CodePair);
		InOutMultiComponentData.Code = CodePair.Value;
	}
	if (false == CalculateParameterValue(GP, PP, InOutMultiComponentData.ComponentLocation))
	{
		return false;
	}
	if (false == CalculateParameterValue(GP, PP, InOutMultiComponentData.ComponentRotation))
	{
		return false;
	}
	if (false == CalculateParameterValue(GP, PP, InOutMultiComponentData.ComponentScale))
	{
		return false;
	}
	if (false == CalculateParameterValue(GP, OverrideParameters, InOutMultiComponentData.SingleComponentData))
	{
		return false;
	}
	if (ECompType::SingleCom == InOutMultiComponentData.ComponentType)
	{
		LoadSingleComponentByFilePath(InOutMultiComponentData.SingleComponentPath, InOutMultiComponentData.SingleComponentData);
		CalculateParameterValue(GP, OverrideParameters, InOutMultiComponentData.SingleComponentData);
	}
	else if (ECompType::MultiCom == InOutMultiComponentData.ComponentType)
	{
		for (int32 i = 0; i < InOutMultiComponentData.ChildComponent.Num(); ++i)
		{
			FString CurPath = FString::Printf(TEXT("%s%02d"), *UpLevelPath, i);
			if (false == CalculateAndLoadLocalCache(
					DB,
					GP,
					OverrideParameters,
					*InOutMultiComponentData.ChildComponent[i],
					PathOperatorMap,
					CurPath,
					UpComponentChange,
					UpComponentVisibility,
					RemainVSS
				)
			)
			{
				return false;
			}
		}
	}
	return true;
}

bool FGeometryDatas::CalculateAndLoadLocalCache_NotLevelSearch(
	FSQLiteDatabase& DB,
	const TMap<FString, FParameterData>& GP,
	const TMap<FString, FParameterData>& PP,
	FMultiComponentDataItem& InOutMultiComponentData,
	bool NeedResearch,
	bool UpVisibility /*= true*/,
	TMap<FString, FString> ValueSizeScope /*= TMap<FString, FString>()*/
)
{
	if (!InOutMultiComponentData.IsValidForGenerate())
	{
		return true;
	}

	TMap<FString, FString> RemainVSS = ValueSizeScope;
	if (ValueSizeScope.Num() > 0 && UpVisibility)
	{
		TArray<FParameterData>& CurParameter = InOutMultiComponentData.ComponentParameters;

		for (auto& VSS : ValueSizeScope)
		{
			if (!VSS.Value.Contains(TEXT("/")))
			{
				continue;
			}
			int32 ValueIndex = CurParameter.IndexOfByPredicate(
				[VSS](const FParameterData& PD)-> bool { return PD.Data.IsVisible() && PD.Data.name.Equals(VSS.Key, ESearchCase::IgnoreCase); }
			);
			if (ValueIndex != INDEX_NONE)
			{
				/*bool Min_Value_Empty = CurParameter[ValueIndex].Data.min_value.IsEmpty();
				bool Min_Expression_Empty = CurParameter[ValueIndex].Data.min_expression.IsEmpty();
				bool Max_Value_Empty = CurParameter[ValueIndex].Data.max_value.IsEmpty();
				bool Max_Expression_Empty = CurParameter[ValueIndex].Data.max_expression.IsEmpty();
				if (Min_Value_Empty && Min_Expression_Empty && Max_Value_Empty && Max_Expression_Empty)*/
				{
					FString MinValue, MaxValue;
					VSS.Value.Split(TEXT("/"), &MinValue, &MaxValue);

					CurParameter[ValueIndex].Data.max_value = MaxValue;
					CurParameter[ValueIndex].Data.max_expression = MaxValue;
					CurParameter[ValueIndex].Data.min_value = MinValue;
					CurParameter[ValueIndex].Data.min_expression = MinValue;
				}
				RemainVSS.Remove(VSS.Key);
			}
		}
	}

	TMap<FString, FParameterData> ComponentParametersMap;
	{
		//合并引用变量与风格变量
		FParameterProcLibrary::CombineParameters(ComponentParametersMap, InOutMultiComponentData.ComponentParameters);
		for (auto& Parameter : InOutMultiComponentData.StyleParameters)
		{
			if (ComponentParametersMap.Contains(Parameter.Data.name))
			{
				ComponentParametersMap[Parameter.Data.name] = Parameter;
			}
			else
			{
				ComponentParametersMap.Add(Parameter.Data.name, Parameter);
			}
		}
	}

	{
		//解析本级变量的引用关系，依据引用关系重新计算各变量的值
		TArray<FParameterData> WithStyleParameters;
		ComponentParametersMap.GenerateValueArray(WithStyleParameters);
		ComponentParametersMap.Empty();
		FParameterRefrenceParser Parser;
		bool Res = Parser.SortParameterByRefrence(WithStyleParameters);
		auto SortedParameters = Res ? Parser.GetSortedParameters() : Parser.GetOriginalParameters();
		FParameterProcLibrary::CombineParameters(ComponentParametersMap, SortedParameters);

		for (int32 i = 0; i < SortedParameters.Num(); ++i)
		{
			auto& EditParameter = SortedParameters[i];
			ComponentParametersMap.Remove(EditParameter.Data.name);
			TMap<FString, FParameterData> OverrideParameters;
			FParameterProcLibrary::CombineParameters(PP, ComponentParametersMap, OverrideParameters);

			//if (EditParameter.Data.name.Equals(PARAM_GDFD_STR))
			//{//特殊处理GDFD参数
			//	if (!EditParameter.Data.editable_exp.IsNumeric())
			//	{
			//		FParameterProcLibrary::CalculateExpression(GP, OverrideParameters, EditParameter.Data.editable_exp, EditParameter.Data.editable);
			//	}
			//	if (FCString::Atoi(*EditParameter.Data.editable) > 0)
			//	{
			//		ComponentParametersMap.Add(EditParameter.Data.name, EditParameter);
			//		continue;
			//	}
			//}

			FParameterProcLibrary::CalculateParameterExpression(GP, OverrideParameters, EditParameter);
			ComponentParametersMap.Add(EditParameter.Data.name, EditParameter);
			const int32 Index = InOutMultiComponentData.ComponentParameters.IndexOfByPredicate([EditParameter](const FParameterData& InOther)
			{
				return InOther.Data.name.Equals(EditParameter.Data.name);
			});
			if (INDEX_NONE != Index)
			{
				InOutMultiComponentData.ComponentParameters[Index] = EditParameter;
			}
		}
	}

	TMap<FString, FParameterData> OverrideParameters;
	FParameterProcLibrary::CombineParameters(PP, ComponentParametersMap, OverrideParameters);
	if (false == CalculateParameterValue(GP, OverrideParameters, InOutMultiComponentData.ComponentVisibility))
	{
		return false;
	}
	//if (false == InOutMultiComponentData.IsVisiable()) return true;//当前引用不显示则不计算，以加快计算速度。

	{
		//计算ID引用是否发生改变
		const int64 PreComponentID = FCString::Atoi64(*InOutMultiComponentData.ComponentID.Value);
		if (false == CalculateParameterValue(GP, OverrideParameters, InOutMultiComponentData.ComponentID))
		{
			UE_LOG(LogTemp, Error, TEXT("FGeometryDatas::CalculateAndLoadLocalCache 2"));
			return false;
		}
		const int64 NewComponentID = FCString::Atoi64(*InOutMultiComponentData.ComponentID.Value);

		UpVisibility = UpVisibility && InOutMultiComponentData.IsVisiable();
		bool CurComponentChange = (PreComponentID != NewComponentID);
		NeedResearch = NeedResearch || CurComponentChange;
		if (NeedResearch)
		{
			InOutMultiComponentData.ComponentType = ECompType::None;

			FLocalFileData NewFile;
			FFolderDataLibrary::SelectFolderData(DB, NewComponentID, NewFile);
			InOutMultiComponentData.ComponentName = NewFile.folder_name;
			InOutMultiComponentData.CodeExp = NewFile.folder_code_exp;
			InOutMultiComponentData.Code = NewFile.folder_code;
			InOutMultiComponentData.ModelType = NewFile.model_type;
			EFolderType FileType = static_cast<EFolderType>(NewFile.folder_type);
			if (EFolderType::ESingleComponent == FileType)
			{
				InOutMultiComponentData.SingleComponentPath.Empty();
				InOutMultiComponentData.SingleComponentData.Empty();
				InOutMultiComponentData.ChildComponent.Empty();

				InOutMultiComponentData.ComponentType = ECompType::SingleCom;
				FSingleComponentTableData SingleComp;
				FComponentDBOperatorLibrary::SelectSingleComponent(DB, NewFile.id, SingleComp);
				InOutMultiComponentData.SingleComponentPath = SingleComp.data_path;
			}
			else if (EFolderType::EMultiComponents == FileType)
			{
				InOutMultiComponentData.ComponentType = ECompType::MultiCom;
				TArray<TSharedPtr<FMultiComponentDataItem>> NewChildren;
				FComponentDBOperatorLibrary::SelectMultiComponent(DB, NewFile.id, NewChildren);

				InOutMultiComponentData.ChildComponent = NewChildren;
			}
		}
	}
	{
		FExpressionValuePair CodePair(InOutMultiComponentData.CodeExp);
		CalculateParameterValue(GP, OverrideParameters, CodePair);
		InOutMultiComponentData.Code = CodePair.Value;
	}
	if (false == CalculateParameterValue(GP, PP, InOutMultiComponentData.ComponentLocation))
	{
		return false;
	}
	if (false == CalculateParameterValue(GP, PP, InOutMultiComponentData.ComponentRotation))
	{
		return false;
	}
	if (false == CalculateParameterValue(GP, PP, InOutMultiComponentData.ComponentScale))
	{
		return false;
	}
	if (false == CalculateParameterValue(GP, OverrideParameters, InOutMultiComponentData.SingleComponentData))
	{
		return false;
	}
	if (ECompType::SingleCom == InOutMultiComponentData.ComponentType)
	{
		LoadSingleComponentByFilePath(InOutMultiComponentData.SingleComponentPath, InOutMultiComponentData.SingleComponentData);
		CalculateParameterValue(GP, OverrideParameters, InOutMultiComponentData.SingleComponentData);
	}
	else if (ECompType::MultiCom == InOutMultiComponentData.ComponentType)
	{
		for (int32 i = 0; i < InOutMultiComponentData.ChildComponent.Num(); ++i)
		{
			if (false == CalculateAndLoadLocalCache_NotLevelSearch(
					DB,
					GP,
					OverrideParameters,
					*InOutMultiComponentData.ChildComponent[i],
					NeedResearch,
					UpVisibility,
					RemainVSS
				)
			)
			{
				return false;
			}
		}
	}
	return true;
}

void FGeometryDatas::CalculateAndLoadLocalCache_ForCache(TSharedPtr<FSQLiteDatabase> DB, const TMap<FString, FParameterData>& GP, const TMap<FString, FParameterData>& PP,
                                                         FMultiComponentData& InOutMultiComponentData)
{
	for (auto& Data : InOutMultiComponentData.ComponentItems)
	{
		CalculateAndLoadLocalCache(*DB, GP, PP, *Data);
	}
}

bool FGeometryDatas::GenerateSinlgeMeshs(FMultiComponentDataItem& InComponentData, TArray<FShowSingleComponentActorProperty>& OutMesh, const FTransform& ParentNodeTrans, const FString& InDMValue)
{
	if (false == InComponentData.IsVisiable())
	{
		return true;
	}
	//Transform信息错误，需要修改数据结构。
	const FTransform CurrentNodeLocalTrans(InComponentData.ComponentRotation.GetRotation(), InComponentData.ComponentLocation.GetLocation(), InComponentData.ComponentScale.GetScale());
	const FTransform CurrentNodeTrans = CurrentNodeLocalTrans * ParentNodeTrans;
	FString DMValue = InDMValue;
	{
		int32 DMIndex = InComponentData.ComponentParameters.IndexOfByPredicate([](const FParameterData& InOther) { return InOther.Data.name.Equals(TEXT("DM")); });
		if (INDEX_NONE != DMIndex)
		{
			DMValue = InComponentData.ComponentParameters[DMIndex].Data.value;
		}
	}
	if (ECompType::MultiCom == InComponentData.ComponentType)
	{
		for (auto& Iter : InComponentData.ChildComponent)
		{
			GenerateSinlgeMeshs(*Iter, OutMesh, CurrentNodeTrans, DMValue);
		}
		return true;
	}
	if (InComponentData.SingleComponentData.IsValid() && !FMath::IsNearlyZero(FCString::Atof(*InComponentData.ComponentVisibility.Value)))
	{
		int32 Offset = OutMesh.AddDefaulted();
		auto& NewSingleComp = OutMesh[Offset];
		NewSingleComp.MeshTransform = CurrentNodeTrans;
		for (auto& SingleMeshIter : InComponentData.SingleComponentData.ComponentItems)
		{
			if (FMath::IsNearlyZero(FCString::Atof(*SingleMeshIter.VisibleParam.Value)))
			{
				continue;
			}

			int32 i = OutMesh[Offset].MeshInfo.AddDefaulted();
			TArray<TPair<FVector, FVector>> Framework;
			GenerateMesh(SingleMeshIter, NewSingleComp.MeshInfo[i].SingleMeshInfo, Framework, DMValue);
			NewSingleComp.MeshInfo[i].CompSource = SingleMeshIter.ComponentSource;
			NewSingleComp.MeshInfo[i].MeshTransform.SetLocation(SingleMeshIter.SingleComponentLocation.GetLocation());
			NewSingleComp.MeshInfo[i].MeshTransform.SetRotation(FQuat(SingleMeshIter.SingleComponentRotation.GetRotation()));
			NewSingleComp.MeshInfo[i].MeshTransform.SetScale3D(SingleMeshIter.SingleComponentScale.GetScale());
			NewSingleComp.OutlinePairs.Append(Framework);
		}
	}
	return true;
}

bool FGeometryDatas::CalculateBaseParam(const TMap<FString, FParameterData>& GP, TMap<FString, FParameterData>& PP)
{
	TMap<FString, FParameterData> ComponentParametersMap;
	FParameterProcLibrary::CombineParameters(ComponentParametersMap, PP);
	TArray<FParameterData> PeerParameters;
	ComponentParametersMap.GenerateValueArray(PeerParameters);
	FParameterRefrenceParser Parser;
	bool Res = Parser.SortParameterByRefrence(PeerParameters);
	auto SortedParameters = Res ? Parser.GetSortedParameters() : Parser.GetOriginalParameters();
	TMap<FString, FParameterData> OverrideParameters;
	FParameterProcLibrary::CombineParameters(PP, ComponentParametersMap, OverrideParameters);
	for (int32 i = 0; i < SortedParameters.Num(); ++i)
	{
		auto& EditParameter = SortedParameters[i];
		//if (EditParameter.Data.expression.IsNumeric())
		//	continue;
		ComponentParametersMap.Remove(EditParameter.Data.name);

		FParameterProcLibrary::CalculateParameterExpression(GP, OverrideParameters, EditParameter);
		if (ComponentParametersMap.Contains(EditParameter.Data.name))
		{
			ComponentParametersMap[EditParameter.Data.name] = EditParameter;
		}
		else
		{
			ComponentParametersMap.Add(EditParameter.Data.name, EditParameter);
		}
	}
	for (auto& iter : PP)
	{
		if (ComponentParametersMap.Contains(iter.Key))
		{
			iter.Value.Data = ComponentParametersMap[iter.Key].Data;
		}
	}
	return true;
}

bool FGeometryDatas::NeedCalculate(const FParameterData& Param)
{
	bool Res = !Param.Data.expression.IsEmpty() && !Param.Data.expression.IsNumeric();
	if (Res)
	{
		return true;
	}

	Res = !Param.Data.max_expression.IsEmpty() && !Param.Data.max_expression.IsNumeric();
	if (Res)
	{
		return true;
	}

	Res = !Param.Data.min_expression.IsEmpty() && !Param.Data.min_expression.IsNumeric();
	if (Res)
	{
		return true;
	}

	Res = !Param.Data.visibility_exp.IsEmpty() && !Param.Data.visibility_exp.IsNumeric();
	if (Res)
	{
		return true;
	}

	Res = !Param.Data.editable_exp.IsEmpty() && !Param.Data.editable_exp.IsNumeric();
	if (Res)
	{
		return true;
	}

	for (auto& Enum : Param.EnumData)
	{
		Res = !Enum.expression.IsEmpty() && !Enum.expression.IsNumeric();
		if (Res)
		{
			return true;
		}

		Res = !Enum.visibility_exp.IsEmpty() && !Enum.visibility_exp.IsNumeric();
		if (Res)
		{
			return true;
		}
	}

	return false;
}

bool FGeometryDatas::IsPathSameLevel(TMap<FString, int32> PathOperatorMap, const FString& DelePOKey)
{
	PathOperatorMap.Remove(DelePOKey);
	for (auto& PO : PathOperatorMap)
	{
		if (PO.Key.Len() != DelePOKey.Len())
		{
			return false;
		}
	}
	return true;
}

#ifdef WITH_EDITOR
#pragma optimize("", on)
#endif
